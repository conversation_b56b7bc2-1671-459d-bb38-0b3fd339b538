let pdfjsModule: typeof import('pdfjs-dist') | undefined
export async function preparePdfjs() {
  if (pdfjsModule) return pdfjsModule
  // @ts-expect-error: import mjs
  pdfjsModule = await import('pdfjs-dist/webpack.mjs')
  return pdfjsModule!
}

export async function getFirstPageImage(
  pdfBlob: Blob,
  options: {
    scale?: number
  } = {},
): Promise<string> {
  const { scale = 1.0 } = options
  const pdfjs = await preparePdfjs()
  const pdfData = await pdfBlob.arrayBuffer()
  const pdfDoc = await pdfjs.getDocument({ data: pdfData }).promise
  const page = await pdfDoc.getPage(1)

  const viewport = page.getViewport({ scale }) // You can adjust scale
  const canvas = document.createElement('canvas')
  const context = canvas.getContext('2d')!
  canvas.width = viewport.width
  canvas.height = viewport.height

  await page.render({ canvasContext: context, viewport }).promise

  return canvas.toDataURL('image/jpeg', 0.8)
}

export function downloadPdf(
  pdf: ArrayBuffer,
  filename: string = 'document.pdf',
): void {
  const pdfBlob = new Blob([pdf], { type: 'application/pdf' })
  const url = URL.createObjectURL(pdfBlob)
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
}
