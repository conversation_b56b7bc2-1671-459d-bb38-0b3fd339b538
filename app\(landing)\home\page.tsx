'use client'

import { ReactNode, useEffect } from 'react'
import Join from './_components/join'
import Editor from './_components/editor'
import AboutPage from './_components/about'
import dynamic from 'next/dynamic'
import AOS from 'aos'
import 'aos/dist/aos.css'
import { PROJECT_CODE } from '@/lib/constant'

const Intro = dynamic(() => import('./_components/intro'), {
  ssr: false,
})
interface ScrollToTopProps {
  children: ReactNode
}

const ScrollToTop = (props: ScrollToTopProps) => {
  useEffect(() => {
    window.scrollTo({ top: 0, left: 0, behavior: 'smooth' })
  }, [])

  return <>{props.children}</>
}

const HomePage = () => {
  // 初始化 AOS
  useEffect(() => {
    AOS.init({
      duration: 1500,
      once: true,
      easing: 'ease',
      delay: 400,
      mirror: false,
      anchorPlacement: 'top-center',
      offset: 200,
    })
  }, [])

  return (
    <ScrollToTop>
      <div className="flex flex-col min-h-screen relative overflow-hidden">
        <section className="w-full" id="intro-section">
          <Intro />
        </section>
        <section
          className="w-full"
          id="about-section"
          data-aos="zoom-in-down"
          data-aos-delay="200"
        >
          <AboutPage />
        </section>
        <section
          className="w-full"
          id="editor-section"
          data-aos="zoom-in-down"
          data-aos-delay="200"
        >
          <Editor />
        </section>
        {PROJECT_CODE === 'ArXtect' && (
          <section
            className="w-full"
            id="join-section"
            data-aos="zoom-in-down"
            data-aos-delay="200"
          >
            <Join />
          </section>
        )}
      </div>
    </ScrollToTop>
  )
}

export default HomePage
