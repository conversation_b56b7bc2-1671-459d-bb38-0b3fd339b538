import { useEffect, useState } from 'react'
import { YProject } from '../y-project'
import { AssetNode, TextNode } from '../y-project/file-index'

export type FolderNodeObject = {
  [key: string]: FolderNodeObject | TextNode | AssetNode
}
/**
 * useYIndex
 * Provides observation and updates for the FileIndex of a YProject.
 */
export function useYIndex(yproject: YProject) {
  const [index, setIndex] = useState<FolderNodeObject>()
  useEffect(() => {
    const projectIndex = yproject.fileIndex
    const onChangeFileIndex = () => {
      setIndex(projectIndex.rootFolder.toJSON())
    }
    onChangeFileIndex()
    projectIndex.rootFolder.observeDeep(onChangeFileIndex)
    return () => {
      projectIndex.rootFolder.unobserveDeep(onChangeFileIndex)
    }
  }, [yproject])
  return index
}
