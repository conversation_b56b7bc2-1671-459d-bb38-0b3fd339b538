import { Input } from '@/components/ui/input'
import { useYProjectContext } from '@/lib/y-adapter/use-yproject-context'
import { setExplorerStore } from '@/stores/explorer-store'
import { useEffect, useRef, useState } from 'react'
import { toast } from 'sonner'

export default function InputTitle({
  defaultValue = '',
}: {
  defaultValue?: string
}) {
  const { yproject } = useYProjectContext()
  const [value, setValue] = useState(defaultValue)
  const ref = useRef<HTMLInputElement>(null)

  useEffect(() => {
    ref.current?.focus()
  }, [])

  return (
    <Input
      className="h-6"
      ref={ref}
      value={value}
      onChange={e => setValue(e.target.value)}
      onBlur={() => {
        setExplorerStore(state => {
          if (state.addingPath) {
            if (!value) {
              state.addingPath = undefined
              return
            }

            if (value.includes('/')) {
              toast.error('File or folder name cannot contain "/"')
              return
            }

            const [path, type] = state.addingPath
            if (type === 'file') {
              yproject.fileHandler.createTextFile(`${path}/${value}`, '')
            } else if (type === 'folder') {
              yproject.fileHandler.createFolder(`${path}/${value}`)
            }

            state.addingPath = undefined
            return
          } else if (state.renamingPath) {
            if (!value) {
              toast.error('Name cannot be empty')
              return
            }

            if (value.includes('/')) {
              toast.error('File or folder name cannot contain "/"')
              return
            }

            const oldPath = state.renamingPath
            const newPath =
              oldPath.substring(0, oldPath.lastIndexOf('/') + 1) + value

            if (newPath !== oldPath && yproject.fileIndex.getNode(newPath)) {
              toast.error('File or folder already exists')
              return
            }
            yproject.fileHandler.move(oldPath, newPath)

            state.renamingPath = undefined
          } else {
            throw new Error(
              'InputTitle were rendered without addingPath or renamingPath',
            )
          }
        })
      }}
    />
  )
}
