import { ComponentProps } from 'react'
import Image from 'next/image'
import { getSize, IconSize } from './icon-sizes'

export default function StatusSuccess(
  props: Partial<ComponentProps<typeof Image>> & {
    size?: IconSize | [number, number]
  },
) {
  const [width, height] = Array.isArray(props.size)
    ? props.size
    : getSize(props.size)
  return (
    <Image
      src="/assets/layout/status-success.svg"
      alt="StatusSuccess"
      width={width}
      height={height}
      {...props}
    />
  )
}
