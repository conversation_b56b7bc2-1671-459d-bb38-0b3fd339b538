// Outdated: need to be updated
// import { promises as nodeFs } from 'fs'
// import { beforeAll, describe, it } from 'vitest'
// import puppeteer from 'puppeteer'
// import type { Browser } from 'puppeteer'
// import { launchServer } from './test-server/server'
// import path from 'path'
// import { readExampleProject } from './utils/read-project'
// import { getFoldersToCreate } from '@/lib/compile'

// describe(
//   'test latex compile',
//   {
//     timeout: 600 * 1000,
//   },
//   async () => {
//     let browser: Browser
//     beforeAll(async () => {
//       launchServer()
//       browser = await puppeteer.launch()
//     })

//     it('should compile a latex file', async () => {
//       const compileProject = 'basic'
//       const workspace = await readExampleProject(compileProject)
//       const page = await browser.newPage()
//       page.exposeFunction('getFoldersToCreate', getFoldersToCreate)
//       page.exposeFunction('readFile', (p: string) =>
//         Buffer.from(workspace.find(({ path }) => p === path)?.buffer!).toString(
//           'base64',
//         ),
//       )
//       page.exposeFunction('saveResult', async (result: string) => {
//         const response = await fetch(result)
//         const blob = await response.blob()
//         nodeFs.mkdir(path.join(__dirname, 'outputs'), { recursive: true })
//         nodeFs.writeFile(
//           path.join(__dirname, `outputs/${compileProject}.pdf`),
//           Buffer.from(await blob.arrayBuffer()),
//         )
//       })

//       const messages: string[] = []
//       page.on('console', message => {
//         console.log(`Page log: ${message.text()}`)
//         messages.push(message.text())
//       })
//       await page.goto('http://localhost:9000', {
//         waitUntil: 'networkidle0',
//         timeout: 10000,
//       })

//       // Try compile basic project

//       // @ts-ignore
//       page.evaluate(
//         (workspace, mainFile) => compile(workspace, mainFile),
//         workspace,
//         'main.tex',
//       )
//       await page.waitForFunction('compileComplete', { timeout: 60 * 1000 })
//     })
//   },
// )
