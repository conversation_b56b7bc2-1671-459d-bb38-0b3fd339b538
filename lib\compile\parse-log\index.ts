/*
 * @Description:
 * @Author: Devin
 * @Date: 2024-08-28 13:48:24
 */
import LatexLogParser, { ParserOptions } from './latex-log-parser'
import ruleset from './rules'
import { LogEntry, ParseResult } from './types'

export default function parseLog(
  rawLog: string | ParseResult,
  options: ParserOptions = { ignoreDuplicates: true },
): ParseResult {
  const parsedLogEntries: ParseResult =
    typeof rawLog === 'string'
      ? new LatexLogParser(rawLog, options).parse()
      : rawLog

  const seenErrorTypes: { [key: string]: boolean } = {}

  for (const entry of parsedLogEntries.all) {
    const ruleDetails = ruleset.find(rule =>
      rule.regexToMatch.test(entry.message as string),
    )

    if (ruleDetails) {
      if (ruleDetails.ruleId) {
        entry.ruleId = ruleDetails.ruleId
      }

      if (ruleDetails.newMessage) {
        entry.message = (entry.message as string).replace(
          ruleDetails.regexToMatch,
          ruleDetails.newMessage,
        )
      }

      if (ruleDetails.contentRegex && entry.content) {
        const match: RegExpMatchArray | null = entry.content.match(
          ruleDetails.contentRegex,
        )
        if (match) {
          entry.contentDetails = match.slice(1)
        }
      }

      if (entry.contentDetails && ruleDetails.improvedTitle) {
        const message = ruleDetails.improvedTitle(
          entry.message,
          entry.contentDetails,
        )

        if (Array.isArray(message)) {
          entry.message = message[0]
        } else {
          entry.message = message as string //TODO: fix type
        }
      }

      if (ruleDetails.cascadesFrom) {
        for (const type of ruleDetails.cascadesFrom) {
          if (seenErrorTypes[type]) {
            entry.suppressed = true
          }
        }
      }

      if (ruleDetails.types) {
        for (const type of ruleDetails.types) {
          seenErrorTypes[type] = true
        }
      }
    }
  }

  for (const [key, errors] of Object.entries(parsedLogEntries)) {
    if (Array.isArray(errors)) {
      parsedLogEntries[key as keyof ParseResult] = errors.filter(
        (err: LogEntry) => !err.suppressed,
      )
    }
  }

  return parsedLogEntries
}
