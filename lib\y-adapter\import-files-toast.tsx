import { Progress } from '@/components/ui/progress'
import { toast } from 'sonner'

function calculateProgress(completed: number, total: number): number {
  if (total === 0) return 0
  return Math.min(Math.round((completed / total) * 100), 100)
}

function createProgressToast(completed: number, total: number) {
  const progress = calculateProgress(completed, total)

  return (
    <div className="w-full">
      <div className="flex items-center justify-between mb-2">
        <span className="text-sm font-medium">Importing files...</span>
        <span className="text-xs text-muted-foreground">
          {completed}/{total}
        </span>
      </div>
      <Progress value={progress} className="h-2" />
      <div className="text-xs text-muted-foreground mt-1">
        {progress}% complete
      </div>
    </div>
  )
}

export default async function loadingToast(
  promise: Promise<unknown>,
  expectedTime: number = 5,
) {
  const beginTime = Date.now()
  const toastId = toast.loading(createProgressToast(0, 100), {
    duration: Infinity,
  })

  const interval = setInterval(() => {
    const progress = Math.round(
      ((Date.now() - beginTime) / (expectedTime * 1000)) * 100,
    )
    toast.loading(createProgressToast(progress, 100), {
      id: toastId,
    })
  }, 500)

  try {
    const result = await promise
    return result
  } catch (error) {
    throw error
  } finally {
    clearInterval(interval)
    toast.dismiss(toastId)
  }
}
