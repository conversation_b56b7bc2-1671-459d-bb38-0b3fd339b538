'use client'

import React from 'react'
import { useGetPublishedProjects } from '@/services/projects/get-published-projects'
import ProjectGrid from './_components/project-grid'
import { GradientText } from '@/components/gradient-text'

function Template() {
  const { data = [] } = useGetPublishedProjects()

  return (
    <section className="container mx-auto px-6 py-12 max-w-7xl">
      <header className="text-center mb-12">
        <GradientText className="text-5xl font-bold mb-6 block">
          LaTeX Templates — Journal Articles
        </GradientText>

        <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
          LaTeX templates for journal articles, academic papers, CVs and
          resumes, presentations, and more.
        </p>
      </header>

      <ProjectGrid projects={data} />
    </section>
  )
}

export default Template
