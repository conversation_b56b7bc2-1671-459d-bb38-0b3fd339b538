import React from 'react'
import { Loader2 } from 'lucide-react'
import { cn } from '@/lib/utils'

interface FullPageLoadingProps {
  message?: string
  className?: string
}

export function FullPageLoading({
  message = 'Loading...',
  className,
}: FullPageLoadingProps) {
  return (
    <div
      className={cn(
        'fixed inset-0 z-50 flex flex-col items-center justify-center bg-background/80 backdrop-blur-sm',
        className,
      )}
    >
      <div className="flex flex-col items-center space-y-6 text-center">
        {/* Simple spinner */}
        <div className="relative">
          <Loader2 className="h-16 w-16 animate-spin text-primary" />
        </div>

        {/* Loading message */}
        <div className="space-y-3">
          <p className="text-lg font-medium text-foreground">{message}</p>
          <div className="flex space-x-2 justify-center">
            <div className="h-2 w-2 bg-primary rounded-full animate-bounce [animation-delay:-0.3s]" />
            <div className="h-2 w-2 bg-primary rounded-full animate-bounce [animation-delay:-0.15s]" />
            <div className="h-2 w-2 bg-primary rounded-full animate-bounce" />
          </div>
        </div>
      </div>
    </div>
  )
}

export default FullPageLoading
