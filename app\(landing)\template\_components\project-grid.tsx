import Link from 'next/link'
import Image from 'next/image'
import { Project } from '@/services/projects/get-projects'
import { BASE_URL } from '@/services/api'
import { useCallback } from 'react'
import { Tooltip } from '@/components/ui/tooltip'
import { Import } from 'lucide-react'
import useRequireLogin from '@/lib/use-require-login'
import { confirmDialog } from '@/components/global-dialog'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { YProject } from '@/lib/y-project'
import createProject from '@/services/projects/create-project'
import * as Y from 'yjs'
import { toast } from 'sonner'
import { useRouter } from 'next/navigation'

export default function ProjectGrid({ projects }: { projects: Project[] }) {
  const router = useRouter()
  const sortedProjects = [...projects].sort((a, b) => {
    const dateA = new Date(a.updatedAt).getTime()
    const dateB = new Date(b.updatedAt).getTime()
    return dateB - dateA
  })

  const requireLogin = useRequireLogin({
    manualTrigger: true,
    message: 'You must be logged in to import a template.',
    redirectBack: true,
  })

  const onClickProject = useCallback(
    async (project: Project) => {
      const isLoggedIn = requireLogin()
      if (!isLoggedIn) return
      const confirm = await confirmDialog(resolve => (
        <AlertDialog>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>
                Are you sure to import {project.projectName}?
              </AlertDialogTitle>
              <AlertDialogDescription>
                This action cannot be undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel onClick={() => resolve(false)}>
                Cancel
              </AlertDialogCancel>
              <AlertDialogAction onClick={() => resolve(true)}>
                Continue
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      ))
      if (!confirm) return
      // TODO: merge the project import logic with project copy logic
      const originalYProject = await new YProject({
        projectId: project.id,
      })
      const { id } = await createProject(project.projectName)
      const newYProject = new YProject({
        projectId: id,
        readonly: false,
      })
      await originalYProject.initialSynced
      const update = Y.encodeStateAsUpdate(originalYProject.ydoc)
      Y.applyUpdate(newYProject.ydoc, update)
      toast.success(`Project ${project.projectName} import successfully!`)
      router.push(`/editor?project-id=${id}`)
      originalYProject.destroy()
      newYProject.destroy()
    },
    [requireLogin, router],
  )

  return (
    <div className="p-4">
      <div className="grid grid-cols-6 gap-6 items-start text-base leading-4 text-center text-black max-md:grid-cols-1 max-md:mt-10 max-md:max-w-full">
        {sortedProjects.map((project, index) => (
          <Tooltip key={index} content="Import Template">
            <div
              key={project.id}
              className="flex flex-col bg-white rounded-xl cursor-pointer relative overflow-hidden group"
              onClick={() => onClickProject(project)}
            >
              <div className="relative p-4 pb-2">
                <div className="absolute inset-4 top-4 bottom-2 bg-gray-200 rounded-lg flex items-center justify-center z-0">
                  <span className="text-gray-500 text-sm">
                    No Preview Available
                  </span>
                </div>
                <Image
                  width={300}
                  height={400}
                  src={BASE_URL + `projects/${project.id}/preview`}
                  alt="Project"
                  className="relative w-full aspect-[1/1.35] object-cover rounded-lg z-10 shadow-sm"
                  onError={e => {
                    e.currentTarget.style.visibility = 'hidden'
                  }}
                />
                <div className="absolute inset-4 top-4 bottom-2 bg-black/30 rounded-lg flex items-center justify-center z-20 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                  <Import className="w-8 h-8 text-white" />
                </div>
              </div>
              <div className="p-4 pt-2">
                <Link
                  href={`editor?project-id=${project.id}`}
                  className="block text-gray-800 hover:text-blue-600 hover:underline font-medium truncate"
                  title={project.projectName}
                >
                  {project.projectName}
                </Link>
              </div>
            </div>
          </Tooltip>
        ))}
      </div>
    </div>
  )
}
