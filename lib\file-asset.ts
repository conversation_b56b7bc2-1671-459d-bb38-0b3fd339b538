import axios from 'axios'
import { storeFile } from './file-cache'
import postPresignedUrls from '@/services/projects/post-presigned-urls'

export async function storeToCache(hash: string, buffer: ArrayBufferLike) {
  return storeFile(hash, buffer)
}

export async function downloadAssets(projectId: string, hashes: string[]) {
  let presignedUrls = await postPresignedUrls(projectId, hashes)
  const missingFiles = presignedUrls.filter(({ url }) => !url)
  if (missingFiles.length) {
    throw new Error(
      `Failed to get presigned urls: [${missingFiles.map(({ hash }) => hash).join(', ')}]`,
    )
  }

  if (process.env.NEXT_PUBLIC_PROXY_OSS_URL) {
    presignedUrls = presignedUrls.map(({ url, hash }) => {
      return {
        url: url.replace(
          process.env.NEXT_PUBLIC_PROXY_OSS_URL!,
          '/api/proxy/oss',
        ),
        hash,
      }
    })
  }

  const arrayBuffers = await Promise.all(
    presignedUrls.map(async ({ url, hash }) => {
      const res = await axios.get(url, {
        responseType: 'arraybuffer',
      })
      const buffer = res.data
      await storeFile(hash, buffer)
      return buffer
    }),
  )

  return arrayBuffers
}
