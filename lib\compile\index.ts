import { getFoldersToCreate } from '../utils'
import type { PdfTeXEngine } from '@/public/PdfTeXEngine'
import type { XeTeXEngine } from '@/public/XeTeXEngine'
import type { DvipdfmxEngine } from '@/public/DvipdfmxEngine'
import { OutputFileEntry } from '../y-project/file-index'

type PdfTeXEngine = InstanceType<typeof PdfTeXEngine>
type XeTeXEngine = InstanceType<typeof XeTeXEngine>
type DvipdfmxEngine = InstanceType<typeof DvipdfmxEngine>
let pdfTeXEngine: PdfTeXEngine | null = null
let xeTeXEngine: XeTeXEngine | null = null
let dvipdfmxEngine: DvipdfmxEngine | null = null

async function initPdfTeXEngine() {
  if (!pdfTeXEngine) {
    const { PdfTeXEngine } = await import('@/public/PdfTeXEngine')
    pdfTeXEngine = new PdfTeXEngine()
    await pdfTeXEngine.loadEngine()
  }
}

async function initXeTeXEngine() {
  if (!xeTeXEngine) {
    const { XeTeXEngine } = await import('@/public/XeTeXEngine')
    xeTeXEngine = new XeTeXEngine()
    await xeTeXEngine.loadEngine()
  }
}

async function initDvipdfmxEngine() {
  if (!dvipdfmxEngine) {
    const { DvipdfmxEngine } = await import('@/public/DvipdfmxEngine')
    dvipdfmxEngine = new DvipdfmxEngine()
    await dvipdfmxEngine.loadEngine()
  }
}

export async function loadEngine(type: 'pdftex' | 'xetex') {
  if (type === 'pdftex') {
    await initPdfTeXEngine()
  } else {
    await Promise.all([initXeTeXEngine(), initDvipdfmxEngine()])
  }
}

async function writeEntryToMemFS(
  engine: PdfTeXEngine | XeTeXEngine | DvipdfmxEngine,
  entries: OutputFileEntry[],
) {
  const folders = getFoldersToCreate(entries.map(({ path }) => path))
  folders.forEach(folder => engine.makeMemFSFolder(folder))
  await Promise.all(
    entries.map(async ({ path, buffer }) => {
      engine.writeMemFSFile(path, new Uint8Array(buffer))
    }),
  )
}

export interface CompileFileEntry {
  path: string
  buffer: ArrayBufferLike
}

export interface CompileConfig {
  engineType: 'pdftex' | 'xetex'
  mainFile: string
}

export interface CompileResult {
  status: number
  pdf: ArrayBuffer
  log: string
}

export async function compile(
  entries: CompileFileEntry[],
  config: CompileConfig,
): Promise<CompileResult> {
  const { engineType, mainFile } = config
  await loadEngine(engineType)
  const engine = (engineType === 'pdftex' ? pdfTeXEngine : xeTeXEngine)!
  engine.flushCache()
  await writeEntryToMemFS(engine, entries)

  engine.setEngineMainFile(mainFile)

  const results: CompileResult[] = []
  // Compile 3 times is necessary
  for (let i = 0; i < 3; i++) {
    const result = await engine.compileLaTeX()
    results.push(result)
    if (result.pdf && engineType === 'xetex') {
      await writeEntryToMemFS(dvipdfmxEngine!, entries)
      dvipdfmxEngine!.writeMemFSFile('main.xdv', result.pdf)
      dvipdfmxEngine!.setEngineMainFile('main.xdv')
      const dviResult = await dvipdfmxEngine!.compilePDF()
      results.push({
        ...dviResult,
        log: result.log + dviResult.log,
      })
    }
  }
  // eslint-disable-next-line no-console
  console.log('Compile results:', results)
  return results.at(-1)!
}
