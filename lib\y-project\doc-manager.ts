import { IndexeddbPersistence } from 'y-indexeddb'
import { HocuspocusProvider } from '@hocuspocus/provider'
import { toast } from 'sonner'
import { Doc } from 'yjs'

export class DocManager {
  private static onlineInstances: Map<string, OnlineYDoc> = new Map()

  public static getInstance(projectId: string) {
    if (this.onlineInstances.has(projectId)) {
      const instance = this.onlineInstances.get(projectId)!
      instance.refCount++
      return instance
    } else {
      const newInstance = new OnlineYDoc(projectId)
      this.onlineInstances.set(projectId, newInstance)
      return newInstance
    }
  }

  public static releaseInstance(projectId: string): void {
    if (this.onlineInstances.has(projectId)) {
      const instance = this.onlineInstances.get(projectId)!
      instance.refCount--
      if (instance.refCount <= 0) {
        setTimeout(() => {
          const instance = this.onlineInstances.get(projectId)
          if (instance && instance.refCount <= 0) {
            instance.doc.destroy()
            this.onlineInstances.delete(projectId)
          }
        }, 30000)
      }
    }
  }
}

class OnlineYDoc {
  projectId: string
  doc: Doc
  refCount: number
  websocketProvider: HocuspocusProvider
  indexedDBProvider: IndexeddbPersistence
  initialSynced: Promise<void>

  constructor(projectId: string) {
    this.projectId = projectId
    this.doc = new Doc()
    this.refCount = 1

    let connectedTimeout: NodeJS.Timeout | null = null
    let connectSuccess = false
    this.indexedDBProvider = new IndexeddbPersistence(
      `project-cache/${projectId}`,
      this.doc,
    )
    this.websocketProvider = new HocuspocusProvider({
      url: '/api/ws',
      name: projectId,
      document: this.doc,
      token: 'unused-token',
      onStatus: ({ status }) => {
        if (status === 'connecting' && connectedTimeout === null) {
          connectedTimeout = setTimeout(() => {
            if (!connectSuccess) {
              toast.error(
                'Failed to connect to the project. Please check your internet connection or try again later.',
              )
            }
            connectedTimeout = null
          }, 5000)
        } else if (status === 'connected') {
          connectSuccess = true
        }
      },
    })

    let resolveSynced: () => void
    this.initialSynced = new Promise(res => {
      resolveSynced = res
    })

    this.websocketProvider.on('synced', () => {
      resolveSynced()
    })
  }

  setUser(user: { id: string; name: string }) {
    this.websocketProvider.awareness?.setLocalStateField('user', {
      id: user.id,
      name: user.name,
    })
  }

  destroy() {
    this.doc.destroy()
  }

  release() {
    DocManager.releaseInstance(this.projectId)
  }
}

export type { OnlineYDoc }
