import * as Y from 'yjs'

export type FileNode = AssetNode | TextNode | FolderNode
export type FolderNode = Y.Map<FileNode>
export interface AssetNode {
  isAsset: true
  hash: string
  size: number
}

export interface TextNode {
  isText: true
  uuid: string
}

export interface InputFileEntry {
  path: string
  file: Blob
}

export interface OutputFileEntry {
  path: string
  buffer: ArrayBufferLike
}

export function isFolderNode(node: unknown): node is FolderNode {
  return node instanceof Y.Map
}

export function isAssetNode(node: unknown): node is AssetNode {
  return (node as AssetNode).isAsset === true
}

export function dfs(
  rootNode: FolderNode,
  callback: (node: FileNode, prefix: string) => boolean | void,
) {
  const queue: Array<{
    prefix: string
    node: FileNode
  }> = [
    {
      prefix: '',
      node: rootNode,
    },
  ]

  while (queue.length > 0) {
    const { prefix, node } = queue.pop()!
    if (node) {
      if (isFolderNode(node)) {
        for (const [segment, child] of node) {
          queue.push({
            prefix: prefix ? prefix + '/' + segment : segment,
            node: child,
          })
        }
      }
      const shouldStop = callback(node, prefix)
      if (shouldStop) {
        break
      }
    }
  }
}
