import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { toast } from 'sonner'
import createShareToken from '@/services/projects/create-share-token'
import { useGetTokens } from '@/services/projects/get-tokens'

function getLink(projectId: string, token: string) {
  if (!token) return ''
  return `${window.location.origin}/invitation?token=${token}`
}

interface ShareLinkProps {
  projectId: string
}

export default function ShareLink({ projectId }: ShareLinkProps) {
  const { data, refetch } = useGetTokens(projectId)

  const handleGenerateLink = async () => {
    await Promise.all([
      createShareToken(projectId, 'collaborator'),
      createShareToken(projectId, 'viewer'),
    ])
    refetch()
  }

  const copyToClipboard = async (text: string, inputEl?: HTMLInputElement) => {
    if (!navigator.clipboard) {
      if (inputEl) {
        inputEl.select()
        toast.info(
          'Clipboard API is not supported. Please press Ctrl+C (or ⌘+C) to copy manually.',
        )
      } else {
        toast.error('Clipboard API not supported and input is not accessible.')
      }
      return
    }

    try {
      await navigator.clipboard.writeText(text)
      toast.success('The link has been copied to your clipboard.')
    } catch (err) {
      console.error('Failed to copy text to clipboard:', err)
      if (inputEl) {
        inputEl.select()
        toast.error('Clipboard copy failed. Please copy manually.')
      } else {
        toast.error('Clipboard copy failed and no fallback available.')
      }
    }
  }

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <div>
          <p className="text-sm font-medium">Collaborator Share Link:</p>
          <Input
            value={getLink(projectId, data?.collaboratorShareToken || '')}
            readOnly
            className="mt-1 cursor-pointer"
            onClick={e =>
              copyToClipboard(
                getLink(projectId, data?.collaboratorShareToken || ''),
                e.currentTarget,
              )
            }
          />
        </div>
        <div>
          <p className="text-sm font-medium">Viewer Share Link:</p>
          <Input
            value={getLink(projectId, data?.viewerShareToken || '')}
            readOnly
            className="mt-1 cursor-pointer"
            onClick={e =>
              copyToClipboard(
                getLink(projectId, data?.viewerShareToken || ''),
                e.currentTarget,
              )
            }
          />
        </div>
      </div>
      <Button
        onClick={handleGenerateLink}
        className="w-full"
        tag-id="generate-link-success"
      >
        Generate Link
      </Button>
    </div>
  )
}
