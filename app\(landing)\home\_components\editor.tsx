import React from 'react'
import Image from 'next/image'
import { PROJECT_CODE } from '@/lib/constant'

const Editor = () => {
  return (
    <div
      className="w-full px-4 sm:px-8 md:px-16 lg:px-32 py-16 md:py-24 m-auto min-h-[120vh] flex flex-col justify-center max-w-[90%]"
      id="editor"
    >
      {/* What We Do Section */}
      <div
        className="inline-block text-4xl md:text-5xl lg:text-6xl font-semibold leading-tight text-transparent bg-gradient-to-r from-[#33B4FF] to-[#C5C5C5] bg-clip-text mb-16"
        data-aos="fade-right"
        data-aos-delay="200"
      >
        What we do
      </div>

      <div className="flex flex-col gap-8 lg:gap-12">
        {/* 文本内容 */}
        <div className="w-full" data-aos="fade-up" data-aos-delay="300">
          <h2 className="text-xl md:text-3xl font-semibold text-black text-left">
            How {PROJECT_CODE} editor works
          </h2>
          <p className="text-lg md:text-xl text-neutral-700 mt-4 leading-relaxed text-left w-full">
            {PROJECT_CODE} is a free collaborative online LaTeX editor that
            leverages WebAssembly technology to speed up the compilation time
            and reduce the computation cost. {PROJECT_CODE} is also an AI-native
            editor, which could make context-aware writing suggestions.
          </p>
        </div>

        {/* 图片部分 - 使用网格布局 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-8 w-full">
          <div
            className="flex justify-center md:justify-start"
            data-aos="zoom-in-right"
            data-aos-delay="400"
          >
            <Image
              src="https://cdn.builder.io/api/v1/image/assets/TEMP/7f36c404d437c52b408a04108c29168aaa49bd439c2e544a444eb51734a8b412?placeholderIfAbsent=true&apiKey=bf311a4629164229920c17c3cb96b691"
              alt="AI-enabled LaTeX editor interface"
              width={576}
              height={345}
              className="object-contain rounded-2xl w-full max-w-[576px] h-auto"
            />
          </div>
          <div
            className="flex justify-center md:justify-start"
            data-aos="zoom-in-left"
            data-aos-delay="500"
          >
            <Image
              src="https://cdn.builder.io/api/v1/image/assets/TEMP/672f8b58a625bbc0b5c007f4a6c2dd172edd966061464ef96319503548bbd8b6?placeholderIfAbsent=true&apiKey=bf311a4629164229920c17c3cb96b691"
              alt="LaTeX document with AI suggestions"
              width={471}
              height={344}
              className="object-contain rounded-2xl w-full max-w-[471px] h-auto"
            />
          </div>
        </div>
      </div>
    </div>
  )
}

export default Editor
