import { useForm } from 'react-hook-form'
import { But<PERSON> } from '@/components/ui/button'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Input } from '@/components/ui/input'
import { toast } from 'sonner'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import createShareToken from '@/services/projects/create-share-token'

interface EmailFormData {
  email: string
  role: 'collaborator' | 'viewer'
}

const emailFormSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  role: z.enum(['collaborator', 'viewer']),
})

interface ShareEmailProps {
  projectId: string
}

export default function ShareEmail({ projectId }: ShareEmailProps) {
  const form = useForm<EmailFormData>({
    resolver: zod<PERSON><PERSON><PERSON><PERSON>(emailFormSchema),
    defaultValues: {
      email: '',
      role: 'collaborator',
    },
  })

  const onSubmit = async (data: EmailFormData) => {
    try {
      await createShareToken(projectId, data.role, data.email)
      toast.success('Invitation sent successfully!')
      form.reset()
    } catch (error) {
      console.error('Failed to send invitation:', error)
      toast.error('Failed to send invitation. Please try again.')
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email Address</FormLabel>
              <FormControl>
                <Input
                  placeholder="Enter email address"
                  type="email"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="role"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Role</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select role" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="collaborator">Collaborator</SelectItem>
                  <SelectItem value="viewer">Viewer</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <Button type="submit" className="w-full">
          Send Invitation
        </Button>
      </form>
    </Form>
  )
}
