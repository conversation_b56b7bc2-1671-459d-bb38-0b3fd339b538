export function triggerFileUpload(): Promise<File[]> {
  return new Promise(resolve => {
    // Create input element
    const input = document.createElement('input')
    input.type = 'file'

    // Allow multiple files
    input.multiple = true

    // Allow folder selection
    input.setAttribute('webkitdirectory', '') // For Chrome/Edge
    input.setAttribute('directory', '') // For Firefox

    // Optional: Add accept attribute to filter file types
    // input.accept = '.pdf,.jpg,.png'; // Uncomment and modify as needed

    // Handle file selection
    input.onchange = (event: Event) => {
      const target = event.target as HTMLInputElement
      if (target.files && target.files.length > 0) {
        const filesArray = Array.from(target.files)
        resolve(filesArray)
      }
      document.body.removeChild(input)
    }

    // Handle cancellation
    input.oncancel = () => {
      document.body.removeChild(input)
    }

    // Add to DOM and trigger click
    input.style.display = 'none'
    document.body.appendChild(input)
    input.click()
  })
}
