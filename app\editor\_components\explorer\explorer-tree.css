/* General tree node styling */
.rc-tree .rc-tree-treenode,
.rc-tree .rc-tree-node-content-wrapper {
  display: flex;
  flex: 1;
  align-items: center;
}

.rc-tree .rc-tree-treenode:hover {
  background-color: oklch(0.9666 0 284);
}

/* Switcher and icon spacing */
.rc-tree .rc-tree-switcher > *,
.rc-tree .rc-tree-iconEle > * {
  margin: 4px;
}

/* Indent handling */
.rc-tree .rc-tree-indent {
  display: inline-flex;
  align-items: stretch;
  align-self: stretch;
}

/* Indent unit styling */
.rc-tree .rc-tree-indent-unit {
  position: relative;
  display: block;
  align-self: stretch;
  width: 24px;
}

.rc-tree .rc-tree-indent-unit::before {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  left: 50%;
  width: 1px;
  background-color: #ccc; /* Customize line color */
}

/* Tree title padding */
.rc-tree .rc-tree-title {
  padding: 4px;
  flex: 1;
}

.rc-tree .rc-tree-treenode-selected {
  background-color: oklch(0.9466 0 284);
}

.rc-tree .drop-target {
  background-color: oklch(0.9666 0 284);
}
