import FileIndex from './file-index'
import FileHandler from './file-handler'
import VersionHandler from './version-handler'
import Undo<PERSON>andler from './undo-handler'
import ConfigHandler from './config-handler'
import { compile } from '../compile'
import { sha256 } from '../utils'
import { DocManager, OnlineYDoc } from './doc-manager'
import { createReadonlyDoc } from './readonly-doc'
import { Doc } from 'yjs'

export interface YProjectConfig {
  projectId: string
  ydoc?: Doc
  connectivity?: boolean
  readonly?: boolean
  user?: {
    id: string
    name: string
  }
}

export class YProject {
  projectId: string
  onlineYDoc: OnlineYDoc | null = null

  ydoc: Doc

  fileIndex: FileIndex
  fileHandler: FileHandler
  versionHandler: VersionHandler
  undoHandler: UndoHandler
  configHandler: ConfigHandler
  initialSynced: Promise<void> | null = null
  internalOrigins: Set<unknown> = new Set()
  docToDestroy: Doc[] = []

  constructor(config: YProjectConfig) {
    const { projectId, ydoc, user, readonly = true } = config

    this.projectId = projectId
    if (ydoc) {
      this.ydoc = createReadonlyDoc(ydoc)
      this.docToDestroy.push(this.ydoc)
    } else {
      this.onlineYDoc = DocManager.getInstance(projectId)
      if (readonly) {
        this.ydoc = createReadonlyDoc(this.onlineYDoc.doc)
        this.docToDestroy.push(this.ydoc)
      } else {
        this.ydoc = this.onlineYDoc.doc
      }

      if (user) {
        this.onlineYDoc.setUser(user)
      }

      this.initialSynced = this.onlineYDoc.initialSynced
      this.internalOrigins.add(this.onlineYDoc.websocketProvider)
      this.internalOrigins.add(this.onlineYDoc.indexedDBProvider)
    }

    this.configHandler = new ConfigHandler(this)
    this.fileIndex = new FileIndex(this)
    this.fileHandler = new FileHandler(this.fileIndex)
    this.undoHandler = new UndoHandler(this)
    this.versionHandler = new VersionHandler(this, config)
    this.internalOrigins.add(this.versionHandler)
  }

  public getSha256Hash() {
    const config = this.configHandler.getConfig()
    const entries = this.fileIndex.exportIndex()
    let content = entries
      .map(entry => {
        return entry.path + entry.content || entry.hash || ''
      })
      .join('\n')
    content += JSON.stringify(config)
    return sha256(new TextEncoder().encode(content))
  }

  public static async compileYProject(projectId: string, version?: string) {
    let yproject = new YProject({ projectId })
    yproject = await yproject.versionHandler.switchVersion(version)
    const entries = await yproject.fileIndex.exportFiles()
    const config = yproject.configHandler.getConfig()
    try {
      const result = await compile(entries, config)
      return result
    } finally {
      yproject.destroy()
    }
  }

  public destroy() {
    this.onlineYDoc?.release()
    this.docToDestroy.forEach(doc => doc.destroy())
    this.versionHandler.destroy()
  }
}
