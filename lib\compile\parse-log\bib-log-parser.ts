import { LogEntry, ParseResult } from './types'

// Define type for parser function
type ParserFunction = (match: RegExpExecArray) => LogEntry

// Define type for parser tuple
type Parser = [RegExp, ParserFunction]

// Define options interface
interface ParserOptions {
  maxErrors?: number
}

// Regular expressions
const LINE_SPLITTER_REGEX: RegExp = /^\[(\d+)].*>\s(INFO|WARN|ERROR)\s-\s(.*)$/

const MULTILINE_WARNING_REGEX: RegExp =
  /^Warning--(.+)\n--line (\d+) of file (.+)$/m
const SINGLELINE_WARNING_REGEX: RegExp = /^Warning--(.+)$/m
const MULTILINE_ERROR_REGEX: RegExp =
  /^(.*)---line (\d+) of file (.*)\n([^]+?)\nI'm skipping whatever remains of this entry$/m
const BAD_CROSS_REFERENCE_REGEX: RegExp =
  /^(A bad cross reference---entry ".+?"\nrefers to entry.+?, which doesn't exist)$/m
const MULTILINE_COMMAND_ERROR_REGEX: RegExp =
  /^(.*)\n?---line (\d+) of file (.*)\n([^]+?)\nI'm skipping whatever remains of this command$/m
const BST_ERROR_REGEX: RegExp =
  /^(.*?)\nwhile executing---line (\d+) of file (.*)/m

// Message levels mapping
const MESSAGE_LEVELS: { [key: string]: string } = {
  INFO: 'info',
  WARN: 'warning',
  ERROR: 'error',
}

// Parser reducer function
const parserReducer = function (maxErrors?: number) {
  return function (
    accumulator: [LogEntry[], string],
    parser: Parser,
  ): [LogEntry[], string] {
    const consume = function (
      logText: string,
      regex: RegExp,
      process: ParserFunction,
    ): [LogEntry[], string] {
      let match: RegExpExecArray | null
      let text: string = logText
      const result: LogEntry[] = []
      let iterationCount: number = 0

      while ((match = regex.exec(text))) {
        iterationCount++
        const newEntry: LogEntry = process(match)

        if (maxErrors != null && iterationCount >= maxErrors) {
          return [result, '']
        }

        result.push(newEntry)
        text =
          match.input.slice(0, match.index) +
          match.input.slice(
            match.index + match[0].length + 1,
            match.input.length,
          )
      }

      return [result, text]
    }

    const [currentErrors, text]: [LogEntry[], string] = accumulator
    const [regex, process]: Parser = parser
    const [errors, remainingText]: [LogEntry[], string] = consume(
      text,
      regex,
      process,
    )
    return [currentErrors.concat(errors), remainingText]
  }
}

export default class BibLogParser {
  private text: string
  private options: ParserOptions
  private lines: string[]
  private warningParsers: Parser[]
  private errorParsers: Parser[]

  constructor(text: string, options: ParserOptions = {}) {
    if (typeof text !== 'string') {
      throw new Error('BibLogParser Error: text parameter must be a string')
    }
    this.text = text.replace(/(\r\n)|\r/g, '\n')
    this.options = options
    this.lines = text.split('\n')

    this.warningParsers = [
      [
        MULTILINE_WARNING_REGEX,
        (match: RegExpExecArray): LogEntry => {
          const [fullMatch, message, lineNumber, fileName] = match
          return {
            file: fileName,
            level: 'warning',
            message,
            line: lineNumber,
            raw: fullMatch,
          }
        },
      ],
      [
        SINGLELINE_WARNING_REGEX,
        (match: RegExpExecArray): LogEntry => {
          const [fullMatch, message] = match
          return {
            file: '',
            level: 'warning',
            message,
            line: '',
            raw: fullMatch,
          }
        },
      ],
    ]

    this.errorParsers = [
      [
        MULTILINE_ERROR_REGEX,
        (match: RegExpExecArray): LogEntry => {
          const [fullMatch, firstMessage, lineNumber, fileName, secondMessage] =
            match
          return {
            file: fileName,
            level: 'error',
            message: firstMessage + '\n' + secondMessage,
            line: lineNumber,
            raw: fullMatch,
          }
        },
      ],
      [
        BAD_CROSS_REFERENCE_REGEX,
        (match: RegExpExecArray): LogEntry => {
          const [fullMatch, message] = match
          return {
            file: '',
            level: 'error',
            message,
            line: '',
            raw: fullMatch,
          }
        },
      ],
      [
        MULTILINE_COMMAND_ERROR_REGEX,
        (match: RegExpExecArray): LogEntry => {
          const [fullMatch, firstMessage, lineNumber, fileName, secondMessage] =
            match
          return {
            file: fileName,
            level: 'error',
            message: firstMessage + '\n' + secondMessage,
            line: lineNumber,
            raw: fullMatch,
          }
        },
      ],
      [
        BST_ERROR_REGEX,
        (match: RegExpExecArray): LogEntry => {
          const [fullMatch, firstMessage, lineNumber, fileName] = match
          return {
            file: fileName,
            level: 'error',
            message: firstMessage,
            line: lineNumber,
            raw: fullMatch,
          }
        },
      ],
    ]
  }

  parseBibtex(): ParseResult {
    const [allWarnings, remainingText]: [LogEntry[], string] =
      this.warningParsers.reduce(parserReducer(this.options.maxErrors), [
        [],
        this.text,
      ])
    const [allErrors]: [LogEntry[], string] = this.errorParsers.reduce(
      parserReducer(this.options.maxErrors),
      [[], remainingText],
    )

    return {
      all: allWarnings.concat(allErrors),
      errors: allErrors,
      warnings: allWarnings,
      files: [],
      typesetting: [],
    }
  }

  parseBiber(): ParseResult {
    const result: ParseResult = {
      all: [],
      errors: [],
      warnings: [],
      files: [],
      typesetting: [],
    }

    this.lines.forEach((line: string) => {
      const match: RegExpMatchArray | null = line.match(LINE_SPLITTER_REGEX)
      if (match) {
        const [fullLine, , messageType, message]: string[] = match
        const newEntry: LogEntry = {
          file: '',
          level:
            (MESSAGE_LEVELS[messageType] as 'info' | 'warning' | 'error') ||
            'info',
          message,
          line: '',
          raw: fullLine,
        }

        const lineMatch: RegExpMatchArray | null = newEntry.message.match(
          /^BibTeX subsystem: \/.+\/(\w+\.\w+)_.+, line (\d+), (.+)$/,
        )
        if (lineMatch) {
          const [, fileName, lineNumber, realMessage]: string[] = lineMatch
          newEntry.file = fileName
          newEntry.line = lineNumber
          newEntry.message = realMessage
        }
        result.all.push(newEntry)
        switch (newEntry.level) {
          case 'error':
            result.errors.push(newEntry)
            break
          case 'warning':
            result.warnings.push(newEntry)
            break
        }
      }
    })

    return result
  }

  parse(): ParseResult {
    const firstLine: string = this.lines[0]
    if (firstLine.match(/^.*INFO - This is Biber.*$/)) {
      return this.parseBiber()
    } else if (firstLine.match(/^This is BibTeX, Version.+$/)) {
      return this.parseBibtex()
    } else {
      throw new Error(
        'BibLogParser Error: cannot determine whether text is biber or bibtex output',
      )
    }
  }
}
