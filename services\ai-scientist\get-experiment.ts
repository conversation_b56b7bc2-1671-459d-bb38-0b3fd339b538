import api from '@/services/api'

export default async function getExperiment(model: string) {
  // Call the results endpoint
  const res = await api.get(`/ai-scientist/results/${model}`, {
    responseType: 'blob',
  })

  const contentDisposition = res.headers['content-disposition']
  const match = contentDisposition?.match(/filename="?([^"]+)"?/)
  const filename = match ? match[1] : `results.zip`
  const file = new File([res.data], filename, {
    type: 'application/zip',
  })

  return file
}
