import { useQuery } from '@tanstack/react-query'
import api from '../api'
import queryClient from '../query-client'
interface Result {
  role: string
  projectId: string
  projectName: string
}

export default async function getTokenInfo(token: string): Promise<Result> {
  const response = await api.get(`/projects/share-tokens/${token}`)
  return response.data
}

export function useGetTokenInfo(token: string) {
  return useQuery(
    {
      queryKey: ['share-token', token],
      queryFn: () => getTokenInfo(token),
      enabled: !!token,
    },
    queryClient,
  )
}
