import api from '../api'

export interface CreateExperimentParams {
  experiment: string
  model: string
  num_ideas: number
  natural_language_prompt?: string
  writeup?: string
  parallel?: number
  improvement?: boolean
  gpus?: string
  engine?: string
  skip_idea_generation?: boolean
  skip_novelty_check?: boolean
  force_rerun?: boolean
}

export default async function createExperiment(params: CreateExperimentParams) {
  // Call the run endpoint
  const res = await api.post(`/ai-scientist/run`, params, {
    headers: {
      'Content-Type': 'application/json',
    },
  })

  return res.data
}
