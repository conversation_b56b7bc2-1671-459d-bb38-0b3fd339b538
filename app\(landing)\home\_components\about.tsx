import TextBackground from '@/components/text-background'
import { PROJECT_CODE } from '@/lib/constant'
import React from 'react'

const AboutPage = () => {
  return (
    <div className="pb-16 flex flex-col items-center justify-center w-full relative min-h-sceen">
      <TextBackground text={PROJECT_CODE} backgroundClassName="text-[#E7F6FF]">
        <div
          className="relative z-10 p-8 flex items-center justify-center max-w-10/12 h-full min-h-[300px] mx-auto pt-16 md:pt-28 lg:pt-44"
          data-aos="fade-up"
          data-aos-delay="300"
        >
          <div
            className="text-black text-center font-semibold font-['Public_Sans'] max-w-[70%] px-4"
            style={{
              fontSize: 'clamp(16px, 3vw, 52px)',
              lineHeight: 'clamp(20px, 3.5vw, 62px)',
            }}
          >
            We are {PROJECT_CODE}. We are trying to lower the barrier of
            research 😊
          </div>
        </div>
      </TextBackground>
    </div>
  )
}

export default AboutPage
