import { useGetMe } from '@/services/auth/get-me'
import { useRouter } from 'next/navigation'
import { useCallback, useEffect } from 'react'
import { toast } from 'sonner'

interface UseRequireLoginOptions {
  message?: string
  manualTrigger?: boolean
  redirectBack?: boolean
}

export default function useRequireLogin({
  message = 'You must be logged in to access this page.',
  manualTrigger = false,
  redirectBack = true,
}: UseRequireLoginOptions = {}) {
  const router = useRouter()
  const { data, isLoading } = useGetMe()
  const isLoggedIn = !!data

  const requireLogin = useCallback(() => {
    if (!isLoggedIn) {
      toast.info(message, {
        id: 'require-login',
      })
      if (redirectBack) {
        router.push(
          `/login?redirect=${encodeURIComponent(window.location.href)}`,
        )
      } else {
        router.push('/login')
      }
    }
    return isLoggedIn
  }, [isLoggedIn, message, redirectBack, router])

  useEffect(() => {
    if (isLoading || manualTrigger) return
    requireLogin()
  }, [isLoading, manualTrigger, requireLogin])

  return requireLogin
}
