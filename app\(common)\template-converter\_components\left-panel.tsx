'use client'
import React, { useEffect, useState } from 'react'
import <PERSON>Arrow from '@/public/assets/website/RightArrow.svg'
import Image from 'next/image'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import JS<PERSON><PERSON> from 'jszip'
import {
  TemplateListItem,
  ConversionStatus,
  getTemplates,
  createConversion,
  getConversionStatus,
  downloadConversionResult,
  uploadTemplate as uploadTemplateAPI,
} from '@/services/template-converter'

const GradientTitle: React.FC<{ step: string; title: string }> = ({
  step,
  title,
}) => (
  <>
    <h1
      className="inline-block text-2xl font-bold z-10 font-['Public_Sans'] leading-tight pb-1 bg-gradient-to-r from-[#33B4FF] to-[#FFAE00] bg-clip-text text-transparent"
      style={{
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        backgroundSize: '100% 100%',
      }}
    >
      {step}
    </h1>
    <h2 className="mt-2.5 text-xl font-semibold text-neutral-700 max-sm:text-lg">
      {title}
    </h2>
  </>
)

// 主面板组件
interface LeftPanelProps {
  onPdfGenerated?: (pdfData: ArrayBuffer) => void
  onCompileStatusChange?: (isCompiling: boolean) => void
}

const LeftPanel: React.FC<LeftPanelProps> = ({
  onPdfGenerated,
  onCompileStatusChange,
}) => {
  const [templates, setTemplates] = useState<TemplateListItem[]>([])
  const [selectedTemplate, setSelectedTemplate] =
    useState<TemplateListItem | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [uploadedFiles, setUploadedFiles] = useState<{
    zipFile: File | null
    mainTexFile: string
  }>({
    zipFile: null,
    mainTexFile: '',
  })
  const [showUploadForm, setShowUploadForm] = useState(false)
  const [templateForm, setTemplateForm] = useState({
    templateName: '',
    mainTexFile: '',
    recommendedCompile: '',
    templateZip: null as File | null,
  })
  const [isUploadingTemplate, setIsUploadingTemplate] = useState(false)
  const [uploadError, setUploadError] = useState('')

  // 转换相关状态
  const [, _setCurrentConversionId] = useState<string | null>(null)
  const [conversionStatus, setConversionStatus] =
    useState<ConversionStatus | null>(null)
  const [conversionLogs, setConversionLogs] = useState<string[]>([])
  const [pollingInterval, setPollingInterval] = useState<NodeJS.Timeout | null>(
    null,
  )

  // 编译方法选项（仅用于模板上传表单）
  const compileMethods = [
    { value: 'pdflatex', label: 'PdfLatex' },
    { value: 'xelatex', label: 'XeLatex' },
    { value: 'lualatex', label: 'LuaLatex' },
  ]

  // 文件验证函数 - 用于源文件（100MB限制）
  const validateZipFile = async (
    file: File,
  ): Promise<{ isValid: boolean; error?: string }> => {
    // 1. 检查文件扩展名
    if (!file.name.toLowerCase().endsWith('.zip')) {
      return { isValid: false, error: '请选择ZIP文件' }
    }

    // 2. 检查文件大小 (100MB for source files)
    const maxSize = 100 * 1024 * 1024 // 100MB
    if (file.size > maxSize) {
      return { isValid: false, error: '文件大小不能超过100MB' }
    }

    // 3. 检查文件是否为空
    if (file.size === 0) {
      return { isValid: false, error: '文件不能为空' }
    }

    // 4. 基本的ZIP文件头验证
    try {
      const arrayBuffer = await file.slice(0, 4).arrayBuffer()
      const header = new Uint8Array(arrayBuffer)

      // ZIP文件魔数检查
      const isZipHeader =
        header[0] === 0x50 &&
        header[1] === 0x4b &&
        ((header[2] === 0x03 && header[3] === 0x04) || // 本地文件头
          (header[2] === 0x05 && header[3] === 0x06) || // 中央目录结束
          (header[2] === 0x07 && header[3] === 0x08)) // 跨度标记

      if (!isZipHeader) {
        return { isValid: false, error: '文件不是有效的ZIP格式' }
      }
    } catch {
      return { isValid: false, error: '无法读取文件内容' }
    }

    return { isValid: true }
  }

  // 专门用于模板文件的验证函数（50MB限制）
  const validateTemplateZipFile = async (
    file: File,
  ): Promise<{ isValid: boolean; error?: string }> => {
    // 1. 检查文件扩展名
    if (!file.name.toLowerCase().endsWith('.zip')) {
      return { isValid: false, error: '请选择ZIP文件' }
    }

    // 2. 检查文件大小 (50MB for templates)
    const maxSize = 50 * 1024 * 1024 // 50MB
    if (file.size > maxSize) {
      return { isValid: false, error: '文件大小不能超过50MB' }
    }

    // 3. 检查文件是否为空
    if (file.size === 0) {
      return { isValid: false, error: '文件不能为空' }
    }

    // 4. 基本的ZIP文件头验证
    try {
      const arrayBuffer = await file.slice(0, 4).arrayBuffer()
      const header = new Uint8Array(arrayBuffer)

      // ZIP文件魔数检查
      const isZipHeader =
        header[0] === 0x50 &&
        header[1] === 0x4b &&
        ((header[2] === 0x03 && header[3] === 0x04) || // 本地文件头
          (header[2] === 0x05 && header[3] === 0x06) || // 中央目录结束
          (header[2] === 0x07 && header[3] === 0x08)) // 跨度标记

      if (!isZipHeader) {
        return { isValid: false, error: '文件不是有效的ZIP格式' }
      }
    } catch {
      return { isValid: false, error: '无法读取文件内容' }
    }

    return { isValid: true }
  }

  // 获取模板列表
  const fetchTemplates = async () => {
    try {
      const response = await getTemplates()
      const templateList = response.templates
      setTemplates(templateList)

      // 如果有模板，默认选择第一个
      if (templateList.length > 0) {
        setSelectedTemplate(templateList[0])
      }
    } catch (error) {
      console.error('Error fetching templates:', error)
      setTemplates([])
    }
  }

  useEffect(() => {
    // 在组件加载时获取模板列表
    fetchTemplates()
  }, [])

  // 清理轮询定时器
  useEffect(() => {
    return () => {
      if (pollingInterval) {
        clearInterval(pollingInterval)
      }
    }
  }, [pollingInterval])

  // 轮询转换状态
  const startPolling = (conversionId: string) => {
    const interval = setInterval(async () => {
      try {
        const response = await getConversionStatus(conversionId)
        setConversionStatus(response.status)

        // 如果有进度信息，可以添加到日志中
        if (response.progress?.message) {
          setConversionLogs(prev => [...prev, response.progress.message])
        }

        // 如果转换完成或失败，停止轮询
        if (response.status === 'completed' || response.status === 'failed') {
          clearInterval(interval)
          setPollingInterval(null)
          setIsLoading(false)
          onCompileStatusChange?.(false)

          if (response.status === 'completed') {
            // 自动下载文件
            downloadConvertedFile(conversionId)
          } else if (response.status === 'failed') {
            alert('转换失败: ' + (response.progress?.message || '未知错误'))
          }
        }
      } catch (error) {
        console.error('Error polling conversion status:', error)
        clearInterval(interval)
        setPollingInterval(null)
        setIsLoading(false)
        onCompileStatusChange?.(false)
      }
    }, 2000) // 每2秒轮询一次

    setPollingInterval(interval)
  }

  // 下载转换后的文件
  const downloadConvertedFile = async (conversionId: string) => {
    try {
      const response = await downloadConversionResult(conversionId)

      // 创建下载链接
      const link = document.createElement('a')
      link.href = response.downloadUrl
      link.download = response.fileName
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    } catch (error) {
      console.error('Error downloading file:', error)
      alert('下载失败: ' + (error as Error).message)
    }
  }

  // 等待转换完成
  const waitForConversion = async (conversionId: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      const checkStatus = async () => {
        try {
          const response = await getConversionStatus(conversionId)

          if (response.status === 'completed') {
            resolve()
          } else if (response.status === 'failed') {
            reject(
              new Error(
                '转换失败: ' + (response.progress?.message || '未知错误'),
              ),
            )
          } else {
            // 继续等待
            setTimeout(checkStatus, 2000)
          }
        } catch (error) {
          reject(error)
        }
      }

      checkStatus()
    })
  }

  // 转换LaTeX项目 - 现在在点击按钮时调用
  const convertProject = async () => {
    if (!selectedTemplate) {
      alert('请先选择模板')
      return
    }

    if (!uploadedFiles.zipFile) {
      alert('请先上传ZIP文件')
      return
    }

    setIsLoading(true)
    onCompileStatusChange?.(true)
    try {
      const response = await createConversion({
        source: uploadedFiles.zipFile,
        targetTemplateID: selectedTemplate.id,
        taskName: `Convert to ${selectedTemplate.name}`,
      })

      _setCurrentConversionId(response.conversion.id)
      setConversionStatus(response.conversion.status)

      // 开始轮询状态
      startPolling(response.conversion.id)
    } catch (error) {
      console.error('Error converting project:', error)
      alert('转换失败: ' + (error as Error).message)
      setIsLoading(false)
      onCompileStatusChange?.(false)
    }
  }
  // 预览PDF功能 - 预览模板转换后的结果
  const previewPdf = async () => {
    if (!selectedTemplate) {
      alert('请先选择模板')
      return
    }

    if (!uploadedFiles.zipFile) {
      alert('请先上传ZIP文件')
      return
    }

    setIsLoading(true)
    onCompileStatusChange?.(true)
    try {
      // 首先调用模板转换API获取转换后的ZIP文件
      const response = await createConversion({
        source: uploadedFiles.zipFile,
        targetTemplateID: selectedTemplate.id,
        taskName: `Preview ${selectedTemplate.name}`,
      })

      // 等待转换完成
      await waitForConversion(response.conversion.id)

      // 获取转换后的ZIP文件
      const downloadResponse = await downloadConversionResult(
        response.conversion.id,
      )

      // 通过预签名URL获取文件内容
      const fileResponse = await fetch(downloadResponse.downloadUrl)
      if (!fileResponse.ok) {
        throw new Error('Failed to fetch converted file')
      }
      const convertedZipBlob = await fileResponse.blob()

      // 使用JSZip解析转换后的ZIP文件
      const JSZip = (await import('jszip')).default
      const convertedZip = new JSZip()
      const convertedZipContent = await convertedZip.loadAsync(convertedZipBlob) // 提取文件到entries数组
      const entries = []
      const allTexFiles = []

      for (const [path, file] of Object.entries(convertedZipContent.files)) {
        if (!file.dir) {
          const buffer = await file.async('arraybuffer')
          entries.push({ path, buffer })

          // 收集所有.tex文件
          if (path.endsWith('.tex')) {
            allTexFiles.push(path)
          }
        }
      }

      // 智能查找主文件
      let mainTexFile = uploadedFiles.mainTexFile

      // 如果原主文件不存在，尝试查找主文件
      const mainFileExists = entries.some(entry => entry.path === mainTexFile)
      if (!mainFileExists && allTexFiles.length > 0) {
        // 首先尝试查找名为 main.tex 的文件
        const mainFile = allTexFiles.find(
          path => path.endsWith('/main.tex') || path === 'main.tex',
        )
        if (mainFile) {
          mainTexFile = mainFile
        } else {
          // 如果没有main.tex，查找包含原文件名的文件
          const originalBaseName =
            uploadedFiles.mainTexFile.split('/').pop() ||
            uploadedFiles.mainTexFile
          const matchingFile = allTexFiles.find(path =>
            path.endsWith(originalBaseName),
          )
          if (matchingFile) {
            mainTexFile = matchingFile
          } else {
            // 最后使用第一个.tex文件
            mainTexFile = allTexFiles[0]
          }
        }
      }
      console.warn('Found .tex files:', allTexFiles)
      console.warn('Using main file:', mainTexFile)
      console.warn(
        'All files in converted ZIP:',
        entries.map(e => e.path),
      )

      // 编译转换后的LaTeX
      const { compile } = await import('@/lib/compile')
      const result = await compile(entries, {
        engineType:
          selectedTemplate?.recommendedCompile === 'xelatex'
            ? 'xetex'
            : 'pdftex',
        mainFile: mainTexFile,
      })

      if (result.status === 0 && result.pdf) {
        // 编译成功，传递PDF数据给父组件
        onPdfGenerated?.(result.pdf)
      } else {
        throw new Error('编译失败: ' + result.log)
      }
    } catch (error) {
      console.error('Error converting and compiling PDF:', error)
      alert('转换和编译失败: ' + (error as Error).message)
    } finally {
      setIsLoading(false)
      onCompileStatusChange?.(false)
    }
  }

  // 处理模板ZIP文件上传
  const handleTemplateZipUpload = async (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const file = event.target.files?.[0]
    if (!file) {
      return
    }

    try {
      setUploadError('')

      // 验证文件（对于模板使用50MB限制）
      const validation = await validateTemplateZipFile(file)
      if (!validation.isValid) {
        setUploadError(validation.error || '文件验证失败')
        // 清空输入框
        event.target.value = ''
        return
      }

      setTemplateForm(prev => ({ ...prev, templateZip: file }))
    } catch (error) {
      console.error('File selection error:', error)
      setUploadError('文件处理失败')
      event.target.value = ''
    }
  }

  // 上传自定义模板
  const uploadTemplate = async () => {
    if (
      !templateForm.templateName ||
      !templateForm.mainTexFile ||
      !templateForm.recommendedCompile ||
      !templateForm.templateZip
    ) {
      setUploadError('请填写所有必需字段')
      return
    }

    setIsUploadingTemplate(true)
    setUploadError('')

    try {
      // 再次验证文件
      const validation = await validateTemplateZipFile(templateForm.templateZip)
      if (!validation.isValid) {
        setUploadError(validation.error || '文件验证失败')
        return
      }

      // 构建请求对象
      const uploadRequest = {
        template: templateForm.templateZip,
        name: templateForm.templateName,
        description: `Template with main file: ${templateForm.mainTexFile}`,
        category: 'custom',
        version: '1.0',
        author: 'User',
        license: 'MIT',
        tags: [templateForm.recommendedCompile],
        isPublic: true,
      }

      await uploadTemplateAPI(uploadRequest)

      // 重置表单
      setTemplateForm({
        templateName: '',
        mainTexFile: '',
        recommendedCompile: '',
        templateZip: null,
      })

      // 隐藏表单
      setShowUploadForm(false)

      // 重新获取模板列表
      await fetchTemplates()

      alert(`模板 "${templateForm.templateName}" 上传成功！`)
    } catch (error: unknown) {
      console.error('Error uploading template:', error)

      // 处理具体的错误信息
      if (error && typeof error === 'object' && 'response' in error) {
        const responseError = error as {
          response?: { data?: { error?: string } }
        }
        if (responseError.response?.data?.error) {
          const errorMessage = responseError.response.data.error
          if (errorMessage.includes('invalid file format')) {
            setUploadError(
              '文件格式不正确，请确保上传的是包含.tex文件的ZIP文件',
            )
          } else if (errorMessage.includes('file too large')) {
            setUploadError('文件过大，模板文件不能超过50MB')
          } else if (errorMessage.includes('no tex files')) {
            setUploadError('ZIP文件中必须包含至少一个.tex文件')
          } else {
            setUploadError(errorMessage)
          }
        } else {
          setUploadError('上传失败，请重试')
        }
      } else {
        setUploadError(
          '上传失败: ' + (error instanceof Error ? error.message : '未知错误'),
        )
      }
    } finally {
      setIsUploadingTemplate(false)
    }
  }

  const handleZipUpload = async (zipFile: File) => {
    try {
      // 验证文件
      const validation = await validateZipFile(zipFile)
      if (!validation.isValid) {
        alert(validation.error)
        return
      }

      // Create JSZip instance to read the uploaded file
      const zip = new JSZip()
      const zipContent = await zip.loadAsync(zipFile)

      // Extract all files from the zip
      const files = []
      for (const [path, file] of Object.entries(zipContent.files)) {
        if (!file.dir) {
          const fileContent = await file.async('blob')
          files.push({
            path: path,
            file: fileContent,
          })
        }
      }

      // Find .tex files to determine the main file
      const texFiles = files.filter(file => file.path.endsWith('.tex'))
      const mainTexFile = texFiles.length > 0 ? texFiles[0].path : 'main.tex' // Update state with the uploaded file info
      setUploadedFiles({
        zipFile: zipFile,
        mainTexFile: mainTexFile,
      })
    } catch (error) {
      console.error('Error processing ZIP file:', error)
      alert('处理ZIP文件时出错: ' + (error as Error).message)
    }
  }

  return (
    <section className="p-6 w-[750px] max-md:w-full ml-2 overflow-y-auto  max-h-[calc(100vh-72px)]">
      <div className="mb-10">
        <GradientTitle step="Step1" title="Choose Template" />
        <div className="mt-5">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <div // Outer div for gradient border
                className={`mb-4 rounded-lg bg-gradient-to-r from-[#33B4FF] to-[#FFAE00] w-[500px] max-sm:w-full cursor-pointer p-[2px]`}
              >
                <div // Inner div for content and solid background
                  className={`flex justify-between items-center px-5 h-[calc(2.75rem-4px)] text-xs font-medium rounded-[calc(0.5rem-2px)] bg-zinc-50 bg-opacity-60 text-neutral-500 w-full`}
                >
                  <div className="select-text">
                    {selectedTemplate?.name || 'Choose Template'}
                  </div>
                  <i className="ti ti-chevron-down" />
                </div>
              </div>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-[500px]">
              {templates.map(template => (
                <DropdownMenuItem
                  key={template.id}
                  onClick={() => setSelectedTemplate(template)}
                  className="flex flex-col items-start p-3"
                >
                  <div className="font-medium text-sm">{template.name}</div>
                  {template.description && (
                    <div className="text-xs text-gray-500 mt-1">
                      {template.description}
                    </div>
                  )}
                  <div className="flex gap-2 mt-1">
                    {template.category && (
                      <span className="text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded">
                        {template.category}
                      </span>
                    )}
                    {template.author && (
                      <span className="text-xs bg-gray-100 text-gray-600 px-2 py-0.5 rounded">
                        by {template.author}
                      </span>
                    )}
                  </div>
                </DropdownMenuItem>
              ))}
              {templates.length === 0 && (
                <DropdownMenuItem disabled>
                  No templates available
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>{' '}
          <div
            className="ml-2.5 mx-0 mt-3 mb-6 text-sm font-medium underline text-neutral-500 hover:text-sky-500 cursor-pointer"
            onClick={() => setShowUploadForm(!showUploadForm)}
          >
            Didn&apos;t find the template here? Upload your template
          </div>
          {/* 上传模板表单 */}
          {showUploadForm && (
            <div className="bg-gray-100 p-6 rounded-lg mb-6">
              <h3 className="text-lg font-semibold mb-4 text-neutral-700">
                Upload New Template
              </h3>
              {/* 模板名称和主文件名输入框并排 */}
              <div className="mb-4 flex gap-4">
                {/* 模板名称输入框 */}
                <div className="flex-1">
                  <label className="block text-sm font-medium text-neutral-600 mb-2">
                    Template Name
                  </label>
                  <div className="rounded-lg bg-gradient-to-r from-[#33B4FF] to-[#FFAE00] w-full p-[2px]">
                    <input
                      type="text"
                      value={templateForm.templateName}
                      onChange={e =>
                        setTemplateForm(prev => ({
                          ...prev,
                          templateName: e.target.value,
                        }))
                      }
                      placeholder="Enter template name"
                      className="w-full px-4 py-2 text-sm font-medium rounded-[calc(0.5rem-2px)] bg-zinc-50 bg-opacity-60 text-neutral-700 border-none outline-none"
                    />
                  </div>
                </div>
                {/* 主文件名输入框 */}
                <div className="flex-1">
                  <label className="block text-sm font-medium text-neutral-600 mb-2">
                    Main TeX File
                  </label>
                  <div className="rounded-lg bg-gradient-to-r from-[#33B4FF] to-[#FFAE00] w-full p-[2px]">
                    <input
                      type="text"
                      value={templateForm.mainTexFile}
                      onChange={e =>
                        setTemplateForm(prev => ({
                          ...prev,
                          mainTexFile: e.target.value,
                        }))
                      }
                      placeholder="e.g., main.tex"
                      className="w-full px-4 py-2 text-sm font-medium rounded-[calc(0.5rem-2px)] bg-zinc-50 bg-opacity-60 text-neutral-700 border-none outline-none"
                    />
                  </div>
                </div>
              </div>

              {/* 推荐编译方法选择 */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-neutral-600 mb-2">
                  Recommended Compile Method
                </label>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <div className="rounded-lg bg-gradient-to-r from-[#33B4FF] to-[#FFAE00] w-full cursor-pointer p-[2px]">
                      <div className="flex justify-between items-center px-4 py-2 text-sm font-medium rounded-[calc(0.5rem-2px)] bg-zinc-50 bg-opacity-60 text-neutral-500 w-full">
                        <div className="select-text">
                          {templateForm.recommendedCompile
                            ? compileMethods.find(
                                m =>
                                  m.value === templateForm.recommendedCompile,
                              )?.label
                            : 'Choose Compile Method'}
                        </div>
                        <i className="ti ti-chevron-down" />
                      </div>
                    </div>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="w-full">
                    {compileMethods.map(method => (
                      <DropdownMenuItem
                        key={method.value}
                        onClick={() =>
                          setTemplateForm(prev => ({
                            ...prev,
                            recommendedCompile: method.value,
                          }))
                        }
                      >
                        {method.label}
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>

              {/* ZIP文件上传 */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-neutral-600 mb-2">
                  Template ZIP File
                </label>
                <div className="rounded-lg bg-gradient-to-r from-[#33B4FF] to-[#FFAE00] w-full p-[2px]">
                  <div className="rounded-[calc(0.5rem-2px)] bg-zinc-50 bg-opacity-60 p-3">
                    <input
                      type="file"
                      accept=".zip"
                      onChange={handleTemplateZipUpload}
                      className="w-full text-sm text-neutral-700 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-sky-50 file:text-sky-700 hover:file:bg-sky-100 cursor-pointer"
                    />
                    {templateForm.templateZip && (
                      <p className="text-xs text-green-600 mt-2">
                        Selected: {templateForm.templateZip.name} (
                        {(templateForm.templateZip.size / 1024 / 1024).toFixed(
                          2,
                        )}{' '}
                        MB)
                      </p>
                    )}
                    {uploadError && (
                      <p className="text-xs text-red-600 mt-2">{uploadError}</p>
                    )}
                  </div>
                </div>
              </div>

              {/* 按钮组 */}
              <div className="flex gap-3">
                <button
                  onClick={uploadTemplate}
                  disabled={
                    isUploadingTemplate ||
                    !templateForm.templateName ||
                    !templateForm.mainTexFile ||
                    !templateForm.recommendedCompile ||
                    !templateForm.templateZip
                  }
                  className="inline-flex gap-2.5 items-center px-6 py-2 text-base font-semibold text-white bg-sky-500 rounded-[50px] hover:bg-sky-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span>
                    {isUploadingTemplate ? 'Uploading...' : 'Add Template'}
                  </span>
                  <Image
                    src={RightArrow}
                    alt="Right Arrow"
                    width={20}
                    height={20}
                    className="w-6 h-6"
                    style={{ filter: 'brightness(0) invert(1)' }}
                  />
                </button>
                <button
                  onClick={() => setShowUploadForm(false)}
                  className="px-6 py-2 text-base font-semibold text-neutral-600 bg-gray-200 rounded-[50px] hover:bg-gray-300 transition-colors"
                >
                  Cancel
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
      <div className="mb-10">
        <GradientTitle step="Step2" title="Upload LaTeX ZIP Package" />
        <div
          className="flex flex-col justify-center items-center mb-8 cursor-pointer rounded-xl border-2 border-sky-400 border-dashed bg-zinc-50 bg-opacity-60 h-[120px] w-[500px] max-sm:w-full transition-colors hover:bg-sky-50 relative"
          onClick={() => document.getElementById('zip-upload')?.click()}
          onDragOver={e => {
            e.preventDefault()
            e.stopPropagation()
          }}
          onDrop={async e => {
            e.preventDefault()
            e.stopPropagation()
            const files = Array.from(e.dataTransfer.files)
            const zipFile = files.find(
              file =>
                file.type === 'application/zip' || file.name.endsWith('.zip'),
            )
            if (zipFile) {
              await handleZipUpload(zipFile)
            } else {
              alert('请上传ZIP文件')
            }
          }}
        >
          {uploadedFiles.zipFile ? (
            <div className="text-center p-4">
              <p className="text-sm font-medium text-green-600">
                {uploadedFiles.zipFile.name}
              </p>
              {uploadedFiles.mainTexFile && (
                <p className="text-xs text-neutral-500 mt-1">
                  Main file: {uploadedFiles.mainTexFile}
                </p>
              )}
              <p className="text-xs text-neutral-400 mt-2">
                Click to upload a different file
              </p>
            </div>
          ) : (
            <>
              <div className="mb-2 text-3xl text-neutral-500">+</div>
              <p className="text-sm font-medium text-neutral-500">
                Click or drag & drop your LaTeX ZIP package here
              </p>
            </>
          )}
          <input
            id="zip-upload"
            type="file"
            accept=".zip"
            className="hidden"
            onChange={async e => {
              const file = e.target.files?.[0]
              if (file) {
                await handleZipUpload(file)
              }
            }}
          />
        </div>
      </div>
      <div className="mb-10">
        <GradientTitle step="Step3" title="Preview and Download" />

        {/* 转换状态显示 */}
        {conversionStatus && (
          <div className="mb-6 p-4 rounded-lg border">
            <div className="flex items-center gap-2 mb-2">
              <div
                className={`w-3 h-3 rounded-full ${
                  conversionStatus === 'pending'
                    ? 'bg-yellow-400'
                    : conversionStatus === 'in_progress'
                      ? 'bg-blue-400 animate-pulse'
                      : conversionStatus === 'completed'
                        ? 'bg-green-400'
                        : conversionStatus === 'failed'
                          ? 'bg-red-400'
                          : 'bg-gray-400'
                }`}
              />
              <span className="font-medium text-sm">
                转换状态:{' '}
                {conversionStatus === 'pending'
                  ? '等待中'
                  : conversionStatus === 'in_progress'
                    ? '处理中'
                    : conversionStatus === 'completed'
                      ? '已完成'
                      : conversionStatus === 'failed'
                        ? '失败'
                        : conversionStatus}
              </span>
            </div>

            {conversionLogs.length > 0 && (
              <div className="mt-3">
                <div className="text-xs font-medium text-gray-600 mb-1">
                  处理日志:
                </div>
                <div className="bg-gray-50 p-2 rounded text-xs max-h-32 overflow-y-auto">
                  {conversionLogs.map((log, index) => (
                    <div key={index} className="mb-1">
                      {log}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        <div className="mt-5 flex gap-4">
          <button
            className="inline-flex gap-2.5 items-center px-6 py-2 text-base font-semibold text-white bg-green-500 rounded-[50px] hover:bg-green-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={isLoading || !selectedTemplate || !uploadedFiles.zipFile}
            onClick={previewPdf}
          >
            <span>
              {isLoading
                ? 'Converting & Compiling...'
                : 'Preview Converted PDF'}
            </span>
            <Image
              src={RightArrow}
              alt="Right Arrow"
              width={20}
              height={20}
              className="w-6 h-6"
              style={{ filter: 'brightness(0) invert(1)' }}
            />
          </button>
          <button
            className="inline-flex gap-2.5 items-center px-6 py-2 text-base font-semibold text-white bg-sky-500 rounded-[50px] hover:bg-sky-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={isLoading || !selectedTemplate || !uploadedFiles.zipFile}
            onClick={convertProject}
          >
            <span>{isLoading ? 'Processing...' : 'Download Template'}</span>
            <Image
              src={RightArrow}
              alt="Right Arrow"
              width={20}
              height={20}
              className="w-6 h-6"
              style={{ filter: 'brightness(0) invert(1)' }}
            />
          </button>
        </div>
      </div>
    </section>
  )
}

export default LeftPanel
