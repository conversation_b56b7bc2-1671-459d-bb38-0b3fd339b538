import { Server } from '@hocuspocus/server'
import { Database } from '@hocuspocus/extension-database'
import dotenv from 'dotenv'
import {
  GetObjectCommand,
  PutObjectCommand,
  S3Client,
} from '@aws-sdk/client-s3'
import { AssetNode, dfs, FileNode, isAssetNode } from './utils'
import * as Y from 'yjs'

dotenv.config()

const documentFolder = 'arxtect-documents'
const scheme = process.env.OSS_SECURE === 'true' ? 'https' : 'http'
const endpoint = `${scheme}://${process.env.OSS_HOST}`
const region = process.env.OSS_HOST?.split('.')[0] || 'deprecated'

const s3Client = new S3Client({
  region,
  endpoint: endpoint,
  forcePathStyle: process.env.OSS_FORCE_PATH_STYLE === 'true',
  credentials: {
    accessKeyId: process.env.OSS_ACCESS_KEY || '',
    secretAccessKey: process.env.OSS_SECRET_KEY || '',
  },
})

const server = Server.configure({
  port: 8080,
  async onAuthenticate(req) {
    const authApi = `${process.env.EINSTEIN_URL}/projects/${req.documentName}/access`
    const res = await fetch(authApi, {
      headers: {
        Cookie: req.requestHeaders.cookie || '',
      },
    })

    if (!res.ok) {
      const body = await res.text()
      throw new Error(`Failed to authenticate: ${res.status} ${body}`)
    }

    const data = await res.json()
    req.connection.readOnly = data.readOnly
    return {
      user: data.user,
    }
  },
  extensions: [
    new Database({
      fetch: async ({ documentName }) => {
        try {
          const res = await s3Client.send(
            new GetObjectCommand({
              Bucket: process.env.OSS_BUCKET,
              Key: `${documentFolder}/${documentName}`,
            }),
          )

          if (!res.Body) {
            throw new Error('When fetch S3: No body in response')
          }

          return res.Body.transformToByteArray()
        } catch (err) {
          if (err instanceof Error && err.name === 'NoSuchKey') {
            return null
          }
          throw err
        }
      },
      store: async ({ documentName, state }) => {
        const document = new Y.Doc()
        Y.applyUpdate(document, state)
        const fileIndex = document.getMap<FileNode>('PROJECT_INDEX')
        const assets: AssetNode[] = []
        if (fileIndex) {
          dfs(fileIndex, node => {
            if (isAssetNode(node)) {
              assets.push(node)
            }
          })
        }
        document.destroy()

        const updateAssetsApi = `${process.env.EINSTEIN_URL}/projects/${documentName}/assets`
        await fetch(updateAssetsApi, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${process.env.API_SECRET}`,
          },
          body: JSON.stringify({
            assets: assets.map(asset => asset.hash),
            documentSize: state.byteLength,
          }),
        })
        await s3Client.send(
          new PutObjectCommand({
            Bucket: process.env.OSS_BUCKET,
            Key: `${documentFolder}/${documentName}`,
            Body: state,
          }),
        )
      },
    }),
  ],
})

server.listen()
