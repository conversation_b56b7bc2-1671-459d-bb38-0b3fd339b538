'use client'

import React, { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { PlusIcon } from 'lucide-react'
import { toast } from 'sonner'

import { <PERSON><PERSON> } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import createExperiment, {
  CreateExperimentParams,
} from '@/services/ai-scientist/create-experiment'
import { EXPERIMENT_NAMES } from '../constant'
import { invalidateExperimentStatus } from '@/services/ai-scientist/get-experiment-status'

// Zod schema for form validation
const researchFormSchema = z.object({
  experiment: z.enum(EXPERIMENT_NAMES, {
    required_error: 'Please select an experiment type.',
  }),
  model: z.string().min(1, 'Please enter a model name.'),
  num_ideas: z
    .number()
    .min(1, 'Must be at least 1')
    .max(10, 'Must be at most 10'),
  natural_language_prompt: z.string(),
  writeup: z.literal('latex'),
  parallel: z.number().min(0, 'Must be 0 or greater'),
  improvement: z.boolean(),
  gpus: z.string().optional(),
  engine: z.enum(['semanticscholar', 'openalex'], {
    required_error: 'Please select a search engine.',
  }),
  skip_idea_generation: z.boolean(),
  skip_novelty_check: z.boolean(),
  force_rerun: z.boolean(),
  max_experiments: z.number().min(1, 'Must be at least 1').optional(),
})

type ResearchFormValues = z.infer<typeof researchFormSchema>

// Default values
const defaultValues: ResearchFormValues = {
  experiment: '2d_diffusion',
  model: 'gpt-4o-mini',
  num_ideas: 1,
  natural_language_prompt: '',
  writeup: 'latex',
  parallel: 1,
  improvement: false,
  gpus: '',
  engine: 'semanticscholar',
  skip_idea_generation: false,
  skip_novelty_check: false,
  force_rerun: true,
  max_experiments: undefined,
}

// AI model options
const modelOptions = [
  'gpt-4o-mini',
  'gpt-4o-2024-05-13',
  'claude-3-5-sonnet-20240620',
  'claude-3-5-haiku-20241022',
  'gpt-4-turbo',
  'gpt-3.5-turbo',
]

export function NewResearchButton() {
  const [open, setOpen] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)

  const form = useForm<ResearchFormValues>({
    resolver: zodResolver(researchFormSchema),
    defaultValues,
  })

  const onSubmit = async (data: ResearchFormValues) => {
    setIsSubmitting(true)
    try {
      // Convert form data to the service parameters
      const experimentParams: CreateExperimentParams = {
        ...data,
        gpus: data.gpus || undefined, // Convert empty string to undefined
      }

      await createExperiment(experimentParams)
      toast.success('Research started successfully!')
      setOpen(false)
      form.reset()
      invalidateExperimentStatus()
    } catch (error) {
      console.error('Error starting research:', error)
      toast.error(
        'Failed to start research. Please check your connection and configuration.',
      )
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button>
          <PlusIcon className="size-4" />
          New Research
        </Button>
      </DialogTrigger>
      <DialogContent
        className="max-w-2xl max-h-[90vh] overflow-y-auto"
        onInteractOutside={e => e.preventDefault()}
      >
        <DialogHeader>
          <DialogTitle>Trigger Research</DialogTitle>
          <DialogDescription>
            Configure the parameters for your AI research experiment.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Experiment Type */}
              <FormField
                control={form.control}
                name="experiment"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Experiment Type</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select experiment type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {EXPERIMENT_NAMES.map(experiment => (
                          <SelectItem key={experiment} value={experiment}>
                            {experiment}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Choose the type of experiment to run.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Model */}
              <FormField
                control={form.control}
                name="model"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>AI Model</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select AI model" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {modelOptions.map(model => (
                          <SelectItem key={model} value={model}>
                            {model}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Choose the AI model for generating ideas.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Number of Ideas */}
              <FormField
                control={form.control}
                name="num_ideas"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Number of Ideas</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min={1}
                        max={10}
                        {...field}
                        onChange={e =>
                          field.onChange(parseInt(e.target.value) || 1)
                        }
                      />
                    </FormControl>
                    <FormDescription>
                      Number of research ideas to generate (1-10).
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Parallel Processes */}
              <FormField
                control={form.control}
                name="parallel"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Parallel Processes</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min={0}
                        {...field}
                        onChange={e =>
                          field.onChange(parseInt(e.target.value) || 0)
                        }
                      />
                    </FormControl>
                    <FormDescription>
                      Number of parallel processes (0 for sequential).
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Max Experiments */}
              <FormField
                control={form.control}
                name="max_experiments"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Max Experiments (Optional)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min={1}
                        placeholder="Leave empty for no limit"
                        {...field}
                        value={field.value || ''}
                        onChange={e => {
                          const value = e.target.value
                          field.onChange(
                            value ? parseInt(value) || undefined : undefined,
                          )
                        }}
                      />
                    </FormControl>
                    <FormDescription>
                      Maximum number of experiments to run from generated ideas.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Search Engine */}
              <FormField
                control={form.control}
                name="engine"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Search Engine</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select search engine" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="semanticscholar">
                          Semantic Scholar
                        </SelectItem>
                        <SelectItem value="openalex">OpenAlex</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Academic search engine for literature review.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* GPU IDs */}
              <FormField
                control={form.control}
                name="gpus"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>GPU IDs (Optional)</FormLabel>
                    <FormControl>
                      <Input placeholder="0,1,2" {...field} />
                    </FormControl>
                    <FormDescription>
                      Comma-separated GPU IDs to use (e.g., &quot;0,1,2&quot;).
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Natural Language Prompt */}
            <FormField
              control={form.control}
              name="natural_language_prompt"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Research Prompt</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Describe what you want to research, e.g., 'Explore different diffusion model architectures for improved generation quality.'"
                      className="min-h-20"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Natural language description to guide the research idea
                    generation.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Boolean Options */}
            <div className="space-y-4">
              <h3 className="text-sm font-medium">Options</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="improvement"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Improvement Mode</FormLabel>
                        <FormDescription>
                          Improve based on previous comments.
                        </FormDescription>
                      </div>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="skip_idea_generation"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Skip Idea Generation</FormLabel>
                        <FormDescription>
                          Skip the idea generation phase.
                        </FormDescription>
                      </div>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="skip_novelty_check"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Skip Novelty Check</FormLabel>
                        <FormDescription>
                          Skip checking idea novelty.
                        </FormDescription>
                      </div>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="force_rerun"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Force Rerun</FormLabel>
                        <FormDescription>
                          Force rerun completed experiments.
                        </FormDescription>
                      </div>
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <div className="flex justify-end space-x-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => setOpen(false)}
              >
                Cancel
              </Button>
              <Button type="submit" loading={isSubmitting}>
                Start Research
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
