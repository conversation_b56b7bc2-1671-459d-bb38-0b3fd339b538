import { ComponentProps } from 'react'
import Image from 'next/image'
import { getSize, IconSize } from './icon-sizes'

export default function ChatTable(
  props: Partial<ComponentProps<typeof Image>> & {
    size?: IconSize | [number, number]
  },
) {
  const [width, height] = Array.isArray(props.size)
    ? props.size
    : getSize(props.size)
  return (
    <Image
      src="/assets/chat/chat-table.svg"
      alt="ChatTable"
      width={width}
      height={height}
      {...props}
    />
  )
}
