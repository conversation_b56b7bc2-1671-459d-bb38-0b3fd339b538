'use client'
import React, { useEffect } from 'react'
import _posthog from 'posthog-js'
import { PostHogProvider } from 'posthog-js/react'
import { useGetMe } from '@/services/auth/get-me'

function PostHogUserIdentifier() {
  const { data: user } = useGetMe()

  useEffect(() => {
    if (user?.id) {
      _posthog.identify(user.id, {
        email: user.email,
        name: user.name,
        role: user.role,
      })
    } else {
      _posthog.reset()
    }
  }, [user])

  return null
}

// PostHog Provider
export function PHProvider({ children }: { children: React.ReactNode }) {
  return (
    <PostHogProvider client={_posthog}>
      <PostHogUserIdentifier />
      {children}
    </PostHogProvider>
  )
}

export const posthog = _posthog
