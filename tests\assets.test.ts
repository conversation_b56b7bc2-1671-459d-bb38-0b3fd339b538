import { test } from 'vitest'
import * as Y from 'yjs'

function revertToState(doc: Y.Doc, state: Uint8Array) {
  const tempDoc = new Y.Doc()
  Y.applyUpdate(tempDoc, state)
  const undoManager = new Y.UndoManager(tempDoc)
  const currentState = Y.encodeStateAsUpdate(doc)
  Y.applyUpdate(tempDoc, currentState)
  undoManager.undo()
  const revertedState = Y.encodeStateAsUpdate(tempDoc)
  const mergedUpdate = Y.mergeUpdates([state, revertedState])
  Y.applyUpdate(doc, mergedUpdate)
  tempDoc.destroy()
  undoManager.destroy()
}

test('test yproject revert', async () => {
  const doc = new Y.Doc()
  doc.getArray('test').push(['a', 'b', 'c'])
  const state0 = Y.encodeStateAsUpdate(doc)
  doc.getArray('test').push(['d', 'e', 'f'])
  revertToState(doc, state0)
  const result = doc.getArray('test').toArray()
  if (result.join(',') !== 'a,b,c') {
    throw new Error(`Expected 'a,b,c', but got '${result.join(',')}'`)
  }

  doc.destroy()
})
