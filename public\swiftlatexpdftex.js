/* eslint-disable */
var Module = typeof Module != 'undefined' ? Module : {}
var ENVIRONMENT_IS_WEB = typeof window == 'object'
var ENVIRONMENT_IS_WORKER = typeof WorkerGlobalScope != 'undefined'
var ENVIRONMENT_IS_NODE =
  typeof process == 'object' &&
  typeof process.versions == 'object' &&
  typeof process.versions.node == 'string' &&
  process.type != 'renderer'
if (ENVIRONMENT_IS_NODE) {
}
const TEXCACHEROOT = '/tex'
const TEXPKGCACHEROOT = '/tex/pkg'
const WORKROOT = '/work'
var Module = {}
self.memlog = ''
self.initmem = undefined
self.mainfile = 'main.tex'
self.texlive_endpoint =
  'https://magic.pointer.ai/latex3/arxtect_version_20250626/'
self.ctan_mirror = 'https://mirrors.ustc.edu.cn/CTAN/'
Module['print'] = function (a) {
  self.memlog += a + '\n'
}
Module['printErr'] = function (a) {
  self.memlog += a + '\n'
  console.log(a)
}
Module['preRun'] = function () {
  FS.mkdir(TEXCACHEROOT)
  FS.mkdir(TEXPKGCACHEROOT)
  FS.mkdir(WORKROOT)
}
function _allocate(content) {
  let res = _malloc(content.length)
  HEAPU8.set(new Uint8Array(content), res)
  return res
}
function dumpHeapMemory() {
  var src = wasmMemory.buffer
  var dst = new Uint8Array(src.byteLength)
  dst.set(new Uint8Array(src))
  return dst
}
function restoreHeapMemory() {
  if (self.initmem) {
    var dst = new Uint8Array(wasmMemory.buffer)
    dst.set(self.initmem)
  }
}
function closeFSStreams() {
  for (var i = 0; i < FS.streams.length; i++) {
    var stream = FS.streams[i]
    if (!stream || stream.fd <= 2) {
      continue
    }
    FS.close(stream)
  }
}
function prepareExecutionContext() {
  self.memlog = ''
  restoreHeapMemory()
  closeFSStreams()
  FS.chdir(WORKROOT)
}
Module['postRun'] = function () {
  self.postMessage({ result: 'ok' })
  self.initmem = dumpHeapMemory()
}
function cleanDir(dir) {
  let l = FS.readdir(dir)
  for (let i in l) {
    let item = l[i]
    if (item === '.' || item === '..') {
      continue
    }
    item = dir + '/' + item
    let fsStat = undefined
    try {
      fsStat = FS.stat(item)
    } catch (err) {
      console.error('Not able to fsstat ' + item)
      continue
    }
    if (FS.isDir(fsStat.mode)) {
      cleanDir(item)
    } else {
      try {
        FS.unlink(item)
      } catch (err) {
        console.error('Not able to unlink ' + item)
      }
    }
  }
  if (dir !== WORKROOT) {
    try {
      FS.rmdir(dir)
    } catch (err) {
      console.error('Not able to top level ' + dir)
    }
  }
}
Module['onAbort'] = function () {
  self.memlog += 'Engine crashed'
  self.postMessage({
    result: 'failed',
    status: -254,
    log: self.memlog,
    cmd: 'compile',
  })
  return
}
function compileLaTeXRoutine() {
  prepareExecutionContext()
  const setMainFunction = cwrap('setMainEntry', 'number', ['string'])
  setMainFunction(self.mainfile)
  let status = _compileLaTeX()
  if (status === 0) {
    let pdfArrayBuffer = null
    _compileBibLatex()
    let pdfurl =
      WORKROOT +
      '/' +
      self.mainfile.substr(0, self.mainfile.length - 4) +
      '.pdf'
    try {
      pdfArrayBuffer = FS.readFile(pdfurl, { encoding: 'binary' })
    } catch (err) {
      console.error('Fetch content failed. ' + pdfurl)
      status = -253
      self.postMessage({
        result: 'failed',
        status,
        log: self.memlog,
        cmd: 'compile',
      })
      return
    }
    self.postMessage(
      {
        result: 'ok',
        status,
        log: self.memlog,
        pdf: pdfArrayBuffer.buffer,
        cmd: 'compile',
      },
      [pdfArrayBuffer.buffer],
    )
  } else {
    let pdfArrayBuffer = null
    let pdfurl =
      WORKROOT +
      '/' +
      self.mainfile.substr(0, self.mainfile.length - 4) +
      '.pdf'
    try {
      _compileBibLatex()
      pdfArrayBuffer = FS.readFile(pdfurl, { encoding: 'binary' })
    } catch (err) {
      console.error('Fetch content failed. ' + pdfurl)
      status = -253
      self.postMessage({
        result: 'failed',
        status,
        log: self.memlog,
        cmd: 'compile',
      })
      return
    }
    console.error('Compilation failed, with status code ' + status)
    self.postMessage(
      {
        result: 'failed',
        status,
        log: self.memlog,
        pdf: pdfArrayBuffer.buffer,
        cmd: 'compile',
      },
      [pdfArrayBuffer.buffer],
    )
  }
}
function compileFormatRoutine() {
  prepareExecutionContext()
  let status = _compileFormat()
  let pdfArrayBuffer = null
  let pdfurl = WORKROOT + '/pdflatex.fmt'
  try {
    pdfArrayBuffer = FS.readFile(pdfurl, { encoding: 'binary' })
  } catch (err) {
    console.error('Fetch content failed.' + pdfurl)
    status = -253
    self.postMessage({
      result: 'failed',
      status,
      log: self.memlog,
      cmd: 'compile',
    })
    return
  }
  if (status === 0) {
    self.postMessage(
      {
        result: 'ok',
        status,
        log: self.memlog,
        pdf: pdfArrayBuffer.buffer,
        cmd: 'compile',
      },
      [pdfArrayBuffer.buffer],
    )
  } else {
    console.error('Compilation format failed, with status code ' + status)
    self.postMessage(
      {
        result: 'failed',
        status,
        log: self.memlog,
        pdf: pdfArrayBuffer.buffer,
        cmd: 'compile',
      },
      [pdfArrayBuffer.buffer],
    )
  }
}
function mkdirRoutine(dirname) {
  try {
    FS.mkdir(WORKROOT + '/' + dirname)
    self.postMessage({ result: 'ok', cmd: 'mkdir' })
  } catch (err) {
    console.error('Not able to mkdir ' + dirname)
    self.postMessage({ result: 'failed', cmd: 'mkdir' })
  }
}
function writeFileRoutine(filename, content) {
  try {
    FS.writeFile(WORKROOT + '/' + filename, content)
    self.postMessage({ result: 'ok', cmd: 'writefile' })
  } catch (err) {
    console.error('Unable to write mem file')
    self.postMessage({ result: 'failed', cmd: 'writefile' })
  }
}
function setTexliveEndpoint(url) {
  if (url) {
    if (!url.endsWith('/')) {
      url += '/'
    }
    self.texlive_endpoint = url
  }
}
self['onmessage'] = function (ev) {
  let data = ev['data']
  let cmd = data['cmd']
  if (cmd === 'compilelatex') {
    compileLaTeXRoutine()
  } else if (cmd === 'compileformat') {
    compileFormatRoutine()
  } else if (cmd === 'settexliveurl') {
    setTexliveEndpoint(data['url'])
  } else if (cmd === 'mkdir') {
    mkdirRoutine(data['url'])
  } else if (cmd === 'writefile') {
    writeFileRoutine(data['url'], data['src'])
  } else if (cmd === 'setmainfile') {
    self.mainfile = data['url']
  } else if (cmd === 'grace') {
    console.error('Gracefully Close')
    self.close()
  } else if (cmd === 'flushcache') {
    cleanDir(WORKROOT)
  } else {
    console.error('Unknown command ' + cmd)
  }
}
let texlive404_cache = {}
let texlive200_cache = {}
function kpse_find_file_impl(nameptr, format, _mustexist) {
  const reqname = UTF8ToString(nameptr)
  if (reqname.includes('/')) {
    return 0
  }
  const cacheKey = format + '/' + reqname
  if (cacheKey in texlive404_cache) {
    return 0
  }
  if (cacheKey in texlive200_cache) {
    const savepath = texlive200_cache[cacheKey]
    return _allocate(intArrayFromString(savepath))
  }
  const remote_url = self.texlive_endpoint + 'pdftex/' + cacheKey
  let xhr = new XMLHttpRequest()
  xhr.open('GET', remote_url, false)
  xhr.timeout = 15e4
  xhr.responseType = 'arraybuffer'
  console.log('Start downloading texlive file ' + remote_url)
  try {
    xhr.send()
  } catch (err) {
    console.log('TexLive Download Failed ' + remote_url)
    return 0
  }
  if (xhr.status === 200) {
    let arraybuffer = xhr.response
    const fileid = xhr.getResponseHeader('fileid')
    const savepath = TEXCACHEROOT + '/' + fileid
    FS.writeFile(savepath, new Uint8Array(arraybuffer))
    texlive200_cache[cacheKey] = savepath
    return _allocate(intArrayFromString(savepath))
  } else if (xhr.status === 301) {
    console.log('TexLive File not exists ' + remote_url)
    texlive404_cache[cacheKey] = 1
    return 0
  }
  return 0
}
let pk404_cache = {}
let pk200_cache = {}
function kpse_find_pk_impl(nameptr, dpi) {
  const reqname = UTF8ToString(nameptr)
  if (reqname.includes('/')) {
    return 0
  }
  const cacheKey = dpi + '/' + reqname
  if (cacheKey in pk404_cache) {
    return 0
  }
  if (cacheKey in pk200_cache) {
    const savepath = pk200_cache[cacheKey]
    return _allocate(intArrayFromString(savepath))
  }
  const remote_url = self.texlive_endpoint + 'pdftex/pk/' + cacheKey
  let xhr = new XMLHttpRequest()
  xhr.open('GET', remote_url, false)
  xhr.timeout = 15e4
  xhr.responseType = 'arraybuffer'
  console.log('Start downloading texlive file ' + remote_url)
  try {
    xhr.send()
  } catch (err) {
    console.log('TexLive Download Failed ' + remote_url)
    return 0
  }
  if (xhr.status === 200) {
    let arraybuffer = xhr.response
    const pkid = xhr.getResponseHeader('pkid')
    const savepath = TEXCACHEROOT + '/' + pkid
    FS.writeFile(savepath, new Uint8Array(arraybuffer))
    pk200_cache[cacheKey] = savepath
    return _allocate(intArrayFromString(savepath))
  } else if (xhr.status === 301) {
    console.log('TexLive File not exists ' + remote_url)
    pk404_cache[cacheKey] = 1
    return 0
  }
  return 0
}
var moduleOverrides = Object.assign({}, Module)
var arguments_ = []
var thisProgram = './this.program'
var quit_ = (status, toThrow) => {
  throw toThrow
}
var scriptDirectory = ''
function locateFile(path) {
  if (Module['locateFile']) {
    return Module['locateFile'](path, scriptDirectory)
  }
  return scriptDirectory + path
}
var readAsync, readBinary
if (ENVIRONMENT_IS_NODE) {
  var fs = require('fs')
  var nodePath = require('path')
  scriptDirectory = __dirname + '/'
  readBinary = filename => {
    filename = isFileURI(filename) ? new URL(filename) : filename
    var ret = fs.readFileSync(filename)
    return ret
  }
  readAsync = async (filename, binary = true) => {
    filename = isFileURI(filename) ? new URL(filename) : filename
    var ret = fs.readFileSync(filename, binary ? undefined : 'utf8')
    return ret
  }
  if (!Module['thisProgram'] && process.argv.length > 1) {
    thisProgram = process.argv[1].replace(/\\/g, '/')
  }
  arguments_ = process.argv.slice(2)
  if (typeof module != 'undefined') {
    module['exports'] = Module
  }
  quit_ = (status, toThrow) => {
    process.exitCode = status
    throw toThrow
  }
} else if (ENVIRONMENT_IS_WEB || ENVIRONMENT_IS_WORKER) {
  if (ENVIRONMENT_IS_WORKER) {
    scriptDirectory = self.location.href
  } else if (typeof document != 'undefined' && document.currentScript) {
    scriptDirectory = document.currentScript.src
  }
  if (scriptDirectory.startsWith('blob:')) {
    scriptDirectory = ''
  } else {
    scriptDirectory = scriptDirectory.slice(
      0,
      scriptDirectory.replace(/[?#].*/, '').lastIndexOf('/') + 1,
    )
  }
  {
    if (ENVIRONMENT_IS_WORKER) {
      readBinary = url => {
        var xhr = new XMLHttpRequest()
        xhr.open('GET', url, false)
        xhr.responseType = 'arraybuffer'
        xhr.send(null)
        return new Uint8Array(xhr.response)
      }
    }
    readAsync = async url => {
      if (isFileURI(url)) {
        return new Promise((resolve, reject) => {
          var xhr = new XMLHttpRequest()
          xhr.open('GET', url, true)
          xhr.responseType = 'arraybuffer'
          xhr.onload = () => {
            if (xhr.status == 200 || (xhr.status == 0 && xhr.response)) {
              resolve(xhr.response)
              return
            }
            reject(xhr.status)
          }
          xhr.onerror = reject
          xhr.send(null)
        })
      }
      var response = await fetch(url, { credentials: 'same-origin' })
      if (response.ok) {
        return response.arrayBuffer()
      }
      throw new Error(response.status + ' : ' + response.url)
    }
  }
} else {
}
var out = Module['print'] || console.log.bind(console)
var err = Module['printErr'] || console.error.bind(console)
Object.assign(Module, moduleOverrides)
moduleOverrides = null
if (Module['arguments']) arguments_ = Module['arguments']
if (Module['thisProgram']) thisProgram = Module['thisProgram']
var wasmBinary = Module['wasmBinary']
var wasmMemory
var ABORT = false
var EXITSTATUS
var HEAP8,
  HEAPU8,
  HEAP16,
  HEAPU16,
  HEAP32,
  HEAPU32,
  HEAPF32,
  HEAP64,
  HEAPU64,
  HEAPF64
var runtimeInitialized = false
var isFileURI = filename => filename.startsWith('file://')
function updateMemoryViews() {
  var b = wasmMemory.buffer
  Module['HEAP8'] = HEAP8 = new Int8Array(b)
  Module['HEAP16'] = HEAP16 = new Int16Array(b)
  Module['HEAPU8'] = HEAPU8 = new Uint8Array(b)
  Module['HEAPU16'] = HEAPU16 = new Uint16Array(b)
  Module['HEAP32'] = HEAP32 = new Int32Array(b)
  Module['HEAPU32'] = HEAPU32 = new Uint32Array(b)
  Module['HEAPF32'] = HEAPF32 = new Float32Array(b)
  Module['HEAPF64'] = HEAPF64 = new Float64Array(b)
  Module['HEAP64'] = HEAP64 = new BigInt64Array(b)
  Module['HEAPU64'] = HEAPU64 = new BigUint64Array(b)
}
function preRun() {
  if (Module['preRun']) {
    if (typeof Module['preRun'] == 'function')
      Module['preRun'] = [Module['preRun']]
    while (Module['preRun'].length) {
      addOnPreRun(Module['preRun'].shift())
    }
  }
  callRuntimeCallbacks(onPreRuns)
}
function initRuntime() {
  runtimeInitialized = true
  if (!Module['noFSInit'] && !FS.initialized) FS.init()
  TTY.init()
  wasmExports['ia']()
  FS.ignorePermissions = false
}
function preMain() {}
function postRun() {
  if (Module['postRun']) {
    if (typeof Module['postRun'] == 'function')
      Module['postRun'] = [Module['postRun']]
    while (Module['postRun'].length) {
      addOnPostRun(Module['postRun'].shift())
    }
  }
  callRuntimeCallbacks(onPostRuns)
}
var runDependencies = 0
var dependenciesFulfilled = null
function getUniqueRunDependency(id) {
  return id
}
function addRunDependency(id) {
  runDependencies++
  Module['monitorRunDependencies']?.(runDependencies)
}
function removeRunDependency(id) {
  runDependencies--
  Module['monitorRunDependencies']?.(runDependencies)
  if (runDependencies == 0) {
    if (dependenciesFulfilled) {
      var callback = dependenciesFulfilled
      dependenciesFulfilled = null
      callback()
    }
  }
}
function abort(what) {
  Module['onAbort']?.(what)
  what = 'Aborted(' + what + ')'
  err(what)
  ABORT = true
  what += '. Build with -sASSERTIONS for more info.'
  var e = new WebAssembly.RuntimeError(what)
  throw e
}
var wasmBinaryFile
function findWasmBinary() {
  return locateFile('swiftlatexpdftex.wasm')
}
function getBinarySync(file) {
  if (file == wasmBinaryFile && wasmBinary) {
    return new Uint8Array(wasmBinary)
  }
  if (readBinary) {
    return readBinary(file)
  }
  throw 'both async and sync fetching of the wasm failed'
}
async function getWasmBinary(binaryFile) {
  if (!wasmBinary) {
    try {
      var response = await readAsync(binaryFile)
      return new Uint8Array(response)
    } catch {}
  }
  return getBinarySync(binaryFile)
}
async function instantiateArrayBuffer(binaryFile, imports) {
  try {
    var binary = await getWasmBinary(binaryFile)
    var instance = await WebAssembly.instantiate(binary, imports)
    return instance
  } catch (reason) {
    err(`failed to asynchronously prepare wasm: ${reason}`)
    abort(reason)
  }
}
async function instantiateAsync(binary, binaryFile, imports) {
  if (
    !binary &&
    typeof WebAssembly.instantiateStreaming == 'function' &&
    !isFileURI(binaryFile) &&
    !ENVIRONMENT_IS_NODE
  ) {
    try {
      var response = fetch(binaryFile, { credentials: 'same-origin' })
      var instantiationResult = await WebAssembly.instantiateStreaming(
        response,
        imports,
      )
      return instantiationResult
    } catch (reason) {
      err(`wasm streaming compile failed: ${reason}`)
      err('falling back to ArrayBuffer instantiation')
    }
  }
  return instantiateArrayBuffer(binaryFile, imports)
}
function getWasmImports() {
  return { a: wasmImports }
}
async function createWasm() {
  function receiveInstance(instance, module) {
    wasmExports = instance.exports
    wasmMemory = wasmExports['ha']
    updateMemoryViews()
    wasmTable = wasmExports['oa']
    removeRunDependency('wasm-instantiate')
    return wasmExports
  }
  addRunDependency('wasm-instantiate')
  function receiveInstantiationResult(result) {
    return receiveInstance(result['instance'])
  }
  var info = getWasmImports()
  if (Module['instantiateWasm']) {
    return new Promise((resolve, reject) => {
      Module['instantiateWasm'](info, (mod, inst) => {
        receiveInstance(mod, inst)
        resolve(mod.exports)
      })
    })
  }
  wasmBinaryFile ??= findWasmBinary()
  var result = await instantiateAsync(wasmBinary, wasmBinaryFile, info)
  var exports = receiveInstantiationResult(result)
  return exports
}
class ExitStatus {
  name = 'ExitStatus'
  constructor(status) {
    this.message = `Program terminated with exit(${status})`
    this.status = status
  }
}
var callRuntimeCallbacks = callbacks => {
  while (callbacks.length > 0) {
    callbacks.shift()(Module)
  }
}
var onPostRuns = []
var addOnPostRun = cb => onPostRuns.unshift(cb)
var onPreRuns = []
var addOnPreRun = cb => onPreRuns.unshift(cb)
var noExitRuntime = Module['noExitRuntime'] || true
var stackRestore = val => __emscripten_stack_restore(val)
var stackSave = () => _emscripten_stack_get_current()
var UTF8Decoder =
  typeof TextDecoder != 'undefined' ? new TextDecoder() : undefined
var UTF8ArrayToString = (heapOrArray, idx = 0, maxBytesToRead = NaN) => {
  var endIdx = idx + maxBytesToRead
  var endPtr = idx
  while (heapOrArray[endPtr] && !(endPtr >= endIdx)) ++endPtr
  if (endPtr - idx > 16 && heapOrArray.buffer && UTF8Decoder) {
    return UTF8Decoder.decode(heapOrArray.subarray(idx, endPtr))
  }
  var str = ''
  while (idx < endPtr) {
    var u0 = heapOrArray[idx++]
    if (!(u0 & 128)) {
      str += String.fromCharCode(u0)
      continue
    }
    var u1 = heapOrArray[idx++] & 63
    if ((u0 & 224) == 192) {
      str += String.fromCharCode(((u0 & 31) << 6) | u1)
      continue
    }
    var u2 = heapOrArray[idx++] & 63
    if ((u0 & 240) == 224) {
      u0 = ((u0 & 15) << 12) | (u1 << 6) | u2
    } else {
      u0 = ((u0 & 7) << 18) | (u1 << 12) | (u2 << 6) | (heapOrArray[idx++] & 63)
    }
    if (u0 < 65536) {
      str += String.fromCharCode(u0)
    } else {
      var ch = u0 - 65536
      str += String.fromCharCode(55296 | (ch >> 10), 56320 | (ch & 1023))
    }
  }
  return str
}
var UTF8ToString = (ptr, maxBytesToRead) =>
  ptr ? UTF8ArrayToString(HEAPU8, ptr, maxBytesToRead) : ''
var ___assert_fail = (condition, filename, line, func) =>
  abort(
    `Assertion failed: ${UTF8ToString(condition)}, at: ` +
      [
        filename ? UTF8ToString(filename) : 'unknown filename',
        line,
        func ? UTF8ToString(func) : 'unknown function',
      ],
  )
var exceptionCaught = []
var uncaughtExceptionCount = 0
var ___cxa_begin_catch = ptr => {
  var info = new ExceptionInfo(ptr)
  if (!info.get_caught()) {
    info.set_caught(true)
    uncaughtExceptionCount--
  }
  info.set_rethrown(false)
  exceptionCaught.push(info)
  ___cxa_increment_exception_refcount(ptr)
  return ___cxa_get_exception_ptr(ptr)
}
var exceptionLast = 0
var ___cxa_end_catch = () => {
  _setThrew(0, 0)
  var info = exceptionCaught.pop()
  ___cxa_decrement_exception_refcount(info.excPtr)
  exceptionLast = 0
}
class ExceptionInfo {
  constructor(excPtr) {
    this.excPtr = excPtr
    this.ptr = excPtr - 24
  }
  set_type(type) {
    HEAPU32[(this.ptr + 4) >> 2] = type
  }
  get_type() {
    return HEAPU32[(this.ptr + 4) >> 2]
  }
  set_destructor(destructor) {
    HEAPU32[(this.ptr + 8) >> 2] = destructor
  }
  get_destructor() {
    return HEAPU32[(this.ptr + 8) >> 2]
  }
  set_caught(caught) {
    caught = caught ? 1 : 0
    HEAP8[this.ptr + 12] = caught
  }
  get_caught() {
    return HEAP8[this.ptr + 12] != 0
  }
  set_rethrown(rethrown) {
    rethrown = rethrown ? 1 : 0
    HEAP8[this.ptr + 13] = rethrown
  }
  get_rethrown() {
    return HEAP8[this.ptr + 13] != 0
  }
  init(type, destructor) {
    this.set_adjusted_ptr(0)
    this.set_type(type)
    this.set_destructor(destructor)
  }
  set_adjusted_ptr(adjustedPtr) {
    HEAPU32[(this.ptr + 16) >> 2] = adjustedPtr
  }
  get_adjusted_ptr() {
    return HEAPU32[(this.ptr + 16) >> 2]
  }
}
var ___resumeException = ptr => {
  if (!exceptionLast) {
    exceptionLast = ptr
  }
  throw exceptionLast
}
var setTempRet0 = val => __emscripten_tempret_set(val)
var findMatchingCatch = args => {
  var thrown = exceptionLast
  if (!thrown) {
    setTempRet0(0)
    return 0
  }
  var info = new ExceptionInfo(thrown)
  info.set_adjusted_ptr(thrown)
  var thrownType = info.get_type()
  if (!thrownType) {
    setTempRet0(0)
    return thrown
  }
  for (var caughtType of args) {
    if (caughtType === 0 || caughtType === thrownType) {
      break
    }
    var adjusted_ptr_addr = info.ptr + 16
    if (___cxa_can_catch(caughtType, thrownType, adjusted_ptr_addr)) {
      setTempRet0(caughtType)
      return thrown
    }
  }
  setTempRet0(thrownType)
  return thrown
}
var ___cxa_find_matching_catch_2 = () => findMatchingCatch([])
var ___cxa_find_matching_catch_3 = arg0 => findMatchingCatch([arg0])
var ___cxa_rethrow = () => {
  var info = exceptionCaught.pop()
  if (!info) {
    abort('no exception to throw')
  }
  var ptr = info.excPtr
  if (!info.get_rethrown()) {
    exceptionCaught.push(info)
    info.set_rethrown(true)
    info.set_caught(false)
    uncaughtExceptionCount++
  }
  exceptionLast = ptr
  throw exceptionLast
}
var ___cxa_throw = (ptr, type, destructor) => {
  var info = new ExceptionInfo(ptr)
  info.init(type, destructor)
  exceptionLast = ptr
  uncaughtExceptionCount++
  throw exceptionLast
}
var ___cxa_uncaught_exceptions = () => uncaughtExceptionCount
var PATH = {
  isAbs: path => path.charAt(0) === '/',
  splitPath: filename => {
    var splitPathRe =
      /^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/
    return splitPathRe.exec(filename).slice(1)
  },
  normalizeArray: (parts, allowAboveRoot) => {
    var up = 0
    for (var i = parts.length - 1; i >= 0; i--) {
      var last = parts[i]
      if (last === '.') {
        parts.splice(i, 1)
      } else if (last === '..') {
        parts.splice(i, 1)
        up++
      } else if (up) {
        parts.splice(i, 1)
        up--
      }
    }
    if (allowAboveRoot) {
      for (; up; up--) {
        parts.unshift('..')
      }
    }
    return parts
  },
  normalize: path => {
    var isAbsolute = PATH.isAbs(path),
      trailingSlash = path.slice(-1) === '/'
    path = PATH.normalizeArray(
      path.split('/').filter(p => !!p),
      !isAbsolute,
    ).join('/')
    if (!path && !isAbsolute) {
      path = '.'
    }
    if (path && trailingSlash) {
      path += '/'
    }
    return (isAbsolute ? '/' : '') + path
  },
  dirname: path => {
    var result = PATH.splitPath(path),
      root = result[0],
      dir = result[1]
    if (!root && !dir) {
      return '.'
    }
    if (dir) {
      dir = dir.slice(0, -1)
    }
    return root + dir
  },
  basename: path => path && path.match(/([^\/]+|\/)\/*$/)[1],
  join: (...paths) => PATH.normalize(paths.join('/')),
  join2: (l, r) => PATH.normalize(l + '/' + r),
}
var initRandomFill = () => {
  if (ENVIRONMENT_IS_NODE) {
    var nodeCrypto = require('crypto')
    return view => nodeCrypto.randomFillSync(view)
  }
  return view => crypto.getRandomValues(view)
}
var randomFill = view => {
  ;(randomFill = initRandomFill())(view)
}
var PATH_FS = {
  resolve: (...args) => {
    var resolvedPath = '',
      resolvedAbsolute = false
    for (var i = args.length - 1; i >= -1 && !resolvedAbsolute; i--) {
      var path = i >= 0 ? args[i] : FS.cwd()
      if (typeof path != 'string') {
        throw new TypeError('Arguments to path.resolve must be strings')
      } else if (!path) {
        return ''
      }
      resolvedPath = path + '/' + resolvedPath
      resolvedAbsolute = PATH.isAbs(path)
    }
    resolvedPath = PATH.normalizeArray(
      resolvedPath.split('/').filter(p => !!p),
      !resolvedAbsolute,
    ).join('/')
    return (resolvedAbsolute ? '/' : '') + resolvedPath || '.'
  },
  relative: (from, to) => {
    from = PATH_FS.resolve(from).slice(1)
    to = PATH_FS.resolve(to).slice(1)
    function trim(arr) {
      var start = 0
      for (; start < arr.length; start++) {
        if (arr[start] !== '') break
      }
      var end = arr.length - 1
      for (; end >= 0; end--) {
        if (arr[end] !== '') break
      }
      if (start > end) return []
      return arr.slice(start, end - start + 1)
    }
    var fromParts = trim(from.split('/'))
    var toParts = trim(to.split('/'))
    var length = Math.min(fromParts.length, toParts.length)
    var samePartsLength = length
    for (var i = 0; i < length; i++) {
      if (fromParts[i] !== toParts[i]) {
        samePartsLength = i
        break
      }
    }
    var outputParts = []
    for (var i = samePartsLength; i < fromParts.length; i++) {
      outputParts.push('..')
    }
    outputParts = outputParts.concat(toParts.slice(samePartsLength))
    return outputParts.join('/')
  },
}
var FS_stdin_getChar_buffer = []
var lengthBytesUTF8 = str => {
  var len = 0
  for (var i = 0; i < str.length; ++i) {
    var c = str.charCodeAt(i)
    if (c <= 127) {
      len++
    } else if (c <= 2047) {
      len += 2
    } else if (c >= 55296 && c <= 57343) {
      len += 4
      ++i
    } else {
      len += 3
    }
  }
  return len
}
var stringToUTF8Array = (str, heap, outIdx, maxBytesToWrite) => {
  if (!(maxBytesToWrite > 0)) return 0
  var startIdx = outIdx
  var endIdx = outIdx + maxBytesToWrite - 1
  for (var i = 0; i < str.length; ++i) {
    var u = str.charCodeAt(i)
    if (u >= 55296 && u <= 57343) {
      var u1 = str.charCodeAt(++i)
      u = (65536 + ((u & 1023) << 10)) | (u1 & 1023)
    }
    if (u <= 127) {
      if (outIdx >= endIdx) break
      heap[outIdx++] = u
    } else if (u <= 2047) {
      if (outIdx + 1 >= endIdx) break
      heap[outIdx++] = 192 | (u >> 6)
      heap[outIdx++] = 128 | (u & 63)
    } else if (u <= 65535) {
      if (outIdx + 2 >= endIdx) break
      heap[outIdx++] = 224 | (u >> 12)
      heap[outIdx++] = 128 | ((u >> 6) & 63)
      heap[outIdx++] = 128 | (u & 63)
    } else {
      if (outIdx + 3 >= endIdx) break
      heap[outIdx++] = 240 | (u >> 18)
      heap[outIdx++] = 128 | ((u >> 12) & 63)
      heap[outIdx++] = 128 | ((u >> 6) & 63)
      heap[outIdx++] = 128 | (u & 63)
    }
  }
  heap[outIdx] = 0
  return outIdx - startIdx
}
var intArrayFromString = (stringy, dontAddNull, length) => {
  var len = length > 0 ? length : lengthBytesUTF8(stringy) + 1
  var u8array = new Array(len)
  var numBytesWritten = stringToUTF8Array(stringy, u8array, 0, u8array.length)
  if (dontAddNull) u8array.length = numBytesWritten
  return u8array
}
var FS_stdin_getChar = () => {
  if (!FS_stdin_getChar_buffer.length) {
    var result = null
    if (ENVIRONMENT_IS_NODE) {
      var BUFSIZE = 256
      var buf = Buffer.alloc(BUFSIZE)
      var bytesRead = 0
      var fd = process.stdin.fd
      try {
        bytesRead = fs.readSync(fd, buf, 0, BUFSIZE)
      } catch (e) {
        if (e.toString().includes('EOF')) bytesRead = 0
        else throw e
      }
      if (bytesRead > 0) {
        result = buf.slice(0, bytesRead).toString('utf-8')
      }
    } else if (
      typeof window != 'undefined' &&
      typeof window.prompt == 'function'
    ) {
      result = window.prompt('Input: ')
      if (result !== null) {
        result += '\n'
      }
    } else {
    }
    if (!result) {
      return null
    }
    FS_stdin_getChar_buffer = intArrayFromString(result, true)
  }
  return FS_stdin_getChar_buffer.shift()
}
var TTY = {
  ttys: [],
  init() {},
  shutdown() {},
  register(dev, ops) {
    TTY.ttys[dev] = { input: [], output: [], ops }
    FS.registerDevice(dev, TTY.stream_ops)
  },
  stream_ops: {
    open(stream) {
      var tty = TTY.ttys[stream.node.rdev]
      if (!tty) {
        throw new FS.ErrnoError(43)
      }
      stream.tty = tty
      stream.seekable = false
    },
    close(stream) {
      stream.tty.ops.fsync(stream.tty)
    },
    fsync(stream) {
      stream.tty.ops.fsync(stream.tty)
    },
    read(stream, buffer, offset, length, pos) {
      if (!stream.tty || !stream.tty.ops.get_char) {
        throw new FS.ErrnoError(60)
      }
      var bytesRead = 0
      for (var i = 0; i < length; i++) {
        var result
        try {
          result = stream.tty.ops.get_char(stream.tty)
        } catch (e) {
          throw new FS.ErrnoError(29)
        }
        if (result === undefined && bytesRead === 0) {
          throw new FS.ErrnoError(6)
        }
        if (result === null || result === undefined) break
        bytesRead++
        buffer[offset + i] = result
      }
      if (bytesRead) {
        stream.node.atime = Date.now()
      }
      return bytesRead
    },
    write(stream, buffer, offset, length, pos) {
      if (!stream.tty || !stream.tty.ops.put_char) {
        throw new FS.ErrnoError(60)
      }
      try {
        for (var i = 0; i < length; i++) {
          stream.tty.ops.put_char(stream.tty, buffer[offset + i])
        }
      } catch (e) {
        throw new FS.ErrnoError(29)
      }
      if (length) {
        stream.node.mtime = stream.node.ctime = Date.now()
      }
      return i
    },
  },
  default_tty_ops: {
    get_char(tty) {
      return FS_stdin_getChar()
    },
    put_char(tty, val) {
      if (val === null || val === 10) {
        out(UTF8ArrayToString(tty.output))
        tty.output = []
      } else {
        if (val != 0) tty.output.push(val)
      }
    },
    fsync(tty) {
      if (tty.output?.length > 0) {
        out(UTF8ArrayToString(tty.output))
        tty.output = []
      }
    },
    ioctl_tcgets(tty) {
      return {
        c_iflag: 25856,
        c_oflag: 5,
        c_cflag: 191,
        c_lflag: 35387,
        c_cc: [
          3, 28, 127, 21, 4, 0, 1, 0, 17, 19, 26, 0, 18, 15, 23, 22, 0, 0, 0, 0,
          0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
      }
    },
    ioctl_tcsets(tty, optional_actions, data) {
      return 0
    },
    ioctl_tiocgwinsz(tty) {
      return [24, 80]
    },
  },
  default_tty1_ops: {
    put_char(tty, val) {
      if (val === null || val === 10) {
        err(UTF8ArrayToString(tty.output))
        tty.output = []
      } else {
        if (val != 0) tty.output.push(val)
      }
    },
    fsync(tty) {
      if (tty.output?.length > 0) {
        err(UTF8ArrayToString(tty.output))
        tty.output = []
      }
    },
  },
}
var alignMemory = (size, alignment) => Math.ceil(size / alignment) * alignment
var mmapAlloc = size => {
  abort()
}
var MEMFS = {
  ops_table: null,
  mount(mount) {
    return MEMFS.createNode(null, '/', 16895, 0)
  },
  createNode(parent, name, mode, dev) {
    if (FS.isBlkdev(mode) || FS.isFIFO(mode)) {
      throw new FS.ErrnoError(63)
    }
    MEMFS.ops_table ||= {
      dir: {
        node: {
          getattr: MEMFS.node_ops.getattr,
          setattr: MEMFS.node_ops.setattr,
          lookup: MEMFS.node_ops.lookup,
          mknod: MEMFS.node_ops.mknod,
          rename: MEMFS.node_ops.rename,
          unlink: MEMFS.node_ops.unlink,
          rmdir: MEMFS.node_ops.rmdir,
          readdir: MEMFS.node_ops.readdir,
          symlink: MEMFS.node_ops.symlink,
        },
        stream: { llseek: MEMFS.stream_ops.llseek },
      },
      file: {
        node: {
          getattr: MEMFS.node_ops.getattr,
          setattr: MEMFS.node_ops.setattr,
        },
        stream: {
          llseek: MEMFS.stream_ops.llseek,
          read: MEMFS.stream_ops.read,
          write: MEMFS.stream_ops.write,
          allocate: MEMFS.stream_ops.allocate,
          mmap: MEMFS.stream_ops.mmap,
          msync: MEMFS.stream_ops.msync,
        },
      },
      link: {
        node: {
          getattr: MEMFS.node_ops.getattr,
          setattr: MEMFS.node_ops.setattr,
          readlink: MEMFS.node_ops.readlink,
        },
        stream: {},
      },
      chrdev: {
        node: {
          getattr: MEMFS.node_ops.getattr,
          setattr: MEMFS.node_ops.setattr,
        },
        stream: FS.chrdev_stream_ops,
      },
    }
    var node = FS.createNode(parent, name, mode, dev)
    if (FS.isDir(node.mode)) {
      node.node_ops = MEMFS.ops_table.dir.node
      node.stream_ops = MEMFS.ops_table.dir.stream
      node.contents = {}
    } else if (FS.isFile(node.mode)) {
      node.node_ops = MEMFS.ops_table.file.node
      node.stream_ops = MEMFS.ops_table.file.stream
      node.usedBytes = 0
      node.contents = null
    } else if (FS.isLink(node.mode)) {
      node.node_ops = MEMFS.ops_table.link.node
      node.stream_ops = MEMFS.ops_table.link.stream
    } else if (FS.isChrdev(node.mode)) {
      node.node_ops = MEMFS.ops_table.chrdev.node
      node.stream_ops = MEMFS.ops_table.chrdev.stream
    }
    node.atime = node.mtime = node.ctime = Date.now()
    if (parent) {
      parent.contents[name] = node
      parent.atime = parent.mtime = parent.ctime = node.atime
    }
    return node
  },
  getFileDataAsTypedArray(node) {
    if (!node.contents) return new Uint8Array(0)
    if (node.contents.subarray) return node.contents.subarray(0, node.usedBytes)
    return new Uint8Array(node.contents)
  },
  expandFileStorage(node, newCapacity) {
    var prevCapacity = node.contents ? node.contents.length : 0
    if (prevCapacity >= newCapacity) return
    var CAPACITY_DOUBLING_MAX = 1024 * 1024
    newCapacity = Math.max(
      newCapacity,
      (prevCapacity * (prevCapacity < CAPACITY_DOUBLING_MAX ? 2 : 1.125)) >>> 0,
    )
    if (prevCapacity != 0) newCapacity = Math.max(newCapacity, 256)
    var oldContents = node.contents
    node.contents = new Uint8Array(newCapacity)
    if (node.usedBytes > 0)
      node.contents.set(oldContents.subarray(0, node.usedBytes), 0)
  },
  resizeFileStorage(node, newSize) {
    if (node.usedBytes == newSize) return
    if (newSize == 0) {
      node.contents = null
      node.usedBytes = 0
    } else {
      var oldContents = node.contents
      node.contents = new Uint8Array(newSize)
      if (oldContents) {
        node.contents.set(
          oldContents.subarray(0, Math.min(newSize, node.usedBytes)),
        )
      }
      node.usedBytes = newSize
    }
  },
  node_ops: {
    getattr(node) {
      var attr = {}
      attr.dev = FS.isChrdev(node.mode) ? node.id : 1
      attr.ino = node.id
      attr.mode = node.mode
      attr.nlink = 1
      attr.uid = 0
      attr.gid = 0
      attr.rdev = node.rdev
      if (FS.isDir(node.mode)) {
        attr.size = 4096
      } else if (FS.isFile(node.mode)) {
        attr.size = node.usedBytes
      } else if (FS.isLink(node.mode)) {
        attr.size = node.link.length
      } else {
        attr.size = 0
      }
      attr.atime = new Date(node.atime)
      attr.mtime = new Date(node.mtime)
      attr.ctime = new Date(node.ctime)
      attr.blksize = 4096
      attr.blocks = Math.ceil(attr.size / attr.blksize)
      return attr
    },
    setattr(node, attr) {
      for (const key of ['mode', 'atime', 'mtime', 'ctime']) {
        if (attr[key] != null) {
          node[key] = attr[key]
        }
      }
      if (attr.size !== undefined) {
        MEMFS.resizeFileStorage(node, attr.size)
      }
    },
    lookup(parent, name) {
      throw MEMFS.doesNotExistError
    },
    mknod(parent, name, mode, dev) {
      return MEMFS.createNode(parent, name, mode, dev)
    },
    rename(old_node, new_dir, new_name) {
      var new_node
      try {
        new_node = FS.lookupNode(new_dir, new_name)
      } catch (e) {}
      if (new_node) {
        if (FS.isDir(old_node.mode)) {
          for (var i in new_node.contents) {
            throw new FS.ErrnoError(55)
          }
        }
        FS.hashRemoveNode(new_node)
      }
      delete old_node.parent.contents[old_node.name]
      new_dir.contents[new_name] = old_node
      old_node.name = new_name
      new_dir.ctime =
        new_dir.mtime =
        old_node.parent.ctime =
        old_node.parent.mtime =
          Date.now()
    },
    unlink(parent, name) {
      delete parent.contents[name]
      parent.ctime = parent.mtime = Date.now()
    },
    rmdir(parent, name) {
      var node = FS.lookupNode(parent, name)
      for (var i in node.contents) {
        throw new FS.ErrnoError(55)
      }
      delete parent.contents[name]
      parent.ctime = parent.mtime = Date.now()
    },
    readdir(node) {
      return ['.', '..', ...Object.keys(node.contents)]
    },
    symlink(parent, newname, oldpath) {
      var node = MEMFS.createNode(parent, newname, 511 | 40960, 0)
      node.link = oldpath
      return node
    },
    readlink(node) {
      if (!FS.isLink(node.mode)) {
        throw new FS.ErrnoError(28)
      }
      return node.link
    },
  },
  stream_ops: {
    read(stream, buffer, offset, length, position) {
      var contents = stream.node.contents
      if (position >= stream.node.usedBytes) return 0
      var size = Math.min(stream.node.usedBytes - position, length)
      if (size > 8 && contents.subarray) {
        buffer.set(contents.subarray(position, position + size), offset)
      } else {
        for (var i = 0; i < size; i++)
          buffer[offset + i] = contents[position + i]
      }
      return size
    },
    write(stream, buffer, offset, length, position, canOwn) {
      if (buffer.buffer === HEAP8.buffer) {
        canOwn = false
      }
      if (!length) return 0
      var node = stream.node
      node.mtime = node.ctime = Date.now()
      if (buffer.subarray && (!node.contents || node.contents.subarray)) {
        if (canOwn) {
          node.contents = buffer.subarray(offset, offset + length)
          node.usedBytes = length
          return length
        } else if (node.usedBytes === 0 && position === 0) {
          node.contents = buffer.slice(offset, offset + length)
          node.usedBytes = length
          return length
        } else if (position + length <= node.usedBytes) {
          node.contents.set(buffer.subarray(offset, offset + length), position)
          return length
        }
      }
      MEMFS.expandFileStorage(node, position + length)
      if (node.contents.subarray && buffer.subarray) {
        node.contents.set(buffer.subarray(offset, offset + length), position)
      } else {
        for (var i = 0; i < length; i++) {
          node.contents[position + i] = buffer[offset + i]
        }
      }
      node.usedBytes = Math.max(node.usedBytes, position + length)
      return length
    },
    llseek(stream, offset, whence) {
      var position = offset
      if (whence === 1) {
        position += stream.position
      } else if (whence === 2) {
        if (FS.isFile(stream.node.mode)) {
          position += stream.node.usedBytes
        }
      }
      if (position < 0) {
        throw new FS.ErrnoError(28)
      }
      return position
    },
    allocate(stream, offset, length) {
      MEMFS.expandFileStorage(stream.node, offset + length)
      stream.node.usedBytes = Math.max(stream.node.usedBytes, offset + length)
    },
    mmap(stream, length, position, prot, flags) {
      if (!FS.isFile(stream.node.mode)) {
        throw new FS.ErrnoError(43)
      }
      var ptr
      var allocated
      var contents = stream.node.contents
      if (!(flags & 2) && contents && contents.buffer === HEAP8.buffer) {
        allocated = false
        ptr = contents.byteOffset
      } else {
        allocated = true
        ptr = mmapAlloc(length)
        if (!ptr) {
          throw new FS.ErrnoError(48)
        }
        if (contents) {
          if (position > 0 || position + length < contents.length) {
            if (contents.subarray) {
              contents = contents.subarray(position, position + length)
            } else {
              contents = Array.prototype.slice.call(
                contents,
                position,
                position + length,
              )
            }
          }
          HEAP8.set(contents, ptr)
        }
      }
      return { ptr, allocated }
    },
    msync(stream, buffer, offset, length, mmapFlags) {
      MEMFS.stream_ops.write(stream, buffer, 0, length, offset, false)
      return 0
    },
  },
}
var asyncLoad = async url => {
  var arrayBuffer = await readAsync(url)
  return new Uint8Array(arrayBuffer)
}
var FS_createDataFile = (parent, name, fileData, canRead, canWrite, canOwn) => {
  FS.createDataFile(parent, name, fileData, canRead, canWrite, canOwn)
}
var preloadPlugins = Module['preloadPlugins'] || []
var FS_handledByPreloadPlugin = (byteArray, fullname, finish, onerror) => {
  if (typeof Browser != 'undefined') Browser.init()
  var handled = false
  preloadPlugins.forEach(plugin => {
    if (handled) return
    if (plugin['canHandle'](fullname)) {
      plugin['handle'](byteArray, fullname, finish, onerror)
      handled = true
    }
  })
  return handled
}
var FS_createPreloadedFile = (
  parent,
  name,
  url,
  canRead,
  canWrite,
  onload,
  onerror,
  dontCreateFile,
  canOwn,
  preFinish,
) => {
  var fullname = name ? PATH_FS.resolve(PATH.join2(parent, name)) : parent
  var dep = getUniqueRunDependency(`cp ${fullname}`)
  function processData(byteArray) {
    function finish(byteArray) {
      preFinish?.()
      if (!dontCreateFile) {
        FS_createDataFile(parent, name, byteArray, canRead, canWrite, canOwn)
      }
      onload?.()
      removeRunDependency(dep)
    }
    if (
      FS_handledByPreloadPlugin(byteArray, fullname, finish, () => {
        onerror?.()
        removeRunDependency(dep)
      })
    ) {
      return
    }
    finish(byteArray)
  }
  addRunDependency(dep)
  if (typeof url == 'string') {
    asyncLoad(url).then(processData, onerror)
  } else {
    processData(url)
  }
}
var FS_modeStringToFlags = str => {
  var flagModes = {
    r: 0,
    'r+': 2,
    w: 512 | 64 | 1,
    'w+': 512 | 64 | 2,
    a: 1024 | 64 | 1,
    'a+': 1024 | 64 | 2,
  }
  var flags = flagModes[str]
  if (typeof flags == 'undefined') {
    throw new Error(`Unknown file open mode: ${str}`)
  }
  return flags
}
var FS_getMode = (canRead, canWrite) => {
  var mode = 0
  if (canRead) mode |= 292 | 73
  if (canWrite) mode |= 146
  return mode
}
var FS = {
  root: null,
  mounts: [],
  devices: {},
  streams: [],
  nextInode: 1,
  nameTable: null,
  currentPath: '/',
  initialized: false,
  ignorePermissions: true,
  ErrnoError: class {
    name = 'ErrnoError'
    constructor(errno) {
      this.errno = errno
    }
  },
  filesystems: null,
  syncFSRequests: 0,
  readFiles: {},
  FSStream: class {
    shared = {}
    get object() {
      return this.node
    }
    set object(val) {
      this.node = val
    }
    get isRead() {
      return (this.flags & 2097155) !== 1
    }
    get isWrite() {
      return (this.flags & 2097155) !== 0
    }
    get isAppend() {
      return this.flags & 1024
    }
    get flags() {
      return this.shared.flags
    }
    set flags(val) {
      this.shared.flags = val
    }
    get position() {
      return this.shared.position
    }
    set position(val) {
      this.shared.position = val
    }
  },
  FSNode: class {
    node_ops = {}
    stream_ops = {}
    readMode = 292 | 73
    writeMode = 146
    mounted = null
    constructor(parent, name, mode, rdev) {
      if (!parent) {
        parent = this
      }
      this.parent = parent
      this.mount = parent.mount
      this.id = FS.nextInode++
      this.name = name
      this.mode = mode
      this.rdev = rdev
      this.atime = this.mtime = this.ctime = Date.now()
    }
    get read() {
      return (this.mode & this.readMode) === this.readMode
    }
    set read(val) {
      val ? (this.mode |= this.readMode) : (this.mode &= ~this.readMode)
    }
    get write() {
      return (this.mode & this.writeMode) === this.writeMode
    }
    set write(val) {
      val ? (this.mode |= this.writeMode) : (this.mode &= ~this.writeMode)
    }
    get isFolder() {
      return FS.isDir(this.mode)
    }
    get isDevice() {
      return FS.isChrdev(this.mode)
    }
  },
  lookupPath(path, opts = {}) {
    if (!path) {
      throw new FS.ErrnoError(44)
    }
    opts.follow_mount ??= true
    if (!PATH.isAbs(path)) {
      path = FS.cwd() + '/' + path
    }
    linkloop: for (var nlinks = 0; nlinks < 40; nlinks++) {
      var parts = path.split('/').filter(p => !!p)
      var current = FS.root
      var current_path = '/'
      for (var i = 0; i < parts.length; i++) {
        var islast = i === parts.length - 1
        if (islast && opts.parent) {
          break
        }
        if (parts[i] === '.') {
          continue
        }
        if (parts[i] === '..') {
          current_path = PATH.dirname(current_path)
          current = current.parent
          continue
        }
        current_path = PATH.join2(current_path, parts[i])
        try {
          current = FS.lookupNode(current, parts[i])
        } catch (e) {
          if (e?.errno === 44 && islast && opts.noent_okay) {
            return { path: current_path }
          }
          throw e
        }
        if (FS.isMountpoint(current) && (!islast || opts.follow_mount)) {
          current = current.mounted.root
        }
        if (FS.isLink(current.mode) && (!islast || opts.follow)) {
          if (!current.node_ops.readlink) {
            throw new FS.ErrnoError(52)
          }
          var link = current.node_ops.readlink(current)
          if (!PATH.isAbs(link)) {
            link = PATH.dirname(current_path) + '/' + link
          }
          path = link + '/' + parts.slice(i + 1).join('/')
          continue linkloop
        }
      }
      return { path: current_path, node: current }
    }
    throw new FS.ErrnoError(32)
  },
  getPath(node) {
    var path
    while (true) {
      if (FS.isRoot(node)) {
        var mount = node.mount.mountpoint
        if (!path) return mount
        return mount[mount.length - 1] !== '/'
          ? `${mount}/${path}`
          : mount + path
      }
      path = path ? `${node.name}/${path}` : node.name
      node = node.parent
    }
  },
  hashName(parentid, name) {
    var hash = 0
    for (var i = 0; i < name.length; i++) {
      hash = ((hash << 5) - hash + name.charCodeAt(i)) | 0
    }
    return ((parentid + hash) >>> 0) % FS.nameTable.length
  },
  hashAddNode(node) {
    var hash = FS.hashName(node.parent.id, node.name)
    node.name_next = FS.nameTable[hash]
    FS.nameTable[hash] = node
  },
  hashRemoveNode(node) {
    var hash = FS.hashName(node.parent.id, node.name)
    if (FS.nameTable[hash] === node) {
      FS.nameTable[hash] = node.name_next
    } else {
      var current = FS.nameTable[hash]
      while (current) {
        if (current.name_next === node) {
          current.name_next = node.name_next
          break
        }
        current = current.name_next
      }
    }
  },
  lookupNode(parent, name) {
    var errCode = FS.mayLookup(parent)
    if (errCode) {
      throw new FS.ErrnoError(errCode)
    }
    var hash = FS.hashName(parent.id, name)
    for (var node = FS.nameTable[hash]; node; node = node.name_next) {
      var nodeName = node.name
      if (node.parent.id === parent.id && nodeName === name) {
        return node
      }
    }
    return FS.lookup(parent, name)
  },
  createNode(parent, name, mode, rdev) {
    var node = new FS.FSNode(parent, name, mode, rdev)
    FS.hashAddNode(node)
    return node
  },
  destroyNode(node) {
    FS.hashRemoveNode(node)
  },
  isRoot(node) {
    return node === node.parent
  },
  isMountpoint(node) {
    return !!node.mounted
  },
  isFile(mode) {
    return (mode & 61440) === 32768
  },
  isDir(mode) {
    return (mode & 61440) === 16384
  },
  isLink(mode) {
    return (mode & 61440) === 40960
  },
  isChrdev(mode) {
    return (mode & 61440) === 8192
  },
  isBlkdev(mode) {
    return (mode & 61440) === 24576
  },
  isFIFO(mode) {
    return (mode & 61440) === 4096
  },
  isSocket(mode) {
    return (mode & 49152) === 49152
  },
  flagsToPermissionString(flag) {
    var perms = ['r', 'w', 'rw'][flag & 3]
    if (flag & 512) {
      perms += 'w'
    }
    return perms
  },
  nodePermissions(node, perms) {
    if (FS.ignorePermissions) {
      return 0
    }
    if (perms.includes('r') && !(node.mode & 292)) {
      return 2
    } else if (perms.includes('w') && !(node.mode & 146)) {
      return 2
    } else if (perms.includes('x') && !(node.mode & 73)) {
      return 2
    }
    return 0
  },
  mayLookup(dir) {
    if (!FS.isDir(dir.mode)) return 54
    var errCode = FS.nodePermissions(dir, 'x')
    if (errCode) return errCode
    if (!dir.node_ops.lookup) return 2
    return 0
  },
  mayCreate(dir, name) {
    if (!FS.isDir(dir.mode)) {
      return 54
    }
    try {
      var node = FS.lookupNode(dir, name)
      return 20
    } catch (e) {}
    return FS.nodePermissions(dir, 'wx')
  },
  mayDelete(dir, name, isdir) {
    var node
    try {
      node = FS.lookupNode(dir, name)
    } catch (e) {
      return e.errno
    }
    var errCode = FS.nodePermissions(dir, 'wx')
    if (errCode) {
      return errCode
    }
    if (isdir) {
      if (!FS.isDir(node.mode)) {
        return 54
      }
      if (FS.isRoot(node) || FS.getPath(node) === FS.cwd()) {
        return 10
      }
    } else {
      if (FS.isDir(node.mode)) {
        return 31
      }
    }
    return 0
  },
  mayOpen(node, flags) {
    if (!node) {
      return 44
    }
    if (FS.isLink(node.mode)) {
      return 32
    } else if (FS.isDir(node.mode)) {
      if (FS.flagsToPermissionString(flags) !== 'r' || flags & (512 | 64)) {
        return 31
      }
    }
    return FS.nodePermissions(node, FS.flagsToPermissionString(flags))
  },
  checkOpExists(op, err) {
    if (!op) {
      throw new FS.ErrnoError(err)
    }
    return op
  },
  MAX_OPEN_FDS: 4096,
  nextfd() {
    for (var fd = 0; fd <= FS.MAX_OPEN_FDS; fd++) {
      if (!FS.streams[fd]) {
        return fd
      }
    }
    throw new FS.ErrnoError(33)
  },
  getStreamChecked(fd) {
    var stream = FS.getStream(fd)
    if (!stream) {
      throw new FS.ErrnoError(8)
    }
    return stream
  },
  getStream: fd => FS.streams[fd],
  createStream(stream, fd = -1) {
    stream = Object.assign(new FS.FSStream(), stream)
    if (fd == -1) {
      fd = FS.nextfd()
    }
    stream.fd = fd
    FS.streams[fd] = stream
    return stream
  },
  closeStream(fd) {
    FS.streams[fd] = null
  },
  dupStream(origStream, fd = -1) {
    var stream = FS.createStream(origStream, fd)
    stream.stream_ops?.dup?.(stream)
    return stream
  },
  doSetAttr(stream, node, attr) {
    var setattr = stream?.stream_ops.setattr
    var arg = setattr ? stream : node
    setattr ??= node.node_ops.setattr
    FS.checkOpExists(setattr, 63)
    setattr(arg, attr)
  },
  chrdev_stream_ops: {
    open(stream) {
      var device = FS.getDevice(stream.node.rdev)
      stream.stream_ops = device.stream_ops
      stream.stream_ops.open?.(stream)
    },
    llseek() {
      throw new FS.ErrnoError(70)
    },
  },
  major: dev => dev >> 8,
  minor: dev => dev & 255,
  makedev: (ma, mi) => (ma << 8) | mi,
  registerDevice(dev, ops) {
    FS.devices[dev] = { stream_ops: ops }
  },
  getDevice: dev => FS.devices[dev],
  getMounts(mount) {
    var mounts = []
    var check = [mount]
    while (check.length) {
      var m = check.pop()
      mounts.push(m)
      check.push(...m.mounts)
    }
    return mounts
  },
  syncfs(populate, callback) {
    if (typeof populate == 'function') {
      callback = populate
      populate = false
    }
    FS.syncFSRequests++
    if (FS.syncFSRequests > 1) {
      err(
        `warning: ${FS.syncFSRequests} FS.syncfs operations in flight at once, probably just doing extra work`,
      )
    }
    var mounts = FS.getMounts(FS.root.mount)
    var completed = 0
    function doCallback(errCode) {
      FS.syncFSRequests--
      return callback(errCode)
    }
    function done(errCode) {
      if (errCode) {
        if (!done.errored) {
          done.errored = true
          return doCallback(errCode)
        }
        return
      }
      if (++completed >= mounts.length) {
        doCallback(null)
      }
    }
    mounts.forEach(mount => {
      if (!mount.type.syncfs) {
        return done(null)
      }
      mount.type.syncfs(mount, populate, done)
    })
  },
  mount(type, opts, mountpoint) {
    var root = mountpoint === '/'
    var pseudo = !mountpoint
    var node
    if (root && FS.root) {
      throw new FS.ErrnoError(10)
    } else if (!root && !pseudo) {
      var lookup = FS.lookupPath(mountpoint, { follow_mount: false })
      mountpoint = lookup.path
      node = lookup.node
      if (FS.isMountpoint(node)) {
        throw new FS.ErrnoError(10)
      }
      if (!FS.isDir(node.mode)) {
        throw new FS.ErrnoError(54)
      }
    }
    var mount = { type, opts, mountpoint, mounts: [] }
    var mountRoot = type.mount(mount)
    mountRoot.mount = mount
    mount.root = mountRoot
    if (root) {
      FS.root = mountRoot
    } else if (node) {
      node.mounted = mount
      if (node.mount) {
        node.mount.mounts.push(mount)
      }
    }
    return mountRoot
  },
  unmount(mountpoint) {
    var lookup = FS.lookupPath(mountpoint, { follow_mount: false })
    if (!FS.isMountpoint(lookup.node)) {
      throw new FS.ErrnoError(28)
    }
    var node = lookup.node
    var mount = node.mounted
    var mounts = FS.getMounts(mount)
    Object.keys(FS.nameTable).forEach(hash => {
      var current = FS.nameTable[hash]
      while (current) {
        var next = current.name_next
        if (mounts.includes(current.mount)) {
          FS.destroyNode(current)
        }
        current = next
      }
    })
    node.mounted = null
    var idx = node.mount.mounts.indexOf(mount)
    node.mount.mounts.splice(idx, 1)
  },
  lookup(parent, name) {
    return parent.node_ops.lookup(parent, name)
  },
  mknod(path, mode, dev) {
    var lookup = FS.lookupPath(path, { parent: true })
    var parent = lookup.node
    var name = PATH.basename(path)
    if (!name) {
      throw new FS.ErrnoError(28)
    }
    if (name === '.' || name === '..') {
      throw new FS.ErrnoError(20)
    }
    var errCode = FS.mayCreate(parent, name)
    if (errCode) {
      throw new FS.ErrnoError(errCode)
    }
    if (!parent.node_ops.mknod) {
      throw new FS.ErrnoError(63)
    }
    return parent.node_ops.mknod(parent, name, mode, dev)
  },
  statfs(path) {
    return FS.statfsNode(FS.lookupPath(path, { follow: true }).node)
  },
  statfsStream(stream) {
    return FS.statfsNode(stream.node)
  },
  statfsNode(node) {
    var rtn = {
      bsize: 4096,
      frsize: 4096,
      blocks: 1e6,
      bfree: 5e5,
      bavail: 5e5,
      files: FS.nextInode,
      ffree: FS.nextInode - 1,
      fsid: 42,
      flags: 2,
      namelen: 255,
    }
    if (node.node_ops.statfs) {
      Object.assign(rtn, node.node_ops.statfs(node.mount.opts.root))
    }
    return rtn
  },
  create(path, mode = 438) {
    mode &= 4095
    mode |= 32768
    return FS.mknod(path, mode, 0)
  },
  mkdir(path, mode = 511) {
    mode &= 511 | 512
    mode |= 16384
    return FS.mknod(path, mode, 0)
  },
  mkdirTree(path, mode) {
    var dirs = path.split('/')
    var d = ''
    for (var i = 0; i < dirs.length; ++i) {
      if (!dirs[i]) continue
      d += '/' + dirs[i]
      try {
        FS.mkdir(d, mode)
      } catch (e) {
        if (e.errno != 20) throw e
      }
    }
  },
  mkdev(path, mode, dev) {
    if (typeof dev == 'undefined') {
      dev = mode
      mode = 438
    }
    mode |= 8192
    return FS.mknod(path, mode, dev)
  },
  symlink(oldpath, newpath) {
    if (!PATH_FS.resolve(oldpath)) {
      throw new FS.ErrnoError(44)
    }
    var lookup = FS.lookupPath(newpath, { parent: true })
    var parent = lookup.node
    if (!parent) {
      throw new FS.ErrnoError(44)
    }
    var newname = PATH.basename(newpath)
    var errCode = FS.mayCreate(parent, newname)
    if (errCode) {
      throw new FS.ErrnoError(errCode)
    }
    if (!parent.node_ops.symlink) {
      throw new FS.ErrnoError(63)
    }
    return parent.node_ops.symlink(parent, newname, oldpath)
  },
  rename(old_path, new_path) {
    var old_dirname = PATH.dirname(old_path)
    var new_dirname = PATH.dirname(new_path)
    var old_name = PATH.basename(old_path)
    var new_name = PATH.basename(new_path)
    var lookup, old_dir, new_dir
    lookup = FS.lookupPath(old_path, { parent: true })
    old_dir = lookup.node
    lookup = FS.lookupPath(new_path, { parent: true })
    new_dir = lookup.node
    if (!old_dir || !new_dir) throw new FS.ErrnoError(44)
    if (old_dir.mount !== new_dir.mount) {
      throw new FS.ErrnoError(75)
    }
    var old_node = FS.lookupNode(old_dir, old_name)
    var relative = PATH_FS.relative(old_path, new_dirname)
    if (relative.charAt(0) !== '.') {
      throw new FS.ErrnoError(28)
    }
    relative = PATH_FS.relative(new_path, old_dirname)
    if (relative.charAt(0) !== '.') {
      throw new FS.ErrnoError(55)
    }
    var new_node
    try {
      new_node = FS.lookupNode(new_dir, new_name)
    } catch (e) {}
    if (old_node === new_node) {
      return
    }
    var isdir = FS.isDir(old_node.mode)
    var errCode = FS.mayDelete(old_dir, old_name, isdir)
    if (errCode) {
      throw new FS.ErrnoError(errCode)
    }
    errCode = new_node
      ? FS.mayDelete(new_dir, new_name, isdir)
      : FS.mayCreate(new_dir, new_name)
    if (errCode) {
      throw new FS.ErrnoError(errCode)
    }
    if (!old_dir.node_ops.rename) {
      throw new FS.ErrnoError(63)
    }
    if (FS.isMountpoint(old_node) || (new_node && FS.isMountpoint(new_node))) {
      throw new FS.ErrnoError(10)
    }
    if (new_dir !== old_dir) {
      errCode = FS.nodePermissions(old_dir, 'w')
      if (errCode) {
        throw new FS.ErrnoError(errCode)
      }
    }
    FS.hashRemoveNode(old_node)
    try {
      old_dir.node_ops.rename(old_node, new_dir, new_name)
      old_node.parent = new_dir
    } catch (e) {
      throw e
    } finally {
      FS.hashAddNode(old_node)
    }
  },
  rmdir(path) {
    var lookup = FS.lookupPath(path, { parent: true })
    var parent = lookup.node
    var name = PATH.basename(path)
    var node = FS.lookupNode(parent, name)
    var errCode = FS.mayDelete(parent, name, true)
    if (errCode) {
      throw new FS.ErrnoError(errCode)
    }
    if (!parent.node_ops.rmdir) {
      throw new FS.ErrnoError(63)
    }
    if (FS.isMountpoint(node)) {
      throw new FS.ErrnoError(10)
    }
    parent.node_ops.rmdir(parent, name)
    FS.destroyNode(node)
  },
  readdir(path) {
    var lookup = FS.lookupPath(path, { follow: true })
    var node = lookup.node
    var readdir = FS.checkOpExists(node.node_ops.readdir, 54)
    return readdir(node)
  },
  unlink(path) {
    var lookup = FS.lookupPath(path, { parent: true })
    var parent = lookup.node
    if (!parent) {
      throw new FS.ErrnoError(44)
    }
    var name = PATH.basename(path)
    var node = FS.lookupNode(parent, name)
    var errCode = FS.mayDelete(parent, name, false)
    if (errCode) {
      throw new FS.ErrnoError(errCode)
    }
    if (!parent.node_ops.unlink) {
      throw new FS.ErrnoError(63)
    }
    if (FS.isMountpoint(node)) {
      throw new FS.ErrnoError(10)
    }
    parent.node_ops.unlink(parent, name)
    FS.destroyNode(node)
  },
  readlink(path) {
    var lookup = FS.lookupPath(path)
    var link = lookup.node
    if (!link) {
      throw new FS.ErrnoError(44)
    }
    if (!link.node_ops.readlink) {
      throw new FS.ErrnoError(28)
    }
    return link.node_ops.readlink(link)
  },
  stat(path, dontFollow) {
    var lookup = FS.lookupPath(path, { follow: !dontFollow })
    var node = lookup.node
    var getattr = FS.checkOpExists(node.node_ops.getattr, 63)
    return getattr(node)
  },
  fstat(fd) {
    var stream = FS.getStreamChecked(fd)
    var node = stream.node
    var getattr = stream.stream_ops.getattr
    var arg = getattr ? stream : node
    getattr ??= node.node_ops.getattr
    FS.checkOpExists(getattr, 63)
    return getattr(arg)
  },
  lstat(path) {
    return FS.stat(path, true)
  },
  doChmod(stream, node, mode, dontFollow) {
    FS.doSetAttr(stream, node, {
      mode: (mode & 4095) | (node.mode & ~4095),
      ctime: Date.now(),
      dontFollow,
    })
  },
  chmod(path, mode, dontFollow) {
    var node
    if (typeof path == 'string') {
      var lookup = FS.lookupPath(path, { follow: !dontFollow })
      node = lookup.node
    } else {
      node = path
    }
    FS.doChmod(null, node, mode, dontFollow)
  },
  lchmod(path, mode) {
    FS.chmod(path, mode, true)
  },
  fchmod(fd, mode) {
    var stream = FS.getStreamChecked(fd)
    FS.doChmod(stream, stream.node, mode, false)
  },
  doChown(stream, node, dontFollow) {
    FS.doSetAttr(stream, node, { timestamp: Date.now(), dontFollow })
  },
  chown(path, uid, gid, dontFollow) {
    var node
    if (typeof path == 'string') {
      var lookup = FS.lookupPath(path, { follow: !dontFollow })
      node = lookup.node
    } else {
      node = path
    }
    FS.doChown(null, node, dontFollow)
  },
  lchown(path, uid, gid) {
    FS.chown(path, uid, gid, true)
  },
  fchown(fd, uid, gid) {
    var stream = FS.getStreamChecked(fd)
    FS.doChown(stream, stream.node, false)
  },
  doTruncate(stream, node, len) {
    if (FS.isDir(node.mode)) {
      throw new FS.ErrnoError(31)
    }
    if (!FS.isFile(node.mode)) {
      throw new FS.ErrnoError(28)
    }
    var errCode = FS.nodePermissions(node, 'w')
    if (errCode) {
      throw new FS.ErrnoError(errCode)
    }
    FS.doSetAttr(stream, node, { size: len, timestamp: Date.now() })
  },
  truncate(path, len) {
    if (len < 0) {
      throw new FS.ErrnoError(28)
    }
    var node
    if (typeof path == 'string') {
      var lookup = FS.lookupPath(path, { follow: true })
      node = lookup.node
    } else {
      node = path
    }
    FS.doTruncate(null, node, len)
  },
  ftruncate(fd, len) {
    var stream = FS.getStreamChecked(fd)
    if (len < 0 || (stream.flags & 2097155) === 0) {
      throw new FS.ErrnoError(28)
    }
    FS.doTruncate(stream, stream.node, len)
  },
  utime(path, atime, mtime) {
    var lookup = FS.lookupPath(path, { follow: true })
    var node = lookup.node
    var setattr = FS.checkOpExists(node.node_ops.setattr, 63)
    setattr(node, { atime, mtime })
  },
  open(path, flags, mode = 438) {
    if (path === '') {
      throw new FS.ErrnoError(44)
    }
    flags = typeof flags == 'string' ? FS_modeStringToFlags(flags) : flags
    if (flags & 64) {
      mode = (mode & 4095) | 32768
    } else {
      mode = 0
    }
    var node
    var isDirPath
    if (typeof path == 'object') {
      node = path
    } else {
      isDirPath = path.endsWith('/')
      var lookup = FS.lookupPath(path, {
        follow: !(flags & 131072),
        noent_okay: true,
      })
      node = lookup.node
      path = lookup.path
    }
    var created = false
    if (flags & 64) {
      if (node) {
        if (flags & 128) {
          throw new FS.ErrnoError(20)
        }
      } else if (isDirPath) {
        throw new FS.ErrnoError(31)
      } else {
        node = FS.mknod(path, mode | 511, 0)
        created = true
      }
    }
    if (!node) {
      throw new FS.ErrnoError(44)
    }
    if (FS.isChrdev(node.mode)) {
      flags &= ~512
    }
    if (flags & 65536 && !FS.isDir(node.mode)) {
      throw new FS.ErrnoError(54)
    }
    if (!created) {
      var errCode = FS.mayOpen(node, flags)
      if (errCode) {
        throw new FS.ErrnoError(errCode)
      }
    }
    if (flags & 512 && !created) {
      FS.truncate(node, 0)
    }
    flags &= ~(128 | 512 | 131072)
    var stream = FS.createStream({
      node,
      path: FS.getPath(node),
      flags,
      seekable: true,
      position: 0,
      stream_ops: node.stream_ops,
      ungotten: [],
      error: false,
    })
    if (stream.stream_ops.open) {
      stream.stream_ops.open(stream)
    }
    if (created) {
      FS.chmod(node, mode & 511)
    }
    if (Module['logReadFiles'] && !(flags & 1)) {
      if (!(path in FS.readFiles)) {
        FS.readFiles[path] = 1
      }
    }
    return stream
  },
  close(stream) {
    if (FS.isClosed(stream)) {
      throw new FS.ErrnoError(8)
    }
    if (stream.getdents) stream.getdents = null
    try {
      if (stream.stream_ops.close) {
        stream.stream_ops.close(stream)
      }
    } catch (e) {
      throw e
    } finally {
      FS.closeStream(stream.fd)
    }
    stream.fd = null
  },
  isClosed(stream) {
    return stream.fd === null
  },
  llseek(stream, offset, whence) {
    if (FS.isClosed(stream)) {
      throw new FS.ErrnoError(8)
    }
    if (!stream.seekable || !stream.stream_ops.llseek) {
      throw new FS.ErrnoError(70)
    }
    if (whence != 0 && whence != 1 && whence != 2) {
      throw new FS.ErrnoError(28)
    }
    stream.position = stream.stream_ops.llseek(stream, offset, whence)
    stream.ungotten = []
    return stream.position
  },
  read(stream, buffer, offset, length, position) {
    if (length < 0 || position < 0) {
      throw new FS.ErrnoError(28)
    }
    if (FS.isClosed(stream)) {
      throw new FS.ErrnoError(8)
    }
    if ((stream.flags & 2097155) === 1) {
      throw new FS.ErrnoError(8)
    }
    if (FS.isDir(stream.node.mode)) {
      throw new FS.ErrnoError(31)
    }
    if (!stream.stream_ops.read) {
      throw new FS.ErrnoError(28)
    }
    var seeking = typeof position != 'undefined'
    if (!seeking) {
      position = stream.position
    } else if (!stream.seekable) {
      throw new FS.ErrnoError(70)
    }
    var bytesRead = stream.stream_ops.read(
      stream,
      buffer,
      offset,
      length,
      position,
    )
    if (!seeking) stream.position += bytesRead
    return bytesRead
  },
  write(stream, buffer, offset, length, position, canOwn) {
    if (length < 0 || position < 0) {
      throw new FS.ErrnoError(28)
    }
    if (FS.isClosed(stream)) {
      throw new FS.ErrnoError(8)
    }
    if ((stream.flags & 2097155) === 0) {
      throw new FS.ErrnoError(8)
    }
    if (FS.isDir(stream.node.mode)) {
      throw new FS.ErrnoError(31)
    }
    if (!stream.stream_ops.write) {
      throw new FS.ErrnoError(28)
    }
    if (stream.seekable && stream.flags & 1024) {
      FS.llseek(stream, 0, 2)
    }
    var seeking = typeof position != 'undefined'
    if (!seeking) {
      position = stream.position
    } else if (!stream.seekable) {
      throw new FS.ErrnoError(70)
    }
    var bytesWritten = stream.stream_ops.write(
      stream,
      buffer,
      offset,
      length,
      position,
      canOwn,
    )
    if (!seeking) stream.position += bytesWritten
    return bytesWritten
  },
  allocate(stream, offset, length) {
    if (FS.isClosed(stream)) {
      throw new FS.ErrnoError(8)
    }
    if (offset < 0 || length <= 0) {
      throw new FS.ErrnoError(28)
    }
    if ((stream.flags & 2097155) === 0) {
      throw new FS.ErrnoError(8)
    }
    if (!FS.isFile(stream.node.mode) && !FS.isDir(stream.node.mode)) {
      throw new FS.ErrnoError(43)
    }
    if (!stream.stream_ops.allocate) {
      throw new FS.ErrnoError(138)
    }
    stream.stream_ops.allocate(stream, offset, length)
  },
  mmap(stream, length, position, prot, flags) {
    if (
      (prot & 2) !== 0 &&
      (flags & 2) === 0 &&
      (stream.flags & 2097155) !== 2
    ) {
      throw new FS.ErrnoError(2)
    }
    if ((stream.flags & 2097155) === 1) {
      throw new FS.ErrnoError(2)
    }
    if (!stream.stream_ops.mmap) {
      throw new FS.ErrnoError(43)
    }
    if (!length) {
      throw new FS.ErrnoError(28)
    }
    return stream.stream_ops.mmap(stream, length, position, prot, flags)
  },
  msync(stream, buffer, offset, length, mmapFlags) {
    if (!stream.stream_ops.msync) {
      return 0
    }
    return stream.stream_ops.msync(stream, buffer, offset, length, mmapFlags)
  },
  ioctl(stream, cmd, arg) {
    if (!stream.stream_ops.ioctl) {
      throw new FS.ErrnoError(59)
    }
    return stream.stream_ops.ioctl(stream, cmd, arg)
  },
  readFile(path, opts = {}) {
    opts.flags = opts.flags || 0
    opts.encoding = opts.encoding || 'binary'
    if (opts.encoding !== 'utf8' && opts.encoding !== 'binary') {
      throw new Error(`Invalid encoding type "${opts.encoding}"`)
    }
    var ret
    var stream = FS.open(path, opts.flags)
    var stat = FS.stat(path)
    var length = stat.size
    var buf = new Uint8Array(length)
    FS.read(stream, buf, 0, length, 0)
    if (opts.encoding === 'utf8') {
      ret = UTF8ArrayToString(buf)
    } else if (opts.encoding === 'binary') {
      ret = buf
    }
    FS.close(stream)
    return ret
  },
  writeFile(path, data, opts = {}) {
    opts.flags = opts.flags || 577
    var stream = FS.open(path, opts.flags, opts.mode)
    if (typeof data == 'string') {
      var buf = new Uint8Array(lengthBytesUTF8(data) + 1)
      var actualNumBytes = stringToUTF8Array(data, buf, 0, buf.length)
      FS.write(stream, buf, 0, actualNumBytes, undefined, opts.canOwn)
    } else if (ArrayBuffer.isView(data)) {
      FS.write(stream, data, 0, data.byteLength, undefined, opts.canOwn)
    } else {
      throw new Error('Unsupported data type')
    }
    FS.close(stream)
  },
  cwd: () => FS.currentPath,
  chdir(path) {
    var lookup = FS.lookupPath(path, { follow: true })
    if (lookup.node === null) {
      throw new FS.ErrnoError(44)
    }
    if (!FS.isDir(lookup.node.mode)) {
      throw new FS.ErrnoError(54)
    }
    var errCode = FS.nodePermissions(lookup.node, 'x')
    if (errCode) {
      throw new FS.ErrnoError(errCode)
    }
    FS.currentPath = lookup.path
  },
  createDefaultDirectories() {
    FS.mkdir('/tmp')
    FS.mkdir('/home')
    FS.mkdir('/home/<USER>')
  },
  createDefaultDevices() {
    FS.mkdir('/dev')
    FS.registerDevice(FS.makedev(1, 3), {
      read: () => 0,
      write: (stream, buffer, offset, length, pos) => length,
      llseek: () => 0,
    })
    FS.mkdev('/dev/null', FS.makedev(1, 3))
    TTY.register(FS.makedev(5, 0), TTY.default_tty_ops)
    TTY.register(FS.makedev(6, 0), TTY.default_tty1_ops)
    FS.mkdev('/dev/tty', FS.makedev(5, 0))
    FS.mkdev('/dev/tty1', FS.makedev(6, 0))
    var randomBuffer = new Uint8Array(1024),
      randomLeft = 0
    var randomByte = () => {
      if (randomLeft === 0) {
        randomFill(randomBuffer)
        randomLeft = randomBuffer.byteLength
      }
      return randomBuffer[--randomLeft]
    }
    FS.createDevice('/dev', 'random', randomByte)
    FS.createDevice('/dev', 'urandom', randomByte)
    FS.mkdir('/dev/shm')
    FS.mkdir('/dev/shm/tmp')
  },
  createSpecialDirectories() {
    FS.mkdir('/proc')
    var proc_self = FS.mkdir('/proc/self')
    FS.mkdir('/proc/self/fd')
    FS.mount(
      {
        mount() {
          var node = FS.createNode(proc_self, 'fd', 16895, 73)
          node.stream_ops = { llseek: MEMFS.stream_ops.llseek }
          node.node_ops = {
            lookup(parent, name) {
              var fd = +name
              var stream = FS.getStreamChecked(fd)
              var ret = {
                parent: null,
                mount: { mountpoint: 'fake' },
                node_ops: { readlink: () => stream.path },
                id: fd + 1,
              }
              ret.parent = ret
              return ret
            },
            readdir() {
              return Array.from(FS.streams.entries())
                .filter(([k, v]) => v)
                .map(([k, v]) => k.toString())
            },
          }
          return node
        },
      },
      {},
      '/proc/self/fd',
    )
  },
  createStandardStreams(input, output, error) {
    if (input) {
      FS.createDevice('/dev', 'stdin', input)
    } else {
      FS.symlink('/dev/tty', '/dev/stdin')
    }
    if (output) {
      FS.createDevice('/dev', 'stdout', null, output)
    } else {
      FS.symlink('/dev/tty', '/dev/stdout')
    }
    if (error) {
      FS.createDevice('/dev', 'stderr', null, error)
    } else {
      FS.symlink('/dev/tty1', '/dev/stderr')
    }
    var stdin = FS.open('/dev/stdin', 0)
    var stdout = FS.open('/dev/stdout', 1)
    var stderr = FS.open('/dev/stderr', 1)
  },
  staticInit() {
    FS.nameTable = new Array(4096)
    FS.mount(MEMFS, {}, '/')
    FS.createDefaultDirectories()
    FS.createDefaultDevices()
    FS.createSpecialDirectories()
    FS.filesystems = { MEMFS }
  },
  init(input, output, error) {
    FS.initialized = true
    input ??= Module['stdin']
    output ??= Module['stdout']
    error ??= Module['stderr']
    FS.createStandardStreams(input, output, error)
  },
  quit() {
    FS.initialized = false
    for (var i = 0; i < FS.streams.length; i++) {
      var stream = FS.streams[i]
      if (!stream) {
        continue
      }
      FS.close(stream)
    }
  },
  findObject(path, dontResolveLastLink) {
    var ret = FS.analyzePath(path, dontResolveLastLink)
    if (!ret.exists) {
      return null
    }
    return ret.object
  },
  analyzePath(path, dontResolveLastLink) {
    try {
      var lookup = FS.lookupPath(path, { follow: !dontResolveLastLink })
      path = lookup.path
    } catch (e) {}
    var ret = {
      isRoot: false,
      exists: false,
      error: 0,
      name: null,
      path: null,
      object: null,
      parentExists: false,
      parentPath: null,
      parentObject: null,
    }
    try {
      var lookup = FS.lookupPath(path, { parent: true })
      ret.parentExists = true
      ret.parentPath = lookup.path
      ret.parentObject = lookup.node
      ret.name = PATH.basename(path)
      lookup = FS.lookupPath(path, { follow: !dontResolveLastLink })
      ret.exists = true
      ret.path = lookup.path
      ret.object = lookup.node
      ret.name = lookup.node.name
      ret.isRoot = lookup.path === '/'
    } catch (e) {
      ret.error = e.errno
    }
    return ret
  },
  createPath(parent, path, canRead, canWrite) {
    parent = typeof parent == 'string' ? parent : FS.getPath(parent)
    var parts = path.split('/').reverse()
    while (parts.length) {
      var part = parts.pop()
      if (!part) continue
      var current = PATH.join2(parent, part)
      try {
        FS.mkdir(current)
      } catch (e) {}
      parent = current
    }
    return current
  },
  createFile(parent, name, properties, canRead, canWrite) {
    var path = PATH.join2(
      typeof parent == 'string' ? parent : FS.getPath(parent),
      name,
    )
    var mode = FS_getMode(canRead, canWrite)
    return FS.create(path, mode)
  },
  createDataFile(parent, name, data, canRead, canWrite, canOwn) {
    var path = name
    if (parent) {
      parent = typeof parent == 'string' ? parent : FS.getPath(parent)
      path = name ? PATH.join2(parent, name) : parent
    }
    var mode = FS_getMode(canRead, canWrite)
    var node = FS.create(path, mode)
    if (data) {
      if (typeof data == 'string') {
        var arr = new Array(data.length)
        for (var i = 0, len = data.length; i < len; ++i)
          arr[i] = data.charCodeAt(i)
        data = arr
      }
      FS.chmod(node, mode | 146)
      var stream = FS.open(node, 577)
      FS.write(stream, data, 0, data.length, 0, canOwn)
      FS.close(stream)
      FS.chmod(node, mode)
    }
  },
  createDevice(parent, name, input, output) {
    var path = PATH.join2(
      typeof parent == 'string' ? parent : FS.getPath(parent),
      name,
    )
    var mode = FS_getMode(!!input, !!output)
    FS.createDevice.major ??= 64
    var dev = FS.makedev(FS.createDevice.major++, 0)
    FS.registerDevice(dev, {
      open(stream) {
        stream.seekable = false
      },
      close(stream) {
        if (output?.buffer?.length) {
          output(10)
        }
      },
      read(stream, buffer, offset, length, pos) {
        var bytesRead = 0
        for (var i = 0; i < length; i++) {
          var result
          try {
            result = input()
          } catch (e) {
            throw new FS.ErrnoError(29)
          }
          if (result === undefined && bytesRead === 0) {
            throw new FS.ErrnoError(6)
          }
          if (result === null || result === undefined) break
          bytesRead++
          buffer[offset + i] = result
        }
        if (bytesRead) {
          stream.node.atime = Date.now()
        }
        return bytesRead
      },
      write(stream, buffer, offset, length, pos) {
        for (var i = 0; i < length; i++) {
          try {
            output(buffer[offset + i])
          } catch (e) {
            throw new FS.ErrnoError(29)
          }
        }
        if (length) {
          stream.node.mtime = stream.node.ctime = Date.now()
        }
        return i
      },
    })
    return FS.mkdev(path, mode, dev)
  },
  forceLoadFile(obj) {
    if (obj.isDevice || obj.isFolder || obj.link || obj.contents) return true
    if (typeof XMLHttpRequest != 'undefined') {
      throw new Error(
        'Lazy loading should have been performed (contents set) in createLazyFile, but it was not. Lazy loading only works in web workers. Use --embed-file or --preload-file in emcc on the main thread.',
      )
    } else {
      try {
        obj.contents = readBinary(obj.url)
        obj.usedBytes = obj.contents.length
      } catch (e) {
        throw new FS.ErrnoError(29)
      }
    }
  },
  createLazyFile(parent, name, url, canRead, canWrite) {
    class LazyUint8Array {
      lengthKnown = false
      chunks = []
      get(idx) {
        if (idx > this.length - 1 || idx < 0) {
          return undefined
        }
        var chunkOffset = idx % this.chunkSize
        var chunkNum = (idx / this.chunkSize) | 0
        return this.getter(chunkNum)[chunkOffset]
      }
      setDataGetter(getter) {
        this.getter = getter
      }
      cacheLength() {
        var xhr = new XMLHttpRequest()
        xhr.open('HEAD', url, false)
        xhr.send(null)
        if (!((xhr.status >= 200 && xhr.status < 300) || xhr.status === 304))
          throw new Error("Couldn't load " + url + '. Status: ' + xhr.status)
        var datalength = Number(xhr.getResponseHeader('Content-length'))
        var header
        var hasByteServing =
          (header = xhr.getResponseHeader('Accept-Ranges')) &&
          header === 'bytes'
        var usesGzip =
          (header = xhr.getResponseHeader('Content-Encoding')) &&
          header === 'gzip'
        var chunkSize = 1024 * 1024
        if (!hasByteServing) chunkSize = datalength
        var doXHR = (from, to) => {
          if (from > to)
            throw new Error(
              'invalid range (' + from + ', ' + to + ') or no bytes requested!',
            )
          if (to > datalength - 1)
            throw new Error(
              'only ' + datalength + ' bytes available! programmer error!',
            )
          var xhr = new XMLHttpRequest()
          xhr.open('GET', url, false)
          if (datalength !== chunkSize)
            xhr.setRequestHeader('Range', 'bytes=' + from + '-' + to)
          xhr.responseType = 'arraybuffer'
          if (xhr.overrideMimeType) {
            xhr.overrideMimeType('text/plain; charset=x-user-defined')
          }
          xhr.send(null)
          if (!((xhr.status >= 200 && xhr.status < 300) || xhr.status === 304))
            throw new Error("Couldn't load " + url + '. Status: ' + xhr.status)
          if (xhr.response !== undefined) {
            return new Uint8Array(xhr.response || [])
          }
          return intArrayFromString(xhr.responseText || '', true)
        }
        var lazyArray = this
        lazyArray.setDataGetter(chunkNum => {
          var start = chunkNum * chunkSize
          var end = (chunkNum + 1) * chunkSize - 1
          end = Math.min(end, datalength - 1)
          if (typeof lazyArray.chunks[chunkNum] == 'undefined') {
            lazyArray.chunks[chunkNum] = doXHR(start, end)
          }
          if (typeof lazyArray.chunks[chunkNum] == 'undefined')
            throw new Error('doXHR failed!')
          return lazyArray.chunks[chunkNum]
        })
        if (usesGzip || !datalength) {
          chunkSize = datalength = 1
          datalength = this.getter(0).length
          chunkSize = datalength
          out(
            'LazyFiles on gzip forces download of the whole file when length is accessed',
          )
        }
        this._length = datalength
        this._chunkSize = chunkSize
        this.lengthKnown = true
      }
      get length() {
        if (!this.lengthKnown) {
          this.cacheLength()
        }
        return this._length
      }
      get chunkSize() {
        if (!this.lengthKnown) {
          this.cacheLength()
        }
        return this._chunkSize
      }
    }
    if (typeof XMLHttpRequest != 'undefined') {
      if (!ENVIRONMENT_IS_WORKER)
        throw 'Cannot do synchronous binary XHRs outside webworkers in modern browsers. Use --embed-file or --preload-file in emcc'
      var lazyArray = new LazyUint8Array()
      var properties = { isDevice: false, contents: lazyArray }
    } else {
      var properties = { isDevice: false, url }
    }
    var node = FS.createFile(parent, name, properties, canRead, canWrite)
    if (properties.contents) {
      node.contents = properties.contents
    } else if (properties.url) {
      node.contents = null
      node.url = properties.url
    }
    Object.defineProperties(node, {
      usedBytes: {
        get: function () {
          return this.contents.length
        },
      },
    })
    var stream_ops = {}
    var keys = Object.keys(node.stream_ops)
    keys.forEach(key => {
      var fn = node.stream_ops[key]
      stream_ops[key] = (...args) => {
        FS.forceLoadFile(node)
        return fn(...args)
      }
    })
    function writeChunks(stream, buffer, offset, length, position) {
      var contents = stream.node.contents
      if (position >= contents.length) return 0
      var size = Math.min(contents.length - position, length)
      if (contents.slice) {
        for (var i = 0; i < size; i++) {
          buffer[offset + i] = contents[position + i]
        }
      } else {
        for (var i = 0; i < size; i++) {
          buffer[offset + i] = contents.get(position + i)
        }
      }
      return size
    }
    stream_ops.read = (stream, buffer, offset, length, position) => {
      FS.forceLoadFile(node)
      return writeChunks(stream, buffer, offset, length, position)
    }
    stream_ops.mmap = (stream, length, position, prot, flags) => {
      FS.forceLoadFile(node)
      var ptr = mmapAlloc(length)
      if (!ptr) {
        throw new FS.ErrnoError(48)
      }
      writeChunks(stream, HEAP8, ptr, length, position)
      return { ptr, allocated: true }
    }
    node.stream_ops = stream_ops
    return node
  },
}
var SYSCALLS = {
  DEFAULT_POLLMASK: 5,
  calculateAt(dirfd, path, allowEmpty) {
    if (PATH.isAbs(path)) {
      return path
    }
    var dir
    if (dirfd === -100) {
      dir = FS.cwd()
    } else {
      var dirstream = SYSCALLS.getStreamFromFD(dirfd)
      dir = dirstream.path
    }
    if (path.length == 0) {
      if (!allowEmpty) {
        throw new FS.ErrnoError(44)
      }
      return dir
    }
    return dir + '/' + path
  },
  writeStat(buf, stat) {
    HEAP32[buf >> 2] = stat.dev
    HEAP32[(buf + 4) >> 2] = stat.mode
    HEAPU32[(buf + 8) >> 2] = stat.nlink
    HEAP32[(buf + 12) >> 2] = stat.uid
    HEAP32[(buf + 16) >> 2] = stat.gid
    HEAP32[(buf + 20) >> 2] = stat.rdev
    HEAP64[(buf + 24) >> 3] = BigInt(stat.size)
    HEAP32[(buf + 32) >> 2] = 4096
    HEAP32[(buf + 36) >> 2] = stat.blocks
    var atime = stat.atime.getTime()
    var mtime = stat.mtime.getTime()
    var ctime = stat.ctime.getTime()
    HEAP64[(buf + 40) >> 3] = BigInt(Math.floor(atime / 1e3))
    HEAPU32[(buf + 48) >> 2] = (atime % 1e3) * 1e3 * 1e3
    HEAP64[(buf + 56) >> 3] = BigInt(Math.floor(mtime / 1e3))
    HEAPU32[(buf + 64) >> 2] = (mtime % 1e3) * 1e3 * 1e3
    HEAP64[(buf + 72) >> 3] = BigInt(Math.floor(ctime / 1e3))
    HEAPU32[(buf + 80) >> 2] = (ctime % 1e3) * 1e3 * 1e3
    HEAP64[(buf + 88) >> 3] = BigInt(stat.ino)
    return 0
  },
  writeStatFs(buf, stats) {
    HEAP32[(buf + 4) >> 2] = stats.bsize
    HEAP32[(buf + 40) >> 2] = stats.bsize
    HEAP32[(buf + 8) >> 2] = stats.blocks
    HEAP32[(buf + 12) >> 2] = stats.bfree
    HEAP32[(buf + 16) >> 2] = stats.bavail
    HEAP32[(buf + 20) >> 2] = stats.files
    HEAP32[(buf + 24) >> 2] = stats.ffree
    HEAP32[(buf + 28) >> 2] = stats.fsid
    HEAP32[(buf + 44) >> 2] = stats.flags
    HEAP32[(buf + 36) >> 2] = stats.namelen
  },
  doMsync(addr, stream, len, flags, offset) {
    if (!FS.isFile(stream.node.mode)) {
      throw new FS.ErrnoError(43)
    }
    if (flags & 2) {
      return 0
    }
    var buffer = HEAPU8.slice(addr, addr + len)
    FS.msync(stream, buffer, offset, len, flags)
  },
  getStreamFromFD(fd) {
    var stream = FS.getStreamChecked(fd)
    return stream
  },
  varargs: undefined,
  getStr(ptr) {
    var ret = UTF8ToString(ptr)
    return ret
  },
}
function ___syscall_chdir(path) {
  try {
    path = SYSCALLS.getStr(path)
    FS.chdir(path)
    return 0
  } catch (e) {
    if (typeof FS == 'undefined' || !(e.name === 'ErrnoError')) throw e
    return -e.errno
  }
}
function ___syscall_faccessat(dirfd, path, amode, flags) {
  try {
    path = SYSCALLS.getStr(path)
    path = SYSCALLS.calculateAt(dirfd, path)
    if (amode & ~7) {
      return -28
    }
    var lookup = FS.lookupPath(path, { follow: true })
    var node = lookup.node
    if (!node) {
      return -44
    }
    var perms = ''
    if (amode & 4) perms += 'r'
    if (amode & 2) perms += 'w'
    if (amode & 1) perms += 'x'
    if (perms && FS.nodePermissions(node, perms)) {
      return -2
    }
    return 0
  } catch (e) {
    if (typeof FS == 'undefined' || !(e.name === 'ErrnoError')) throw e
    return -e.errno
  }
}
var syscallGetVarargI = () => {
  var ret = HEAP32[+SYSCALLS.varargs >> 2]
  SYSCALLS.varargs += 4
  return ret
}
var syscallGetVarargP = syscallGetVarargI
function ___syscall_fcntl64(fd, cmd, varargs) {
  SYSCALLS.varargs = varargs
  try {
    var stream = SYSCALLS.getStreamFromFD(fd)
    switch (cmd) {
      case 0: {
        var arg = syscallGetVarargI()
        if (arg < 0) {
          return -28
        }
        while (FS.streams[arg]) {
          arg++
        }
        var newStream
        newStream = FS.dupStream(stream, arg)
        return newStream.fd
      }
      case 1:
      case 2:
        return 0
      case 3:
        return stream.flags
      case 4: {
        var arg = syscallGetVarargI()
        stream.flags |= arg
        return 0
      }
      case 12: {
        var arg = syscallGetVarargP()
        var offset = 0
        HEAP16[(arg + offset) >> 1] = 2
        return 0
      }
      case 13:
      case 14:
        return 0
    }
    return -28
  } catch (e) {
    if (typeof FS == 'undefined' || !(e.name === 'ErrnoError')) throw e
    return -e.errno
  }
}
function ___syscall_fstat64(fd, buf) {
  try {
    return SYSCALLS.writeStat(buf, FS.fstat(fd))
  } catch (e) {
    if (typeof FS == 'undefined' || !(e.name === 'ErrnoError')) throw e
    return -e.errno
  }
}
var stringToUTF8 = (str, outPtr, maxBytesToWrite) =>
  stringToUTF8Array(str, HEAPU8, outPtr, maxBytesToWrite)
function ___syscall_getcwd(buf, size) {
  try {
    if (size === 0) return -28
    var cwd = FS.cwd()
    var cwdLengthInBytes = lengthBytesUTF8(cwd) + 1
    if (size < cwdLengthInBytes) return -68
    stringToUTF8(cwd, buf, size)
    return cwdLengthInBytes
  } catch (e) {
    if (typeof FS == 'undefined' || !(e.name === 'ErrnoError')) throw e
    return -e.errno
  }
}
function ___syscall_getdents64(fd, dirp, count) {
  try {
    var stream = SYSCALLS.getStreamFromFD(fd)
    stream.getdents ||= FS.readdir(stream.path)
    var struct_size = 280
    var pos = 0
    var off = FS.llseek(stream, 0, 1)
    var startIdx = Math.floor(off / struct_size)
    var endIdx = Math.min(
      stream.getdents.length,
      startIdx + Math.floor(count / struct_size),
    )
    for (var idx = startIdx; idx < endIdx; idx++) {
      var id
      var type
      var name = stream.getdents[idx]
      if (name === '.') {
        id = stream.node.id
        type = 4
      } else if (name === '..') {
        var lookup = FS.lookupPath(stream.path, { parent: true })
        id = lookup.node.id
        type = 4
      } else {
        var child
        try {
          child = FS.lookupNode(stream.node, name)
        } catch (e) {
          if (e?.errno === 28) {
            continue
          }
          throw e
        }
        id = child.id
        type = FS.isChrdev(child.mode)
          ? 2
          : FS.isDir(child.mode)
            ? 4
            : FS.isLink(child.mode)
              ? 10
              : 8
      }
      HEAP64[(dirp + pos) >> 3] = BigInt(id)
      HEAP64[(dirp + pos + 8) >> 3] = BigInt((idx + 1) * struct_size)
      HEAP16[(dirp + pos + 16) >> 1] = 280
      HEAP8[dirp + pos + 18] = type
      stringToUTF8(name, dirp + pos + 19, 256)
      pos += struct_size
    }
    FS.llseek(stream, idx * struct_size, 0)
    return pos
  } catch (e) {
    if (typeof FS == 'undefined' || !(e.name === 'ErrnoError')) throw e
    return -e.errno
  }
}
function ___syscall_ioctl(fd, op, varargs) {
  SYSCALLS.varargs = varargs
  try {
    var stream = SYSCALLS.getStreamFromFD(fd)
    switch (op) {
      case 21509: {
        if (!stream.tty) return -59
        return 0
      }
      case 21505: {
        if (!stream.tty) return -59
        if (stream.tty.ops.ioctl_tcgets) {
          var termios = stream.tty.ops.ioctl_tcgets(stream)
          var argp = syscallGetVarargP()
          HEAP32[argp >> 2] = termios.c_iflag || 0
          HEAP32[(argp + 4) >> 2] = termios.c_oflag || 0
          HEAP32[(argp + 8) >> 2] = termios.c_cflag || 0
          HEAP32[(argp + 12) >> 2] = termios.c_lflag || 0
          for (var i = 0; i < 32; i++) {
            HEAP8[argp + i + 17] = termios.c_cc[i] || 0
          }
          return 0
        }
        return 0
      }
      case 21510:
      case 21511:
      case 21512: {
        if (!stream.tty) return -59
        return 0
      }
      case 21506:
      case 21507:
      case 21508: {
        if (!stream.tty) return -59
        if (stream.tty.ops.ioctl_tcsets) {
          var argp = syscallGetVarargP()
          var c_iflag = HEAP32[argp >> 2]
          var c_oflag = HEAP32[(argp + 4) >> 2]
          var c_cflag = HEAP32[(argp + 8) >> 2]
          var c_lflag = HEAP32[(argp + 12) >> 2]
          var c_cc = []
          for (var i = 0; i < 32; i++) {
            c_cc.push(HEAP8[argp + i + 17])
          }
          return stream.tty.ops.ioctl_tcsets(stream.tty, op, {
            c_iflag,
            c_oflag,
            c_cflag,
            c_lflag,
            c_cc,
          })
        }
        return 0
      }
      case 21519: {
        if (!stream.tty) return -59
        var argp = syscallGetVarargP()
        HEAP32[argp >> 2] = 0
        return 0
      }
      case 21520: {
        if (!stream.tty) return -59
        return -28
      }
      case 21531: {
        var argp = syscallGetVarargP()
        return FS.ioctl(stream, op, argp)
      }
      case 21523: {
        if (!stream.tty) return -59
        if (stream.tty.ops.ioctl_tiocgwinsz) {
          var winsize = stream.tty.ops.ioctl_tiocgwinsz(stream.tty)
          var argp = syscallGetVarargP()
          HEAP16[argp >> 1] = winsize[0]
          HEAP16[(argp + 2) >> 1] = winsize[1]
        }
        return 0
      }
      case 21524: {
        if (!stream.tty) return -59
        return 0
      }
      case 21515: {
        if (!stream.tty) return -59
        return 0
      }
      default:
        return -28
    }
  } catch (e) {
    if (typeof FS == 'undefined' || !(e.name === 'ErrnoError')) throw e
    return -e.errno
  }
}
function ___syscall_lstat64(path, buf) {
  try {
    path = SYSCALLS.getStr(path)
    return SYSCALLS.writeStat(buf, FS.lstat(path))
  } catch (e) {
    if (typeof FS == 'undefined' || !(e.name === 'ErrnoError')) throw e
    return -e.errno
  }
}
function ___syscall_newfstatat(dirfd, path, buf, flags) {
  try {
    path = SYSCALLS.getStr(path)
    var nofollow = flags & 256
    var allowEmpty = flags & 4096
    flags = flags & ~6400
    path = SYSCALLS.calculateAt(dirfd, path, allowEmpty)
    return SYSCALLS.writeStat(buf, nofollow ? FS.lstat(path) : FS.stat(path))
  } catch (e) {
    if (typeof FS == 'undefined' || !(e.name === 'ErrnoError')) throw e
    return -e.errno
  }
}
function ___syscall_openat(dirfd, path, flags, varargs) {
  SYSCALLS.varargs = varargs
  try {
    path = SYSCALLS.getStr(path)
    path = SYSCALLS.calculateAt(dirfd, path)
    var mode = varargs ? syscallGetVarargI() : 0
    return FS.open(path, flags, mode).fd
  } catch (e) {
    if (typeof FS == 'undefined' || !(e.name === 'ErrnoError')) throw e
    return -e.errno
  }
}
function ___syscall_readlinkat(dirfd, path, buf, bufsize) {
  try {
    path = SYSCALLS.getStr(path)
    path = SYSCALLS.calculateAt(dirfd, path)
    if (bufsize <= 0) return -28
    var ret = FS.readlink(path)
    var len = Math.min(bufsize, lengthBytesUTF8(ret))
    var endChar = HEAP8[buf + len]
    stringToUTF8(ret, buf, bufsize + 1)
    HEAP8[buf + len] = endChar
    return len
  } catch (e) {
    if (typeof FS == 'undefined' || !(e.name === 'ErrnoError')) throw e
    return -e.errno
  }
}
function ___syscall_renameat(olddirfd, oldpath, newdirfd, newpath) {
  try {
    oldpath = SYSCALLS.getStr(oldpath)
    newpath = SYSCALLS.getStr(newpath)
    oldpath = SYSCALLS.calculateAt(olddirfd, oldpath)
    newpath = SYSCALLS.calculateAt(newdirfd, newpath)
    FS.rename(oldpath, newpath)
    return 0
  } catch (e) {
    if (typeof FS == 'undefined' || !(e.name === 'ErrnoError')) throw e
    return -e.errno
  }
}
function ___syscall_rmdir(path) {
  try {
    path = SYSCALLS.getStr(path)
    FS.rmdir(path)
    return 0
  } catch (e) {
    if (typeof FS == 'undefined' || !(e.name === 'ErrnoError')) throw e
    return -e.errno
  }
}
function ___syscall_stat64(path, buf) {
  try {
    path = SYSCALLS.getStr(path)
    return SYSCALLS.writeStat(buf, FS.stat(path))
  } catch (e) {
    if (typeof FS == 'undefined' || !(e.name === 'ErrnoError')) throw e
    return -e.errno
  }
}
function ___syscall_unlinkat(dirfd, path, flags) {
  try {
    path = SYSCALLS.getStr(path)
    path = SYSCALLS.calculateAt(dirfd, path)
    if (flags === 0) {
      FS.unlink(path)
    } else if (flags === 512) {
      FS.rmdir(path)
    } else {
      abort('Invalid flags passed to unlinkat')
    }
    return 0
  } catch (e) {
    if (typeof FS == 'undefined' || !(e.name === 'ErrnoError')) throw e
    return -e.errno
  }
}
var __abort_js = () => abort('')
var __emscripten_throw_longjmp = () => {
  throw Infinity
}
var isLeapYear = year =>
  year % 4 === 0 && (year % 100 !== 0 || year % 400 === 0)
var MONTH_DAYS_LEAP_CUMULATIVE = [
  0, 31, 60, 91, 121, 152, 182, 213, 244, 274, 305, 335,
]
var MONTH_DAYS_REGULAR_CUMULATIVE = [
  0, 31, 59, 90, 120, 151, 181, 212, 243, 273, 304, 334,
]
var ydayFromDate = date => {
  var leap = isLeapYear(date.getFullYear())
  var monthDaysCumulative = leap
    ? MONTH_DAYS_LEAP_CUMULATIVE
    : MONTH_DAYS_REGULAR_CUMULATIVE
  var yday = monthDaysCumulative[date.getMonth()] + date.getDate() - 1
  return yday
}
var INT53_MAX = 9007199254740992
var INT53_MIN = -9007199254740992
var bigintToI53Checked = num =>
  num < INT53_MIN || num > INT53_MAX ? NaN : Number(num)
function __localtime_js(time, tmPtr) {
  time = bigintToI53Checked(time)
  var date = new Date(time * 1e3)
  HEAP32[tmPtr >> 2] = date.getSeconds()
  HEAP32[(tmPtr + 4) >> 2] = date.getMinutes()
  HEAP32[(tmPtr + 8) >> 2] = date.getHours()
  HEAP32[(tmPtr + 12) >> 2] = date.getDate()
  HEAP32[(tmPtr + 16) >> 2] = date.getMonth()
  HEAP32[(tmPtr + 20) >> 2] = date.getFullYear() - 1900
  HEAP32[(tmPtr + 24) >> 2] = date.getDay()
  var yday = ydayFromDate(date) | 0
  HEAP32[(tmPtr + 28) >> 2] = yday
  HEAP32[(tmPtr + 36) >> 2] = -(date.getTimezoneOffset() * 60)
  var start = new Date(date.getFullYear(), 0, 1)
  var summerOffset = new Date(date.getFullYear(), 6, 1).getTimezoneOffset()
  var winterOffset = start.getTimezoneOffset()
  var dst =
    (summerOffset != winterOffset &&
      date.getTimezoneOffset() == Math.min(winterOffset, summerOffset)) | 0
  HEAP32[(tmPtr + 32) >> 2] = dst
}
var __tzset_js = (timezone, daylight, std_name, dst_name) => {
  var currentYear = new Date().getFullYear()
  var winter = new Date(currentYear, 0, 1)
  var summer = new Date(currentYear, 6, 1)
  var winterOffset = winter.getTimezoneOffset()
  var summerOffset = summer.getTimezoneOffset()
  var stdTimezoneOffset = Math.max(winterOffset, summerOffset)
  HEAPU32[timezone >> 2] = stdTimezoneOffset * 60
  HEAP32[daylight >> 2] = Number(winterOffset != summerOffset)
  var extractZone = timezoneOffset => {
    var sign = timezoneOffset >= 0 ? '-' : '+'
    var absOffset = Math.abs(timezoneOffset)
    var hours = String(Math.floor(absOffset / 60)).padStart(2, '0')
    var minutes = String(absOffset % 60).padStart(2, '0')
    return `UTC${sign}${hours}${minutes}`
  }
  var winterName = extractZone(winterOffset)
  var summerName = extractZone(summerOffset)
  if (summerOffset < winterOffset) {
    stringToUTF8(winterName, std_name, 17)
    stringToUTF8(summerName, dst_name, 17)
  } else {
    stringToUTF8(winterName, dst_name, 17)
    stringToUTF8(summerName, std_name, 17)
  }
}
var _emscripten_date_now = () => Date.now()
var getHeapMax = () => 2147483648
var growMemory = size => {
  var b = wasmMemory.buffer
  var pages = ((size - b.byteLength + 65535) / 65536) | 0
  try {
    wasmMemory.grow(pages)
    updateMemoryViews()
    return 1
  } catch (e) {}
}
var _emscripten_resize_heap = requestedSize => {
  var oldSize = HEAPU8.length
  requestedSize >>>= 0
  var maxHeapSize = getHeapMax()
  if (requestedSize > maxHeapSize) {
    return false
  }
  for (var cutDown = 1; cutDown <= 4; cutDown *= 2) {
    var overGrownHeapSize = oldSize * (1 + 0.2 / cutDown)
    overGrownHeapSize = Math.min(overGrownHeapSize, requestedSize + 100663296)
    var newSize = Math.min(
      maxHeapSize,
      alignMemory(Math.max(requestedSize, overGrownHeapSize), 65536),
    )
    var replacement = growMemory(newSize)
    if (replacement) {
      return true
    }
  }
  return false
}
var ENV = {}
var getExecutableName = () => thisProgram || './this.program'
var getEnvStrings = () => {
  if (!getEnvStrings.strings) {
    var lang =
      (
        (typeof navigator == 'object' &&
          navigator.languages &&
          navigator.languages[0]) ||
        'C'
      ).replace('-', '_') + '.UTF-8'
    var env = {
      USER: 'web_user',
      LOGNAME: 'web_user',
      PATH: '/',
      PWD: '/',
      HOME: '/home/<USER>',
      LANG: lang,
      _: getExecutableName(),
    }
    for (var x in ENV) {
      if (ENV[x] === undefined) delete env[x]
      else env[x] = ENV[x]
    }
    var strings = []
    for (var x in env) {
      strings.push(`${x}=${env[x]}`)
    }
    getEnvStrings.strings = strings
  }
  return getEnvStrings.strings
}
var stringToAscii = (str, buffer) => {
  for (var i = 0; i < str.length; ++i) {
    HEAP8[buffer++] = str.charCodeAt(i)
  }
  HEAP8[buffer] = 0
}
var _environ_get = (__environ, environ_buf) => {
  var bufSize = 0
  getEnvStrings().forEach((string, i) => {
    var ptr = environ_buf + bufSize
    HEAPU32[(__environ + i * 4) >> 2] = ptr
    stringToAscii(string, ptr)
    bufSize += string.length + 1
  })
  return 0
}
var _environ_sizes_get = (penviron_count, penviron_buf_size) => {
  var strings = getEnvStrings()
  HEAPU32[penviron_count >> 2] = strings.length
  var bufSize = 0
  strings.forEach(string => (bufSize += string.length + 1))
  HEAPU32[penviron_buf_size >> 2] = bufSize
  return 0
}
var runtimeKeepaliveCounter = 0
var keepRuntimeAlive = () => noExitRuntime || runtimeKeepaliveCounter > 0
var _proc_exit = code => {
  EXITSTATUS = code
  if (!keepRuntimeAlive()) {
    Module['onExit']?.(code)
    ABORT = true
  }
  quit_(code, new ExitStatus(code))
}
var exitJS = (status, implicit) => {
  EXITSTATUS = status
  _proc_exit(status)
}
var _exit = exitJS
function _fd_close(fd) {
  try {
    var stream = SYSCALLS.getStreamFromFD(fd)
    FS.close(stream)
    return 0
  } catch (e) {
    if (typeof FS == 'undefined' || !(e.name === 'ErrnoError')) throw e
    return e.errno
  }
}
var doReadv = (stream, iov, iovcnt, offset) => {
  var ret = 0
  for (var i = 0; i < iovcnt; i++) {
    var ptr = HEAPU32[iov >> 2]
    var len = HEAPU32[(iov + 4) >> 2]
    iov += 8
    var curr = FS.read(stream, HEAP8, ptr, len, offset)
    if (curr < 0) return -1
    ret += curr
    if (curr < len) break
    if (typeof offset != 'undefined') {
      offset += curr
    }
  }
  return ret
}
function _fd_read(fd, iov, iovcnt, pnum) {
  try {
    var stream = SYSCALLS.getStreamFromFD(fd)
    var num = doReadv(stream, iov, iovcnt)
    HEAPU32[pnum >> 2] = num
    return 0
  } catch (e) {
    if (typeof FS == 'undefined' || !(e.name === 'ErrnoError')) throw e
    return e.errno
  }
}
function _fd_seek(fd, offset, whence, newOffset) {
  offset = bigintToI53Checked(offset)
  try {
    if (isNaN(offset)) return 61
    var stream = SYSCALLS.getStreamFromFD(fd)
    FS.llseek(stream, offset, whence)
    HEAP64[newOffset >> 3] = BigInt(stream.position)
    if (stream.getdents && offset === 0 && whence === 0) stream.getdents = null
    return 0
  } catch (e) {
    if (typeof FS == 'undefined' || !(e.name === 'ErrnoError')) throw e
    return e.errno
  }
}
var doWritev = (stream, iov, iovcnt, offset) => {
  var ret = 0
  for (var i = 0; i < iovcnt; i++) {
    var ptr = HEAPU32[iov >> 2]
    var len = HEAPU32[(iov + 4) >> 2]
    iov += 8
    var curr = FS.write(stream, HEAP8, ptr, len, offset)
    if (curr < 0) return -1
    ret += curr
    if (curr < len) {
      break
    }
    if (typeof offset != 'undefined') {
      offset += curr
    }
  }
  return ret
}
function _fd_write(fd, iov, iovcnt, pnum) {
  try {
    var stream = SYSCALLS.getStreamFromFD(fd)
    var num = doWritev(stream, iov, iovcnt)
    HEAPU32[pnum >> 2] = num
    return 0
  } catch (e) {
    if (typeof FS == 'undefined' || !(e.name === 'ErrnoError')) throw e
    return e.errno
  }
}
function _kpse_find_file_js(nameptr, format, mustexist) {
  return kpse_find_file_impl(nameptr, format, mustexist)
}
function _kpse_find_pk_js(nameptr, dpi) {
  return kpse_find_pk_impl(nameptr, dpi)
}
var handleException = e => {
  if (e instanceof ExitStatus || e == 'unwind') {
    return EXITSTATUS
  }
  quit_(1, e)
}
var stackAlloc = sz => __emscripten_stack_alloc(sz)
var stringToUTF8OnStack = str => {
  var size = lengthBytesUTF8(str) + 1
  var ret = stackAlloc(size)
  stringToUTF8(str, ret, size)
  return ret
}
var wasmTableMirror = []
var wasmTable
var getWasmTableEntry = funcPtr => {
  var func = wasmTableMirror[funcPtr]
  if (!func) {
    if (funcPtr >= wasmTableMirror.length) wasmTableMirror.length = funcPtr + 1
    wasmTableMirror[funcPtr] = func = wasmTable.get(funcPtr)
  }
  return func
}
var getCFunc = ident => {
  var func = Module['_' + ident]
  return func
}
var writeArrayToMemory = (array, buffer) => {
  HEAP8.set(array, buffer)
}
var ccall = (ident, returnType, argTypes, args, opts) => {
  var toC = {
    string: str => {
      var ret = 0
      if (str !== null && str !== undefined && str !== 0) {
        ret = stringToUTF8OnStack(str)
      }
      return ret
    },
    array: arr => {
      var ret = stackAlloc(arr.length)
      writeArrayToMemory(arr, ret)
      return ret
    },
  }
  function convertReturnValue(ret) {
    if (returnType === 'string') {
      return UTF8ToString(ret)
    }
    if (returnType === 'boolean') return Boolean(ret)
    return ret
  }
  var func = getCFunc(ident)
  var cArgs = []
  var stack = 0
  if (args) {
    for (var i = 0; i < args.length; i++) {
      var converter = toC[argTypes[i]]
      if (converter) {
        if (stack === 0) stack = stackSave()
        cArgs[i] = converter(args[i])
      } else {
        cArgs[i] = args[i]
      }
    }
  }
  var ret = func(...cArgs)
  function onDone(ret) {
    if (stack !== 0) stackRestore(stack)
    return convertReturnValue(ret)
  }
  ret = onDone(ret)
  return ret
}
var cwrap = (ident, returnType, argTypes, opts) => {
  var numericArgs =
    !argTypes || argTypes.every(type => type === 'number' || type === 'boolean')
  var numericRet = returnType !== 'string'
  if (numericRet && numericArgs && !opts) {
    return getCFunc(ident)
  }
  return (...args) => ccall(ident, returnType, argTypes, args, opts)
}
FS.createPreloadedFile = FS_createPreloadedFile
FS.staticInit()
MEMFS.doesNotExistError = new FS.ErrnoError(44)
MEMFS.doesNotExistError.stack = '<generic error, no stack>'
var wasmImports = {
  c: ___assert_fail,
  p: ___cxa_begin_catch,
  t: ___cxa_end_catch,
  a: ___cxa_find_matching_catch_2,
  i: ___cxa_find_matching_catch_3,
  J: ___cxa_rethrow,
  n: ___cxa_throw,
  L: ___cxa_uncaught_exceptions,
  f: ___resumeException,
  ca: ___syscall_chdir,
  da: ___syscall_faccessat,
  s: ___syscall_fcntl64,
  ba: ___syscall_fstat64,
  Z: ___syscall_getcwd,
  T: ___syscall_getdents64,
  fa: ___syscall_ioctl,
  _: ___syscall_lstat64,
  $: ___syscall_newfstatat,
  F: ___syscall_openat,
  S: ___syscall_readlinkat,
  P: ___syscall_renameat,
  Q: ___syscall_rmdir,
  aa: ___syscall_stat64,
  R: ___syscall_unlinkat,
  ea: __abort_js,
  N: __emscripten_throw_longjmp,
  U: __localtime_js,
  V: __tzset_js,
  G: _emscripten_date_now,
  O: _emscripten_resize_heap,
  X: _environ_get,
  Y: _environ_sizes_get,
  A: _exit,
  w: _fd_close,
  H: _fd_read,
  W: _fd_seek,
  B: _fd_write,
  C: invoke_diii,
  D: invoke_fiii,
  o: invoke_i,
  b: invoke_ii,
  d: invoke_iii,
  m: invoke_iiii,
  h: invoke_iiiii,
  j: invoke_iiiiii,
  u: invoke_iiiiiii,
  E: invoke_iiiiiiii,
  y: invoke_iiiiiiiiiiii,
  z: invoke_jiiii,
  k: invoke_v,
  g: invoke_vi,
  e: invoke_vii,
  l: invoke_viii,
  q: invoke_viiii,
  M: invoke_viiiii,
  I: invoke_viiiiii,
  r: invoke_viiiiiii,
  v: invoke_viiiiiiiiii,
  x: invoke_viiiiiiiiiiiiiii,
  K: _kpse_find_file_js,
  ga: _kpse_find_pk_js,
}
var wasmExports
createWasm()
var ___wasm_call_ctors = () => (___wasm_call_ctors = wasmExports['ia'])()
var _compileLaTeX = (Module['_compileLaTeX'] = () =>
  (_compileLaTeX = Module['_compileLaTeX'] = wasmExports['ja'])())
var _compileFormat = (Module['_compileFormat'] = () =>
  (_compileFormat = Module['_compileFormat'] = wasmExports['ka'])())
var _compileBibLatex = (Module['_compileBibLatex'] = () =>
  (_compileBibLatex = Module['_compileBibLatex'] = wasmExports['la'])())
var _setMainEntry = (Module['_setMainEntry'] = a0 =>
  (_setMainEntry = Module['_setMainEntry'] = wasmExports['ma'])(a0))
var _main = (Module['_main'] = (a0, a1) =>
  (_main = Module['_main'] = wasmExports['na'])(a0, a1))
var _malloc = (Module['_malloc'] = a0 =>
  (_malloc = Module['_malloc'] = wasmExports['pa'])(a0))
var _setThrew = (a0, a1) => (_setThrew = wasmExports['qa'])(a0, a1)
var __emscripten_tempret_set = a0 =>
  (__emscripten_tempret_set = wasmExports['ra'])(a0)
var __emscripten_stack_restore = a0 =>
  (__emscripten_stack_restore = wasmExports['sa'])(a0)
var __emscripten_stack_alloc = a0 =>
  (__emscripten_stack_alloc = wasmExports['ta'])(a0)
var _emscripten_stack_get_current = () =>
  (_emscripten_stack_get_current = wasmExports['ua'])()
var ___cxa_decrement_exception_refcount = a0 =>
  (___cxa_decrement_exception_refcount = wasmExports['va'])(a0)
var ___cxa_increment_exception_refcount = a0 =>
  (___cxa_increment_exception_refcount = wasmExports['wa'])(a0)
var ___cxa_can_catch = (a0, a1, a2) =>
  (___cxa_can_catch = wasmExports['xa'])(a0, a1, a2)
var ___cxa_get_exception_ptr = a0 =>
  (___cxa_get_exception_ptr = wasmExports['ya'])(a0)
function invoke_ii(index, a1) {
  var sp = stackSave()
  try {
    return getWasmTableEntry(index)(a1)
  } catch (e) {
    stackRestore(sp)
    if (e !== e + 0) throw e
    _setThrew(1, 0)
  }
}
function invoke_v(index) {
  var sp = stackSave()
  try {
    getWasmTableEntry(index)()
  } catch (e) {
    stackRestore(sp)
    if (e !== e + 0) throw e
    _setThrew(1, 0)
  }
}
function invoke_iii(index, a1, a2) {
  var sp = stackSave()
  try {
    return getWasmTableEntry(index)(a1, a2)
  } catch (e) {
    stackRestore(sp)
    if (e !== e + 0) throw e
    _setThrew(1, 0)
  }
}
function invoke_iiiiii(index, a1, a2, a3, a4, a5) {
  var sp = stackSave()
  try {
    return getWasmTableEntry(index)(a1, a2, a3, a4, a5)
  } catch (e) {
    stackRestore(sp)
    if (e !== e + 0) throw e
    _setThrew(1, 0)
  }
}
function invoke_iiiii(index, a1, a2, a3, a4) {
  var sp = stackSave()
  try {
    return getWasmTableEntry(index)(a1, a2, a3, a4)
  } catch (e) {
    stackRestore(sp)
    if (e !== e + 0) throw e
    _setThrew(1, 0)
  }
}
function invoke_vi(index, a1) {
  var sp = stackSave()
  try {
    getWasmTableEntry(index)(a1)
  } catch (e) {
    stackRestore(sp)
    if (e !== e + 0) throw e
    _setThrew(1, 0)
  }
}
function invoke_vii(index, a1, a2) {
  var sp = stackSave()
  try {
    getWasmTableEntry(index)(a1, a2)
  } catch (e) {
    stackRestore(sp)
    if (e !== e + 0) throw e
    _setThrew(1, 0)
  }
}
function invoke_iiii(index, a1, a2, a3) {
  var sp = stackSave()
  try {
    return getWasmTableEntry(index)(a1, a2, a3)
  } catch (e) {
    stackRestore(sp)
    if (e !== e + 0) throw e
    _setThrew(1, 0)
  }
}
function invoke_viii(index, a1, a2, a3) {
  var sp = stackSave()
  try {
    getWasmTableEntry(index)(a1, a2, a3)
  } catch (e) {
    stackRestore(sp)
    if (e !== e + 0) throw e
    _setThrew(1, 0)
  }
}
function invoke_viiii(index, a1, a2, a3, a4) {
  var sp = stackSave()
  try {
    getWasmTableEntry(index)(a1, a2, a3, a4)
  } catch (e) {
    stackRestore(sp)
    if (e !== e + 0) throw e
    _setThrew(1, 0)
  }
}
function invoke_iiiiiii(index, a1, a2, a3, a4, a5, a6) {
  var sp = stackSave()
  try {
    return getWasmTableEntry(index)(a1, a2, a3, a4, a5, a6)
  } catch (e) {
    stackRestore(sp)
    if (e !== e + 0) throw e
    _setThrew(1, 0)
  }
}
function invoke_viiiiii(index, a1, a2, a3, a4, a5, a6) {
  var sp = stackSave()
  try {
    getWasmTableEntry(index)(a1, a2, a3, a4, a5, a6)
  } catch (e) {
    stackRestore(sp)
    if (e !== e + 0) throw e
    _setThrew(1, 0)
  }
}
function invoke_i(index) {
  var sp = stackSave()
  try {
    return getWasmTableEntry(index)()
  } catch (e) {
    stackRestore(sp)
    if (e !== e + 0) throw e
    _setThrew(1, 0)
  }
}
function invoke_viiiii(index, a1, a2, a3, a4, a5) {
  var sp = stackSave()
  try {
    getWasmTableEntry(index)(a1, a2, a3, a4, a5)
  } catch (e) {
    stackRestore(sp)
    if (e !== e + 0) throw e
    _setThrew(1, 0)
  }
}
function invoke_iiiiiiii(index, a1, a2, a3, a4, a5, a6, a7) {
  var sp = stackSave()
  try {
    return getWasmTableEntry(index)(a1, a2, a3, a4, a5, a6, a7)
  } catch (e) {
    stackRestore(sp)
    if (e !== e + 0) throw e
    _setThrew(1, 0)
  }
}
function invoke_jiiii(index, a1, a2, a3, a4) {
  var sp = stackSave()
  try {
    return getWasmTableEntry(index)(a1, a2, a3, a4)
  } catch (e) {
    stackRestore(sp)
    if (e !== e + 0) throw e
    _setThrew(1, 0)
    return 0n
  }
}
function invoke_fiii(index, a1, a2, a3) {
  var sp = stackSave()
  try {
    return getWasmTableEntry(index)(a1, a2, a3)
  } catch (e) {
    stackRestore(sp)
    if (e !== e + 0) throw e
    _setThrew(1, 0)
  }
}
function invoke_diii(index, a1, a2, a3) {
  var sp = stackSave()
  try {
    return getWasmTableEntry(index)(a1, a2, a3)
  } catch (e) {
    stackRestore(sp)
    if (e !== e + 0) throw e
    _setThrew(1, 0)
  }
}
function invoke_viiiiiii(index, a1, a2, a3, a4, a5, a6, a7) {
  var sp = stackSave()
  try {
    getWasmTableEntry(index)(a1, a2, a3, a4, a5, a6, a7)
  } catch (e) {
    stackRestore(sp)
    if (e !== e + 0) throw e
    _setThrew(1, 0)
  }
}
function invoke_iiiiiiiiiiii(
  index,
  a1,
  a2,
  a3,
  a4,
  a5,
  a6,
  a7,
  a8,
  a9,
  a10,
  a11,
) {
  var sp = stackSave()
  try {
    return getWasmTableEntry(index)(
      a1,
      a2,
      a3,
      a4,
      a5,
      a6,
      a7,
      a8,
      a9,
      a10,
      a11,
    )
  } catch (e) {
    stackRestore(sp)
    if (e !== e + 0) throw e
    _setThrew(1, 0)
  }
}
function invoke_viiiiiiiiii(index, a1, a2, a3, a4, a5, a6, a7, a8, a9, a10) {
  var sp = stackSave()
  try {
    getWasmTableEntry(index)(a1, a2, a3, a4, a5, a6, a7, a8, a9, a10)
  } catch (e) {
    stackRestore(sp)
    if (e !== e + 0) throw e
    _setThrew(1, 0)
  }
}
function invoke_viiiiiiiiiiiiiii(
  index,
  a1,
  a2,
  a3,
  a4,
  a5,
  a6,
  a7,
  a8,
  a9,
  a10,
  a11,
  a12,
  a13,
  a14,
  a15,
) {
  var sp = stackSave()
  try {
    getWasmTableEntry(index)(
      a1,
      a2,
      a3,
      a4,
      a5,
      a6,
      a7,
      a8,
      a9,
      a10,
      a11,
      a12,
      a13,
      a14,
      a15,
    )
  } catch (e) {
    stackRestore(sp)
    if (e !== e + 0) throw e
    _setThrew(1, 0)
  }
}
Module['cwrap'] = cwrap
function callMain(args = []) {
  var entryFunction = _main
  args.unshift(thisProgram)
  var argc = args.length
  var argv = stackAlloc((argc + 1) * 4)
  var argv_ptr = argv
  args.forEach(arg => {
    HEAPU32[argv_ptr >> 2] = stringToUTF8OnStack(arg)
    argv_ptr += 4
  })
  HEAPU32[argv_ptr >> 2] = 0
  try {
    var ret = entryFunction(argc, argv)
    exitJS(ret, true)
    return ret
  } catch (e) {
    return handleException(e)
  }
}
function run(args = arguments_) {
  if (runDependencies > 0) {
    dependenciesFulfilled = run
    return
  }
  preRun()
  if (runDependencies > 0) {
    dependenciesFulfilled = run
    return
  }
  function doRun() {
    Module['calledRun'] = true
    if (ABORT) return
    initRuntime()
    preMain()
    Module['onRuntimeInitialized']?.()
    var noInitialRun = Module['noInitialRun']
    if (!noInitialRun) callMain(args)
    postRun()
  }
  if (Module['setStatus']) {
    Module['setStatus']('Running...')
    setTimeout(() => {
      setTimeout(() => Module['setStatus'](''), 1)
      doRun()
    }, 1)
  } else {
    doRun()
  }
}
if (Module['preInit']) {
  if (typeof Module['preInit'] == 'function')
    Module['preInit'] = [Module['preInit']]
  while (Module['preInit'].length > 0) {
    Module['preInit'].pop()()
  }
}
run()
