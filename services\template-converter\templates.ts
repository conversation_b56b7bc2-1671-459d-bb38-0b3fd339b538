import api from '../api'
import {
  Template,
  TemplateListItem,
  UploadTemplateResponse,
  UploadTemplateRequest,
} from './types'

/**
 * 上传模板
 */
export const uploadTemplate = async (
  data: UploadTemplateRequest,
): Promise<UploadTemplateResponse> => {
  const formData = new FormData()

  // 文件字段
  formData.append('template', data.template)

  // 元数据字段 - 与后端期望的字段名匹配
  formData.append('name', data.name)
  formData.append('description', data.description)
  formData.append('category', data.category)
  formData.append('version', data.version)
  formData.append('author', data.author)
  formData.append('license', data.license)
  formData.append('tags', data.tags.join(',')) // 转换为逗号分隔的字符串
  formData.append('isPublic', data.isPublic.toString())

  const response = await api.post<UploadTemplateResponse>(
    '/templates',
    formData,
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    },
  )

  return response.data
}

/**
 * 获取模板列表
 */
export const getTemplates = async (): Promise<{
  templates: TemplateListItem[]
}> => {
  const response = await api.get<{ templates: TemplateListItem[] }>(
    '/templates',
  )
  return response.data
}

/**
 * 获取模板详情
 */
export const getTemplate = async (
  id: string,
): Promise<{ template: Template }> => {
  const response = await api.get<{ template: Template }>(`/templates/${id}`)
  return response.data
}

/**
 * 删除模板（如果后端支持）
 */
export const deleteTemplate = async (
  id: string,
): Promise<{ message: string }> => {
  const response = await api.delete<{ message: string }>(`/templates/${id}`)
  return response.data
}
