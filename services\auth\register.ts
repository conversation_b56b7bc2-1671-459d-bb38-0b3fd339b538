import { z } from 'zod'
import { AuthResponse } from './type'
import api from '../api'

export const registerSchema = z
  .object({
    name: z
      .string()
      .min(1, 'Username is required')
      .max(100, 'Username cannot exceed 100 characters')
      .regex(
        /^(?!.*[-_.]{2})([a-zA-Z0-9]+[-_.])*[a-zA-Z0-9]+$/,
        'Username can only contain alphanumeric characters, dash (-), underscore (_) and dot (.). It cannot begin or end with non-alphanumeric characters, and consecutive non-alphanumeric chars are forbidden.',
      ),
    email: z
      .string()
      .min(1, 'Email is required')
      .email('Invalid email address'),
    password: z
      .string()
      .min(8, 'Password must be at least 8 characters')
      .max(32, 'Password cannot exceed 32 characters'),
    confirmPassword: z.string().min(1, 'Please confirm your password'),
  })
  .refine(data => data.password === data.confirmPassword, {
    message: 'Passwords do not match',
    path: ['confirmPassword'],
  })

export type RegisterFormValues = z.infer<typeof registerSchema>

export const registerUser = async (
  data: RegisterFormValues,
): Promise<AuthResponse> => {
  const formattedData = {
    name: data.name,
    email: data.email,
    password: data.password,
    password_confirm: data.confirmPassword,
  }
  const response = await api.post<AuthResponse>('/auth/register', formattedData)
  return response.data
}
