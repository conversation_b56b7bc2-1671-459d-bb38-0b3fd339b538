import { applyUpdate, Doc, encodeStateAsUpdate } from 'yjs'
import { revertToState } from './utils'
import { toast } from 'sonner'

export function createReadonlyDoc(parentDoc: Doc) {
  const update = encodeStateAsUpdate(parentDoc)
  const doc = new Doc()
  applyUpdate(doc, update)
  doc.clientID = parentDoc.clientID
  const onUpdate = (update: Uint8Array) => {
    applyUpdate(doc, update, createReadonlyDoc)
  }

  parentDoc.on('update', onUpdate)

  let debounceTimeout: NodeJS.Timeout | null = null
  doc.on('update', (_state, origin) => {
    if (origin !== createReadonlyDoc) {
      const revert = () => {
        const targetState = encodeStateAsUpdate(parentDoc)
        revertToState(doc, targetState, createReadonlyDoc)
        toast.error('You are in read-only mode. Changes will not take effect.')
      }

      if (debounceTimeout !== null) {
        clearTimeout(debounceTimeout)
      }
      debounceTimeout = setTimeout(revert, 1000)
    }
  })

  doc.on('destroy', () => {
    parentDoc.off('update', onUpdate)
    if (debounceTimeout !== null) {
      clearTimeout(debounceTimeout)
    }
  })

  return doc
}
