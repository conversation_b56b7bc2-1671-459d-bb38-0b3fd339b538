import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'
import LeftPanel from '../left-panel'

// Mock the template converter services
jest.mock('@/services/template-converter', () => ({
  getTemplates: jest.fn(),
  createConversion: jest.fn(),
  getConversionStatus: jest.fn(),
  downloadConversionResult: jest.fn(),
  uploadTemplate: jest.fn(),
}))

// Mock JSZip
jest.mock('jszip', () => {
  return jest.fn().mockImplementation(() => ({
    loadAsync: jest.fn().mockResolvedValue({
      files: {
        'main.tex': {
          dir: false,
          async: jest.fn().mockResolvedValue(new ArrayBuffer(0))
        }
      }
    })
  }))
})

describe('LeftPanel', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders the main components', () => {
    render(<LeftPanel />)
    
    expect(screen.getByText('Choose Template')).toBeInTheDocument()
    expect(screen.getByText('Upload LaTeX ZIP Package')).toBeInTheDocument()
    expect(screen.getByText('Convert & Preview')).toBeInTheDocument()
  })

  it('shows convert button initially', () => {
    render(<LeftPanel />)
    
    const convertButton = screen.getByText('Convert & Preview')
    expect(convertButton).toBeInTheDocument()
    expect(convertButton).toBeDisabled() // Should be disabled without template and file
  })

  it('does not show download button initially', () => {
    render(<LeftPanel />)
    
    expect(screen.queryByText('Download Result')).not.toBeInTheDocument()
  })

  it('shows conversion status when conversion is in progress', async () => {
    const { getTemplates, createConversion } = require('@/services/template-converter')
    
    // Mock templates
    getTemplates.mockResolvedValue({
      templates: [
        {
          id: 'template-1',
          name: 'Test Template',
          description: 'A test template'
        }
      ]
    })

    // Mock conversion creation
    createConversion.mockResolvedValue({
      conversion: {
        id: 'conversion-1',
        status: 'pending',
        taskName: 'Convert to Test Template'
      }
    })

    render(<LeftPanel />)

    // Wait for templates to load
    await waitFor(() => {
      expect(screen.getByText('Choose Template')).toBeInTheDocument()
    })

    // The component should show the conversion status area when a conversion is started
    // This would require more complex mocking of the polling mechanism
  })
})
