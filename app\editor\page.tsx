'use client'

import { Suspense, useEffect, useState } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import useRequireLogin from '@/lib/use-require-login'
import PageTopBar from './_components/page-top-bar'
import EditorLayout from './_components/editor-layout'
import Explorer from './_components/explorer'
import TextEditor from './_components/text-editor'
import Output from './_components/output'
import { YProject } from '@/lib/y-project'
import { YProjectContext } from '@/lib/y-adapter/use-yproject-context'
import FullPageLoading from '@/components/full-page-loading'
import { resetExplorerStore } from '@/stores/explorer-store'
import { useGetProjectAccess } from '@/services/projects/get-project-access'
import { toast } from 'sonner'
import { useGetMe } from '@/services/auth/get-me'

function Page() {
  useRequireLogin()
  const searchParams = useSearchParams()
  const router = useRouter()
  const projectId = searchParams.get('project-id')
  const [yproject, setYProject] = useState<YProject>()
  const [yprojectView, setYProjectView] = useState<YProject>()
  const [version, setVersion] = useState<string>()
  const { data: projectAccess, isLoading: isProjectAccessLoading } =
    useGetProjectAccess(projectId!)
  const { data: user, isLoading: isUserLoading } = useGetMe()
  const isLoading = isProjectAccessLoading || isUserLoading

  useEffect(() => {
    if (isLoading) return
    if (!projectId || !projectAccess || !user) {
      toast.error('Project not found or access denied')
      router.push('/projects')
    } else {
      const yproject = new YProject({
        projectId,
        readonly: projectAccess.readOnly,
        user,
      })
      setYProject(yproject)
      return () => {
        yproject?.destroy()
      }
    }
  }, [
    isLoading,
    isProjectAccessLoading,
    projectAccess,
    projectId,
    router,
    user,
  ])

  useEffect(() => {
    yproject?.versionHandler.switchVersion(version).then(version => {
      setYProjectView(version)
    })
  }, [version, yproject])

  useEffect(() => {
    return resetExplorerStore
  }, [])

  if (!projectId || !yprojectView) {
    return <FullPageLoading message="Loading YProject..." />
  }

  return (
    <YProjectContext.Provider
      value={{
        yproject: yprojectView,
        version,
        setVersion,
      }}
    >
      <div className="flex flex-col h-screen">
        <PageTopBar />
        <EditorLayout
          left={<Explorer />}
          center={<TextEditor />}
          right={<Output />}
        />
      </div>
    </YProjectContext.Provider>
  )
}

export default function SuspensePage() {
  return (
    <Suspense>
      <Page />
    </Suspense>
  )
}
