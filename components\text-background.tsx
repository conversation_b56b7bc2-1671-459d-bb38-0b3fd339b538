import React, { useEffect, useRef } from 'react'
import { cn } from '@/lib/utils'

interface TextBackgroundProps {
  text: string
  className?: string
  backgroundClassName?: string
  children: React.ReactNode
}

const TextBackground: React.FC<TextBackgroundProps> = ({
  text,
  className = '',
  backgroundClassName = '',
  children,
}) => {
  const textRef = useRef<HTMLDivElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const resizeText = () => {
      if (textRef.current && containerRef.current) {
        const container = containerRef.current
        const textElement = textRef.current

        // Reset transform to get natural width
        textElement.style.transform = 'scale(1)'

        const containerWidth = container.offsetWidth
        const textWidth = textElement.scrollWidth

        if (textWidth > 0) {
          const scale = containerWidth / textWidth
          textElement.style.transform = `scale(${scale})`
        }
      }
    }

    resizeText()

    // Add resize observer for responsive behavior
    const resizeObserver = new ResizeObserver(resizeText)
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current)
    }

    return () => {
      resizeObserver.disconnect()
    }
  }, [text])

  return (
    <div ref={containerRef} className={cn('relative', className)}>
      <div className="absolute inset-0 flex items-center justify-center overflow-hidden pointer-events-none select-none -translate-y-1/8">
        <div
          ref={textRef}
          className={cn(
            'text-gray-100 font-bold whitespace-nowrap',
            backgroundClassName,
          )}
          style={{
            fontSize: '8rem',
            transformOrigin: 'center',
            transition: 'transform 0.2s ease-out',
          }}
        >
          {text}
        </div>
      </div>
      <div className="relative z-10">{children}</div>
    </div>
  )
}

export default TextBackground
