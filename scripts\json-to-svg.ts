import * as fs from 'fs'
import * as path from 'path'

// Define interfaces for the JSON structure
interface Attributes {
  [key: string]: string
}

interface Node {
  type: string
  name: string
  attributes?: Attributes
  children?: Node[]
  isRootNode?: boolean
}

interface IconData {
  icon: Node
  name: string
}

// Function to convert attributes object to string
function attributesToString(attrs: Attributes | undefined): string {
  if (!attrs) return ''
  return Object.entries(attrs)
    .map(([key, value]) => `${key}="${value}"`)
    .join(' ')
}

// Function to convert node to SVG string
function nodeToSvg(node: Node, indent: number = 0): string {
  const indentation = ' '.repeat(indent * 2)
  const attrsString = attributesToString(node.attributes)
  const openingTag = `${indentation}<${node.name}${attrsString ? ' ' + attrsString : ''}>`

  if (!node.children || node.children.length === 0) {
    return `${indentation}<${node.name}${attrsString ? ' ' + attrsString : ''} />`
  }

  const childrenContent = node.children
    .map(child => nodeToSvg(child, indent + 1))
    .join('\n')

  return `${openingTag}\n${childrenContent}\n${indentation}</${node.name}>`
}

// Function to convert JSON to SVG
function convertToSvg(data: IconData): string {
  return `<?xml version="1.0" encoding="UTF-8"?>\n${nodeToSvg(data.icon)}`
}

// Function to convert camelCase to kebab-case
function toKebabCase(str: string): string {
  return str.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase()
}

// Function to process all JSON files in directory
function processDirectory(inputDir: string, outputDir: string): void {
  try {
    const files = fs.readdirSync(inputDir)

    // Ensure output directory exists
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true })
    }

    files.forEach(file => {
      const fullInputPath = path.join(inputDir, file)
      const stat = fs.statSync(fullInputPath)

      if (stat.isDirectory()) {
        // Recursively process subdirectories
        const newOutputDir = path.join(outputDir, file)
        processDirectory(fullInputPath, newOutputDir)
      } else if (file.endsWith('.json')) {
        try {
          // Read and parse JSON file
          const jsonContent = fs.readFileSync(fullInputPath, 'utf8')
          const iconData: IconData = JSON.parse(jsonContent)

          // Generate SVG content
          const svgContent = convertToSvg(iconData)

          // Convert name to kebab-case and create output path
          const kebabName = toKebabCase(iconData.name)
          const outputPath = path.join(outputDir, `${kebabName}.svg`)

          // Write SVG file
          fs.writeFileSync(outputPath, svgContent, 'utf8')
          console.log(`Converted: ${fullInputPath} -> ${outputPath}`)
        } catch (error) {
          console.error(`Error processing ${fullInputPath}:`, error)
        }
      }
    })
  } catch (error) {
    console.error(`Error reading directory ${inputDir}:`, error)
  }
}

// Main execution
const sourceDir = path.resolve('./json') // Get it from newton
const outputDir = path.resolve('./svg')

if (!fs.existsSync(sourceDir)) {
  console.error(`Source directory ${sourceDir} does not exist`)
  process.exit(1)
}

console.log(
  `Starting conversion of JSON files from ${sourceDir} to ${outputDir}...`,
)
processDirectory(sourceDir, outputDir)
console.log('Conversion complete!')
