// components/navigation/UserMenu.tsx
'use client'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { toast } from 'sonner'
import { cn } from '@/lib/utils'
import { useRouter } from 'next/navigation'
import { useGetMe } from '@/services/auth/get-me'
import { logoutUser } from '@/services/auth/logout'
import { User } from 'lucide-react'

interface UserMenuProps {
  variant: 'desktop' | 'mobile'
}

export const UserMenu: React.FC<UserMenuProps> = ({ variant }) => {
  const { data: user } = useGetMe()

  const isDesktop = variant === 'desktop'
  const router = useRouter()

  const handleLogout = async () => {
    try {
      await logoutUser()
      router.push('/home')
    } catch (error: unknown) {
      console.error('Failed to log out', error)
      toast.error('Error logging out')
    }
  }

  if (user?.id) {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            className="w-10 h-10 rounded-full bg-gray-100 hover:bg-gray-200 text-gray-600"
          >
            <User className="w-5 h-5" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem disabled>{user?.email}</DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem>Account Settings</DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={handleLogout}>Log Out</DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    )
  }

  return (
    <div
      className={cn(
        isDesktop ? 'flex space-x-4' : 'flex flex-col space-y-4 mt-4',
      )}
    >
      <Button asChild variant="outline">
        <Link href="/login">Login</Link>
      </Button>
      <Button asChild>
        <Link href="/register">Sign up</Link>
      </Button>
    </div>
  )
}
