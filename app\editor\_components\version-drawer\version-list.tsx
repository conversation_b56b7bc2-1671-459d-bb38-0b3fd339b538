import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { useYProjectContext } from '@/lib/y-adapter/use-yproject-context'
import {
  Clock,
  Check,
  RotateCcw,
  FileText,
  Calendar,
  History,
  Trash2,
} from 'lucide-react'
import useYVersions from '@/lib/y-adapter/use-yversion'
import { confirmDialog } from '@/components/global-dialog'

export function VersionList() {
  const { yproject, version: currentVersion, setVersion } = useYProjectContext()
  const versions = useYVersions(yproject)

  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp)
    const now = new Date()

    // Reset both dates to midnight for accurate day comparison
    const dateAtMidnight = new Date(
      date.getFullYear(),
      date.getMonth(),
      date.getDate(),
    )
    const nowAtMidnight = new Date(
      now.getFullYear(),
      now.getMonth(),
      now.getDate(),
    )

    const diffTime = nowAtMidnight.getTime() - dateAtMidnight.getTime()
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))

    if (diffDays === 0) {
      return `Today at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`
    } else if (diffDays === 1) {
      return `Yesterday at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`
    } else if (diffDays < 7) {
      return `${diffDays} days ago`
    } else {
      return date.toLocaleDateString([], {
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      })
    }
  }

  const isCurrentVersion = (versionId: string) => {
    return currentVersion === versionId
  }

  const isLatest = currentVersion === undefined

  return (
    <ScrollArea className="h-0 flex-1 px-4">
      <div className="space-y-3 py-4">
        {/* Latest / Live Version */}
        <div
          className={`p-4 rounded-lg border-2 transition-colors ${
            isLatest
              ? 'border-primary bg-primary/5'
              : 'border-border bg-card hover:bg-accent/50'
          }`}
        >
          <div className="flex justify-between items-start gap-4">
            <div className="flex-1 space-y-1">
              <div className="flex items-center gap-2">
                <FileText className="h-4 w-4 text-muted-foreground" />
                <span className="font-medium">Latest Version</span>
                {isLatest && (
                  <Badge variant="default" className="text-xs">
                    <Check className="h-3 w-3 mr-1" />
                    Current
                  </Badge>
                )}
              </div>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Clock className="h-3 w-3" />
                <span>Live editing</span>
              </div>
            </div>
            {!isLatest && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setVersion(undefined)}
              >
                <RotateCcw className="h-3 w-3 mr-1" />
                Switch
              </Button>
            )}
          </div>
        </div>

        {/* Saved Versions */}
        {versions.map((version, index) => {
          const isCurrent = isCurrentVersion(version.id)
          const contributors = version.contributors
          return (
            <div
              key={version.id}
              className={`p-4 rounded-lg border-2 transition-colors ${
                isCurrent
                  ? 'border-primary bg-primary/5'
                  : 'border-border bg-card hover:bg-accent/50'
              }`}
            >
              <div className="flex justify-between items-start gap-4">
                <div className="flex-1 space-y-1">
                  <div className="flex items-center gap-2">
                    <FileText className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">
                      Version {versions.length - index}
                    </span>
                    {isCurrent && (
                      <Badge variant="default" className="text-xs">
                        <Check className="h-3 w-3 mr-1" />
                        Current
                      </Badge>
                    )}
                  </div>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Calendar className="h-3 w-3" />
                    <span>{formatDate(version.date)}</span>
                  </div>
                  {contributors && Object.keys(contributors).length > 0 && (
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <span>
                        Contributors:{' '}
                        {Object.values(contributors)
                          .map(c => c.name)
                          .join(', ')}
                      </span>
                    </div>
                  )}
                </div>

                {!isCurrent && (
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setVersion(version.id)}
                    >
                      <RotateCcw className="h-3 w-3 mr-1" />
                      Switch
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={async () => {
                        const confirm = await confirmDialog()
                        if (!confirm) return
                        yproject.versionHandler.deleteVersion(version.id)
                      }}
                      className="text-destructive hover:text-destructive"
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                )}
              </div>
            </div>
          )
        })}

        {/* Empty State */}
        {versions.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <History className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p>No saved versions yet</p>
            <p className="text-sm">Create your first version to get started</p>
          </div>
        )}
      </div>
    </ScrollArea>
  )
}
