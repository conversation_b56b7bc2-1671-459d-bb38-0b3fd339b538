import React from 'react'
import Link from 'next/link'
import Image from 'next/image'
import <PERSON><PERSON><PERSON> from '@/public/assets/website/RightArrow.svg'
import Spline from '@splinetool/react-spline'

const Intro = () => {
  return (
    <div className="hero-section relative w-full h-screen flex flex-col items-center justify-center text-center px-4">
      {/* Spline 3D Background */}
      <div
        className="absolute inset-0 -z-10 scale-100 md:scale-150 lg:scale-[250%] mt-80 overflow-hidden"
        style={{
          clipPath: 'inset(0 0 30% 30%)',
          pointerEvents: 'none',
        }}
      >
        <Spline scene="https://prod.spline.design/OlDgU0-g3zMqpQJT/scene.splinecode" />
      </div>

      {/* Main Heading */}
      <h1 className="text-transparent bg-gradient-to-r from-[#33B4FF] to-[#FFAE00] bg-clip-text text-4xl md:text-5xl lg:text-[64px] font-bold z-10 font-['Public_Sans'] leading-tight pb-1">
        Shape the Future of Scholarly Publishing
      </h1>

      {/* Subtext */}
      <p className="text-lg md:text-xl text-gray-600 z-10 mt-8 sm:mt-12 md:mt-16 lg:mt-[80px]">
        We are Transforming How Knowledge is Shared Globally.
      </p>

      {/* Buttons */}
      <div className="flex space-x-4 z-10 mt-8 sm:mt-12 md:mt-16 lg:mt-[80px]">
        <Link
          href="/projects"
          className="go-to-editor flex items-center px-6 py-3 bg-white bg-opacity-55 border-2 border-[#D4D4D4] text-black font-sans rounded-lg shadow-md hover:bg-[#33b4ff] hover:bg-opacity-20 hover:border-[#33b4ff] hover:text-[#33b4ff] transition-all duration-300"
        >
          Go to Editor
          <Image
            src={RightArrow}
            alt="Arrow"
            className="ml-2 w-5 h-5"
            width={20}
            height={20}
          />
        </Link>
      </div>
    </div>
  )
}

export default Intro
