.mainContent {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px;
  font-family:
    Public Sans,
    sans-serif;
}
.title {
  background: linear-gradient(90deg, #33b4ff 0%, #ffae00 100%);
  -webkit-text-fill-color: transparent;
  -webkit-background-clip: text;
  margin-bottom: 40px;
  font-size: 64px;
  font-weight: 700;
}
@media (width<=991px) {
  .title {
    font-size: 48px;
  }
}
@media (width<=640px) {
  .title {
    font-size: 32px;
  }
}
.jobsList {
  flex-direction: column;
  gap: 20px;
  display: flex;
}
.jobCard {
  background-color: #fafafade;
  border: 1px solid #eaeaea;
  border-radius: 36px;
  align-items: center;
  padding: 20px;
  display: flex;
}

.jobCard {
  width: 100%;
  height: auto;
  background-color: #fafafa;
  font-family:
    Public Sans,
    sans-serif;
  border-radius: 16px;
  box-shadow: 0px 4px 16px rgba(0, 0, 0, 0.1);
  gap: 20px;
}

.JobInfo {
  display: flex;
  flex-direction: column;
}

.jobTitle {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 10px;
}

.jobCompany {
  font-size: 18px;
  font-weight: 500;
  color: #808080;
  margin-top: -10px;
}

.jobLocation {
  margin-top: 2rem;
  font-size: 22px;
  font-weight: 500;
  color: #808080;
}

@media (width<=768px) {
  .jobTitle {
    font-size: 20px;
  }
  .jobsList {
    grid-template-columns: 1fr;
  }
}

.jobLogo {
  width: 160px;
  height: 160px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}

@media (width<=640px) {
  .jobLogo {
    width: 100px;
    height: 100px;
  }
}
