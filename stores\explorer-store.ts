import { create } from 'zustand'
import { FileNode } from '../lib/y-project/file-index'
import { produce, WritableDraft } from 'immer'
import { Key } from 'react'

interface ExplorerStore {
  selectedNode?: FileNode
  selectedPath?: string
  rightClickedPath?: string
  addingPath?: [string, 'file' | 'folder'] // [parentPath, type]
  renamingPath?: string
  expandedKeys: Key[] // Add expandedKeys for rc-tree
}

export const useExplorerStore = create<ExplorerStore>(() => ({
  expandedKeys: [],
}))

export const resetExplorerStore = () => {
  useExplorerStore.setState(
    {
      expandedKeys: [],
    },
    true,
  )
}

/**
 * @deprecated Not correct implementation
 */
export function setExplorerStore(
  fn: (state: WritableDraft<ExplorerStore>) => void,
) {
  useExplorerStore.setState(state => produce(state, fn))
}

// New action to ensure all parent nodes are expanded for a given path
export function expandToPath(targetPath: string) {
  setExplorerStore(state => {
    const pathParts = targetPath.split('/').filter(Boolean)
    const keysToExpand: string[] = []

    // Build all parent paths
    for (let i = 1; i <= pathParts.length; i++) {
      const parentPath = pathParts.slice(0, i).join('/')
      keysToExpand.push(parentPath)
    }
    // Add new keys to expandedKeys, avoiding duplicates
    state.expandedKeys = Array.from(
      new Set([...state.expandedKeys, ...keysToExpand]),
    )
  })
}
