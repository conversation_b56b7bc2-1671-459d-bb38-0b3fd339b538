# Hawking

Next version of Arxtext in Next.js

## Development

### Prerequisites

- Node.js v20 (Recommended using nvm to manage node versions)

### Environment

Copy and rename .env.example to .env

### Start the Hawking project

```bash
npm install
npm run dev

```

## Self-hosted Y-Server (Not required for front-end development only)

### Start local y-server

```bash
cd y-server
npm i
npm run start
```

## Quick Reference

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Yjs Documentation](https://docs.yjs.dev/) - learn about Yjs features and API.
- [shadcn/ui](https://ui.shadcn.com/) - UI components library.
- [tailwindcss](https://tailwindcss.com/docs) - CSS utility library.
- [Zustand](https://zustand.docs.pmnd.rs/) - State management library.

## Publish

```bash
npm run build
npm publish
```

## Docker Build

```bash
npm run build
docker buildx build \
  --platform linux/amd64,linux/arm64 \
  -t tommyjqliu/arxtect-hawking:0.1.0 \
  -t tommyjqliu/arxtect-hawking:latest \
  --push .

docker buildx build \
  --platform linux/amd64,linux/arm64 \
  -t tommyjqliu/arxtect-hypersheet:0.1.0 \
  -t tommyjqliu/arxtect-hypersheet:latest \
  --push .
```
