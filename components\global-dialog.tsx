'use client'
import { cloneElement, ReactElement } from 'react'
import { create } from 'zustand'
import { Dialog } from './ui/dialog'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from './ui/alert-dialog'

type RootComponent = typeof Dialog | typeof AlertDialog
type RootComponentProps = React.ComponentProps<RootComponent>
type RootComponentElement = ReactElement<RootComponentProps, RootComponent>

const useDialogStore = create<{
  open: boolean
  setOpen: (isOpen: boolean) => void
  content: RootComponentElement | null
  setContent: (content: RootComponentElement) => void
}>(set => ({
  open: false,
  setOpen: isOpen => set({ open: isOpen }),
  content: null,
  setContent: content => set({ content }),
}))

export function GlobalDialog() {
  const open = useDialogStore(state => state.open)
  const setOpen = useDialogStore(state => state.setOpen)
  const content = useDialogStore(state => state.content)

  if (!content) return null
  return cloneElement(content, {
    open,
    onOpenChange: setOpen,
  })
}

export function triggerDialog(content: RootComponentElement) {
  useDialogStore.setState({
    open: true,
    content,
  })
}

export async function confirmDialog(
  renderContent?: (
    resolve: (value: boolean | PromiseLike<boolean>) => void,
  ) => ReactElement,
) {
  return new Promise<boolean>(resolve => {
    const content = renderContent ? (
      renderContent(resolve)
    ) : (
      <AlertDialog>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => resolve(false)}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction onClick={() => resolve(true)}>
              Continue
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    )

    useDialogStore.setState({
      open: true,
      content,
    })
  })
}
