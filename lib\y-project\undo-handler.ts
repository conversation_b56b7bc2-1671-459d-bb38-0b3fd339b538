import { YProject } from '.'
import * as Y from 'yjs'

export default class UndoHandler {
  private yproject: YProject
  textManagerMap: Map<string, Y.UndoManager> = new Map()
  indexManager: Y.UndoManager | undefined = undefined

  constructor(yproject: YProject) {
    this.yproject = yproject
    this.indexManager = new Y.UndoManager(this.yproject.fileIndex.rootFolder)
  }

  getTextManager(id: string) {
    if (!this.textManagerMap.has(id)) {
      const ytext = this.yproject.fileIndex.documentMap.get(id)

      if (ytext) {
        const undoManager = new Y.UndoManager(ytext)
        this.textManagerMap.set(id, undoManager)
      }
    }
    return this.textManagerMap.get(id)
  }

  destroy() {
    this.textManagerMap.forEach(undoManager => {
      undoManager.destroy()
    })
    this.textManagerMap.clear()

    if (this.indexManager) {
      this.indexManager.destroy()
      this.indexManager = undefined
    }
  }
}
