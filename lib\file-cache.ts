import localForage from 'localforage'

export const ASSET_CACHE_NAME = 'file-cache'
export const PDF_CACHE_NAME = 'pdf-cache'

const createStore = (cacheName: string) => {
  return localForage.createInstance({
    name: cacheName,
    storeName: 'files',
    driver: [
      localForage.INDEXEDDB,
      localForage.WEBSQL,
      localForage.LOCALSTORAGE,
    ],
  })
}
interface FileMetadata {
  storedTime: number
  lastAccessTime: number
  size: number
}

interface StoredFile {
  data: ArrayBuffer
  metadata: FileMetadata
}

async function makeSpace(store: LocalForage): Promise<void> {
  // Get all items with their keys and access times
  const items: { key: string; lastAccessTime: number }[] = []
  await store.iterate((value: StoredFile, key: string) => {
    items.push({
      key,
      lastAccessTime: value.metadata.lastAccessTime,
    })
  })

  if (items.length === 0) {
    throw new Error('No files to remove and storage still full')
  }

  // Sort by last access time (oldest first)
  items.sort((a, b) => a.lastAccessTime - b.lastAccessTime)

  // Remove the oldest half
  const itemsToRemove = Math.ceil(items.length / 2)
  for (let i = 0; i < itemsToRemove; i++) {
    await store.removeItem(items[i].key)
  }
}

export async function storeFile(
  fileKey: string,
  file: ArrayBufferLike,
  cacheName = ASSET_CACHE_NAME,
): Promise<void> {
  const store = createStore(cacheName)
  const uint8Array = new Uint8Array(file)
  const now = Date.now()
  const storedFile: StoredFile = {
    data: uint8Array.buffer as ArrayBuffer,
    metadata: {
      storedTime: now,
      lastAccessTime: now,
      size: uint8Array.byteLength,
    },
  }

  let attempts = 0
  while (attempts < 3) {
    attempts += 1
    try {
      await store.setItem(fileKey, storedFile)
      break // Exit loop on success
    } catch (error) {
      if (error instanceof Error && error.name === 'QuotaExceededError') {
        await makeSpace(store)
      } else {
        throw error
      }
    }
  }
}

export async function retrieveFile(
  fileKey: string,
  cacheName: string = ASSET_CACHE_NAME,
): Promise<ArrayBuffer | null> {
  try {
    const store = createStore(cacheName)
    const storedFile: StoredFile | null = await store.getItem(fileKey)

    if (!storedFile) return null

    // Update last access time
    storedFile.metadata.lastAccessTime = Date.now()
    await store.setItem(fileKey, storedFile)

    return storedFile.data
  } catch (error) {
    throw new Error(
      `Failed to retrieve file: ${error instanceof Error ? error.message : 'Unknown error'}`,
    )
  }
}

export async function getMissingFiles(
  fileKeys: string[],
  cacheName: string = ASSET_CACHE_NAME,
): Promise<string[]> {
  const store = createStore(cacheName)
  const results = await Promise.all(
    fileKeys.map(async key => {
      const exists = await store.getItem(key)
      return !exists
    }),
  )
  return fileKeys.filter((_, index) => results[index])
}

export async function clearCache(
  cacheName: string = ASSET_CACHE_NAME,
): Promise<void> {
  try {
    const store = createStore(cacheName)
    await store.clear()
  } catch (error) {
    throw new Error(
      `Failed to clear cache: ${error instanceof Error ? error.message : 'Unknown error'}`,
    )
  }
}
