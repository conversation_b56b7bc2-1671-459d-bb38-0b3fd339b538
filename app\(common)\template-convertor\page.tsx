'use client'
import React, { useState, useRef, MouseEvent, useEffect } from 'react'
import LeftPanel from './_components/left-panel'
import RightPanel from './_components/right-panel'

const TemplateConvertor: React.FC = () => {
  const isDragging = useRef(false)
  const containerRef = useRef<HTMLDivElement>(null)
  const [collapsed] = useState(false)
  const [leftWidth, setLeftWidth] = useState<number>(960)
  const [pdfData, setPdfData] = useState<ArrayBuffer | null>(null)
  const [isCompiling, setIsCompiling] = useState(false)

  const MIN_WIDTH = 800
  const MAX_WIDTH = () =>
    containerRef.current?.clientWidth
      ? containerRef.current.clientWidth - 700
      : 800

  useEffect(() => {
    setLeftWidth(
      containerRef.current?.clientWidth
        ? containerRef.current?.clientWidth / 2
        : 1200,
    )
  }, [])

  const startResize = (e: MouseEvent) => {
    e.preventDefault()
    isDragging.current = true

    const startX = e.clientX
    const startLeftWidth = leftWidth

    document.body.style.userSelect = 'none'
    document.body.style.cursor = 'col-resize'

    const onMouseMove = (_moveEvent: Event) => {
      if (!isDragging.current) return

      const moveEvent = _moveEvent as unknown as MouseEvent
      const deltaX = moveEvent.clientX - startX
      const newLeftWidth = Math.min(
        Math.max(MIN_WIDTH, startLeftWidth + deltaX),
        MAX_WIDTH(),
      )
      setLeftWidth(newLeftWidth)
    }

    const onMouseUp = () => {
      isDragging.current = false
      document.body.style.userSelect = ''
      document.body.style.cursor = ''
      window.removeEventListener('mousemove', onMouseMove)
      window.removeEventListener('mouseup', onMouseUp)
    }

    window.addEventListener('mousemove', onMouseMove)
    window.addEventListener('mouseup', onMouseUp)
  }

  // const handleCollapseClick = (e: MouseEvent) => {
  //   e.stopPropagation()
  //   if (isDragging.current) {
  //     isDragging.current = false
  //     return
  //   }
  //   setCollapsed(c => !c)
  // }

  return (
    <div className="w-full max-h-screen">
      <div
        ref={containerRef}
        className="flex h-[calc(100vh_-_72px)] max-md:flex-col relative"
      >
        {' '}
        <div
          className="h-full pl-2.5 overflow-hidden"
          style={{
            width: collapsed
              ? containerRef.current?.clientWidth
                ? containerRef.current?.clientWidth - 4
                : '98%'
              : leftWidth,
            minWidth: collapsed
              ? containerRef.current?.clientWidth
                ? containerRef.current?.clientWidth - 4
                : '98%'
              : MIN_WIDTH,
            flexShrink: 0,
          }}
        >
          <LeftPanel
            onPdfGenerated={setPdfData}
            onCompileStatusChange={setIsCompiling}
          />
        </div>
        <div
          className="relative bg-border cursor-col-resize hover:bg-blue-500 transition-colors"
          style={{
            width: 4,
            zIndex: 10,
          }}
          onMouseDown={startResize}
        >
          <button
            className="absolute top-1/2 -translate-y-1/2 -right-3 w-6 h-12 flex items-center justify-center text-xs text-muted-foreground bg-muted hover:bg-accent border rounded-md shadow-sm transition-all duration-200 hover:shadow-md group"
            onClick={/*handleCollapseClick*/ () => {}}
            aria-label={collapsed ? '展开面板' : '折叠面板'}
          >
            <div className="flex flex-col items-center space-y-1 group-hover:space-y-1.5 transition-all">
              {collapsed ? (
                <>
                  <span className="text-sm font-medium">⟨</span>
                  <div className="flex flex-col items-center space-y-0.5">
                    <span className="w-1 h-1 rounded-full bg-current opacity-60"></span>
                    <span className="w-1 h-1 rounded-full bg-current opacity-60"></span>
                    <span className="w-1 h-1 rounded-full bg-current opacity-60"></span>
                  </div>
                </>
              ) : (
                <>
                  <span className="text-sm font-medium">⟩</span>
                  <div className="flex flex-col items-center space-y-0.5">
                    <span className="w-1 h-1 rounded-full bg-current opacity-60"></span>
                    <span className="w-1 h-1 rounded-full bg-current opacity-60"></span>
                    <span className="w-1 h-1 rounded-full bg-current opacity-60"></span>
                  </div>
                </>
              )}
            </div>
          </button>
        </div>{' '}
        {!collapsed && (
          <div className="flex-1 h-full bg-gray-300 border-l min-w-[400px]">
            <RightPanel pdfData={pdfData} isLoading={isCompiling} />
          </div>
        )}
      </div>
    </div>
  )
}

export default TemplateConvertor
