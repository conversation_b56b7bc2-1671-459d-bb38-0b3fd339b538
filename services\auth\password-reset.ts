import api from '../api'
import { z } from 'zod'

export const forgotPasswordSchema = z.object({
  email: z.string().min(1, 'Email is required').email('Invalid email address'),
})

export const resetPasswordSchema = z
  .object({
    resetToken: z.string().min(1, 'Reset token is required'),
    password: z
      .string()
      .min(1, 'Password is required')
      .min(8, 'Password must be at least 8 characters'),
    passwordConfirm: z.string().min(1, 'Please confirm your password'),
  })
  .refine(data => data.password === data.passwordConfirm, {
    message: 'Passwords do not match',
    path: ['passwordConfirm'],
  })

export type ForgotPasswordFormValues = z.infer<typeof forgotPasswordSchema>
export type ResetPasswordFormValues = z.infer<typeof resetPasswordSchema>

export interface PasswordResetResponse {
  message: string
}

export const forgotPassword = async (
  email: string,
): Promise<PasswordResetResponse> => {
  const res = await api.post<PasswordResetResponse>('/auth/forgot-password', {
    email,
  })
  return res.data
}

export const resetPassword = async (
  data: ResetPasswordFormValues & { resetToken: string },
): Promise<PasswordResetResponse> => {
  const response = await api.post<PasswordResetResponse>(
    '/auth/reset-password',
    data,
  )
  return response.data
}
