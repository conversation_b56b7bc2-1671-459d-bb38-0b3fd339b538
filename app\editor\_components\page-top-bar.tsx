import React, { useState, useRef, ChangeEvent } from 'react'
import { Textarea } from '@/components/ui/textarea'
import { Tooltip } from '@/components/ui/tooltip'
import Link from 'next/link'
import VersionDrawer from './version-drawer'
import { useYProjectContext } from '@/lib/y-adapter/use-yproject-context'
import {
  ChevronLeft,
  Download,
  Share2,
  History,
  Pencil,
  Globe,
} from 'lucide-react'
import { useGetProject } from '@/services/projects/get-project'
import updateProject from '@/services/projects/update-project'
import { toast } from 'sonner'
import { ShareLinkButton } from '@/components/project/share-project-dialog'
import exportZip from '@/lib/y-adapter/export-zip'
import { YProject } from '@/lib/y-project'
import useAwareness, { CollaborateUser } from '@/lib/y-adapter/use-awareness'
import { cn } from '@/lib/utils'
import { confirmDialog } from '@/components/global-dialog'
import { AlertDialog } from '@radix-ui/react-alert-dialog'
import {
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'

function BackToHomeSection() {
  return (
    <div className="flex items-center pl-4 space-x-4 w-1/5 text-white">
      <Link href="/projects" tag-id="back-to-home">
        <ChevronLeft className="cursor-pointer hover:opacity-50 filter brightness-0 invert" />
      </Link>
      <span className="ml-2 text-white font-bold font-arx">Back to Home</span>
    </div>
  )
}

function UserListSection({ userList }: { userList: CollaborateUser[] }) {
  return (
    <div className="flex items-center text-sm xl:block whitespace-nowrap">
      {userList.map((user, index) => (
        <span
          key={index}
          style={{ backgroundColor: user.color }}
          className="text-white font-bold rounded-full px-2 py-1 mr-2"
        >
          {user.name}
        </span>
      ))}
    </div>
  )
}

function ActionButtonsSection({ yproject }: { yproject: YProject }) {
  const { data } = useGetProject(yproject.projectId)

  const buttonBaseClasses =
    'flex items-center justify-start px-3 py-1 h-8 text-white hover:bg-white/20 transition-colors duration-200 cursor-pointer'
  const published = data?.published
  return (
    <div className="flex items-center mr-4 bg-white/10 border border-white/40 rounded-lg backdrop-blur-sm">
      {/* Publish Button */}
      <Tooltip content={published ? 'Unpublish' : 'Publish'}>
        <button
          className={cn(buttonBaseClasses, 'border-r border-white/20')}
          onClick={async () => {
            const confirm = await confirmDialog(resolve => (
              <AlertDialog>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>
                      Are you sure to {published ? 'unpublish' : 'publish'} the
                      project?
                    </AlertDialogTitle>
                    <AlertDialogDescription>
                      {published
                        ? 'This will make the project private and remove it from public view.'
                        : 'This will make the project public and visible to everyone. The project will also become read-only.'}
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel onClick={() => resolve(false)}>
                      Cancel
                    </AlertDialogCancel>
                    <AlertDialogAction onClick={() => resolve(true)}>
                      Continue
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            ))
            if (!confirm) return
            await updateProject(yproject.projectId, {
              published: !published,
            })
            toast.success(
              `Project ${!published ? 'published' : 'unpublished'} successfully`,
            )
            // page refresh
            window.location.reload()
          }}
          tag-id={published ? 'unpublish-project' : 'publish-project'}
        >
          <Globe className="w-4 h-4" />
          <span className="ml-2 hidden md:block whitespace-nowrap">
            {published ? 'Unpublish' : 'Publish'}
          </span>
        </button>
      </Tooltip>

      {/* History Button */}
      <Tooltip content="History">
        <VersionDrawer
          trigger={
            <div
              className={cn(buttonBaseClasses, 'border-r border-white/20')}
              tag-id="view-history"
            >
              <History className="w-4 h-4" />
              <span className="ml-2 hidden md:block whitespace-nowrap">
                History
              </span>
            </div>
          }
        />
      </Tooltip>

      {/* Share Button */}
      <Tooltip content="Share">
        <ShareLinkButton projectId={yproject.projectId}>
          <div
            className={cn(buttonBaseClasses, 'border-r border-white/20')}
            tag-id="share-project"
          >
            <Share2 className="w-4 h-4" />
            <span className="ml-2 hidden md:block whitespace-nowrap">
              Share
            </span>
          </div>
        </ShareLinkButton>
      </Tooltip>

      {/* Download Button */}
      <Tooltip content="Download">
        <button
          className={cn(buttonBaseClasses, 'rounded-r-lg')}
          onClick={() =>
            data && exportZip(yproject.projectId, data.projectName)
          }
          tag-id="download-project"
        >
          <Download className="w-4 h-4" />
          <span className="ml-2 hidden md:block whitespace-nowrap">
            Download
          </span>
        </button>
      </Tooltip>
    </div>
  )
}

// Main Component
export default function PageTopBar() {
  // Hooks
  const { yproject } = useYProjectContext()
  const { data, refetch } = useGetProject(yproject.projectId)

  // State
  const [isEditing, setIsEditing] = useState<boolean>(false)
  const [value, setValue] = useState<string>('')
  const userList = useAwareness(yproject)
  const uniqueUserList = userList.filter(
    (user, index, self) => index === self.findIndex(t => t.id === user.id),
  )
  // Refs
  const inputRef = useRef<HTMLTextAreaElement>(null)

  // Event Handlers
  const handleChange = (event: ChangeEvent<HTMLTextAreaElement>) => {
    setValue(event.target.value)
  }

  const handleBlur = () => {
    setIsEditing(false)
    handleProjectRename(value.trim())
  }

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      handleBlur()
    }
    if (e.key === 'Escape') {
      setIsEditing(false)
      setValue(data?.projectName || '')
    }
  }

  const handleProjectRename = async (newName: string) => {
    // Validate project name
    if (!newName || newName.length === 0) {
      toast.error('Project name cannot be empty')
      setValue(data?.projectName || '')
      return
    }

    if (newName.length > 100) {
      toast.error('Project name cannot exceed 100 characters')
      setValue(data?.projectName || '')
      return
    }

    // Only update if name has changed
    if (newName === data?.projectName) {
      return
    }

    try {
      await updateProject(yproject.projectId, {
        projectName: newName,
      })
      await refetch()
      toast.success('Project name updated successfully')
    } catch (error) {
      console.error('Failed to update project name:', error)
      toast.error('Failed to update project name')
      setValue(data?.projectName || '')
    }
  }

  const handleEditClick = () => {
    setIsEditing(true)
    setValue(data?.projectName || '')
    setTimeout(() => inputRef.current?.focus?.(), 0)
  }
  // Render
  return (
    <div className="flex items-center justify-between p-2 bg-[#434343] text-white">
      {/* Left Section: Back to Home */}
      <BackToHomeSection />

      {/* Center Section: Project Name / Rename Input */}
      <div className="h-full flex justify-center items-center text-center w-2/5 text-sm relative">
        {isEditing ? (
          <Textarea
            ref={inputRef}
            className="text-center resize-none overflow-hidden h-8 min-h-[32px] px-2 py-1 border border-[#81c784] rounded-md focus:ring-2 focus:ring-[#81c784] w-full"
            placeholder="Enter project name"
            value={value}
            autoFocus
            onChange={handleChange}
            onKeyDown={handleKeyDown}
            onBlur={handleBlur}
            tag-id="edit-project-name"
          />
        ) : (
          <div
            className="flex items-center justify-center group cursor-pointer"
            onClick={handleEditClick}
          >
            <div className="text-base text-center font-arx truncate overflow-hidden max-w-full">
              {data?.projectName || 'Untitled Project'}
            </div>
            <Pencil
              className="ml-2 w-4 h-4 opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer"
              onClick={e => {
                e.stopPropagation()
                handleEditClick()
              }}
            />
          </div>
        )}
      </div>

      {/* Right Section: User List and Action Buttons */}
      <div className="flex items-center mr-4 w-2/5 justify-end gap-20">
        <UserListSection userList={uniqueUserList} />
        <ActionButtonsSection yproject={yproject} />
      </div>
    </div>
  )
}
