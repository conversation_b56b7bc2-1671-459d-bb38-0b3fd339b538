<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>PdfTeX basic example</title>
    <meta name="description" content="" />
    <meta name="author" content="" />
  </head>
  <script>
    function base64ToArrayBuffer(base64) {
      var binaryString = atob(base64)
      var bytes = new Uint8Array(binaryString.length)
      for (var i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i)
      }
      return bytes
    }

    function blobToBase64(blob) {
      return new Promise((resolve, _) => {
        const reader = new FileReader()
        reader.onloadend = () => resolve(reader.result)
        reader.readAsDataURL(blob)
      })
    }

    async function internalCompile(engine, workspace, mainFile) {
      engine.flushCache()
      const folders = await getFoldersToCreate(
        workspace.map(({ path }) => path),
      )
      folders.forEach(folder => engine.makeMemFSFolder(folder))
      await Promise.all(
        workspace.map(async ({ path }) => {
          const base64 = await readFile(path)
          const buffer = base64ToArrayBuffer(base64)
          engine.writeMemFSFile(path, buffer)
        }),
      )
      engine.setEngineMainFile(mainFile)
      return engine.compileLaTeX()
    }

    var compileComplete = false
    async function compile(workspace, mainFile) {
      compileComplete = false
      const { PdfTeXEngine } = await import('./PdfTeXEngine.js')
      const pdfTeXEngine = new PdfTeXEngine()
      await pdfTeXEngine.loadEngine()
      let r = await internalCompile(pdfTeXEngine, workspace, mainFile)
      console.log(r.log)
      if (r.status === 0) {
        const pdfblob = new Blob([r.pdf], { type: 'application/pdf' })
        const base64 = await blobToBase64(pdfblob)
        await saveResult(base64)
      }
      compileComplete = true
    }
  </script>
</html>
