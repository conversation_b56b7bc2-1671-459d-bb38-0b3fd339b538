import * as Y from 'yjs'

export function isTextFile(filename: string) {
  const name = filename.toLowerCase()
  const textExtensions = [
    '.sty',
    '.bst',
    '.tex',
    '.bib',
    '.txt',
    '.csv',
    '.log',
    '.md',
    '.markdown',
    '.json',
    '.xml',
    '.html',
    '.htm',
    '.css',
    '.js',
    '.ts',
    '.py',
    '.java',
    '.c',
    '.cpp',
    '.h',
    '.sh',
    '.bat',
    '.php',
    '.sql',
    '.yaml',
    '.yml',
    '.ini',
    '.cfg',
    '.conf',
  ]

  return textExtensions.some(ext => name.endsWith(ext))
}

export function revertToState(doc: Y.Doc, state: Uint8Array, origin?: unknown) {
  const tempDoc = new Y.Doc({ gc: false })
  Y.applyUpdate(tempDoc, state)
  const undoManager = new Y.UndoManager(tempDoc)
  const currentState = Y.encodeStateAsUpdate(doc)
  Y.applyUpdate(tempDoc, currentState)
  undoManager.undo()
  const revertedState = Y.encodeStateAsUpdate(tempDoc)
  const mergedUpdate = Y.mergeUpdates([state, revertedState])
  Y.applyUpdate(doc, mergedUpdate, origin)
  tempDoc.destroy()
  undoManager.destroy()
}
