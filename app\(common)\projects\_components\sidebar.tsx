import React, { useState } from 'react'
import { cn } from '@/lib/utils'
import {
  AllProject,
  ShareProject,
  Trash,
  YourProject,
} from '@/components/ar-icons'
import { ChevronDown } from 'lucide-react'
import ZipUploadDialog from '@/components/zip-upload-dialog'
import { CreateProjectDialog } from './create-project-dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { invalidateGetProjects } from '@/services/projects/get-projects'
import importZipProject from '@/lib/y-adapter/import-zip-project'
import loadingToast from '@/lib/y-adapter/import-files-toast'

interface SlideProps {
  filterKey: string
  setFilterKey: (key: string) => void
}

// Menu items configuration
const menuItems = [
  { key: 'all', label: 'All Projects', icon: <AllProject /> },
  { key: 'owner', label: 'Your Projects', icon: <YourProject /> },
  { key: 'member', label: 'Shared With You', icon: <ShareProject /> },
  { key: 'deleted', label: 'Trash', icon: <Trash /> },
]

export default function Sidebar({
  filterKey: filterKey,
  setFilterKey: setFilterKey,
}: SlideProps) {
  const [newDialogOpen, setNewDialogOpen] = useState(false)
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false)

  return (
    <div className="flex flex-col px-2 w-full text-base font-semibold leading-4 text-center text-black border-0 border-black border-solid shadow-sm bg-white pt-4">
      <div className="space-y-1">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <div
              className="flex gap-3 px-3 py-2 rounded-2xl h-10 text-[1rem] items-center justify-center text-white bg-[#33B4FF] cursor-pointer"
              style={{ marginBottom: '1rem' }}
            >
              <h1>New Project</h1>
              <ChevronDown className="h-4 w-4" />
            </div>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuItem onClick={() => setNewDialogOpen(true)}>
              Create New
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => setUploadDialogOpen(true)}>
              Upload Zip
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
        {menuItems.map(item => {
          return (
            <div
              key={item.key}
              onClick={() => setFilterKey(item.key)}
              className={cn(
                'flex md:gap-5 px-2 md:px-5 py-3 rounded-2xl my-1 h-14 text-[1rem] items-center w-full justify-start font-sans',
                filterKey === item.key
                  ? 'bg-[rgba(51,180,255,0.2)]'
                  : 'cursor-pointer',
              )}
            >
              {item.icon}
              <span>{item.label}</span>
            </div>
          )
        })}
      </div>
      <CreateProjectDialog
        open={newDialogOpen}
        setOpen={setNewDialogOpen}
        refreshProjects={invalidateGetProjects}
      />
      <ZipUploadDialog
        open={uploadDialogOpen}
        setOpen={setUploadDialogOpen}
        callback={async (file, unwrapFirstFolder) => {
          await loadingToast(importZipProject(file, { unwrapFirstFolder }))
          await invalidateGetProjects()
        }}
      />
    </div>
  )
}
