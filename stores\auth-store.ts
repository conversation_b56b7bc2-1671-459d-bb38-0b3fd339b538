// TODO: Deprecated, remove them
// store/auth-store.ts
import { create } from 'zustand'

interface LoginDialogState {
  isOpen: boolean
  pendingOperation: (() => void) | null
  setIsOpen: (isOpen: boolean) => void
  setPendingOperation: (operation: (() => void) | null) => void
}

export const useLoginDialogStore = create<LoginDialogState>(set => ({
  isOpen: false,
  pendingOperation: null,
  setIsOpen: isOpen => set({ isOpen }),
  setPendingOperation: operation => set({ pendingOperation: operation }),
}))
