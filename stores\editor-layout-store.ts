/*
 * @Description:
 * @Author: Devin
 * @Date: 2024-05-28 13:48:03
 */
import { create } from 'zustand'

export const useEditorLayoutStore = create(() => ({
  showHeader: false,
  showSide: true,
  showEditor: true,
  showView: true,
  showXterm: false,
  showFooter: true,
  sideWidth: 220,
  xtermHeight: 150,
  willResizing: false,
}))

export const layoutStoreActions = {
  toggleSide: () =>
    useEditorLayoutStore.setState(state => ({ showSide: !state.showSide })),

  toggleXterm: () =>
    useEditorLayoutStore.setState(state => ({ showXterm: !state.showXterm })),

  toggleEditor: () =>
    useEditorLayoutStore.setState(state => {
      if (!state.showView && state.showEditor) {
        return {
          showView: !state.showView,
          showEditor: !state.showEditor,
        }
      }
      return { showEditor: !state.showEditor }
    }),

  changeShowEditor: (show: boolean) =>
    useEditorLayoutStore.setState({ showEditor: show }),

  changeShowView: (show: boolean) =>
    useEditorLayoutStore.setState({ showView: show }),

  toggleView: () =>
    useEditorLayoutStore.setState(state => {
      if (!state.showEditor && state.showView) {
        return {
          showView: !state.showView,
          showEditor: !state.showEditor,
        }
      }
      return { showView: !state.showView }
    }),

  toggleFooter: () =>
    useEditorLayoutStore.setState(state => ({ showFooter: !state.showFooter })),

  setSideWidth: (width: number) =>
    useEditorLayoutStore.setState({ sideWidth: width }),

  setXtermHeight: (height: number) =>
    useEditorLayoutStore.setState({ xtermHeight: height }),

  setWillResizing: (resizing: boolean) =>
    useEditorLayoutStore.setState({ willResizing: resizing }),
}
