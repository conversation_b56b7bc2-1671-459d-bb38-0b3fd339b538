import { ComponentProps } from 'react'
import Image from 'next/image'
import { getSize, IconSize } from './icon-sizes'

export default function CopyHover(
  props: Partial<ComponentProps<typeof Image>> & {
    size?: IconSize | [number, number]
  },
) {
  const [width, height] = Array.isArray(props.size)
    ? props.size
    : getSize(props.size)
  return (
    <Image
      src="/assets/chat/copy-hover.svg"
      alt="CopyHover"
      width={width}
      height={height}
      {...props}
    />
  )
}
