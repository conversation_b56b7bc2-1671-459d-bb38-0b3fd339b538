import Link from 'next/link'
import { UserMenu } from './user-menu'
import { cn } from '@/lib/utils'

interface NavLinksProps {
  variant: 'desktop' | 'mobile'
}

const navItems = [
  { label: 'Home', href: '/' },
  { label: 'Career', href: '/explore' },
  { label: 'Latex Editor', href: '/projects' },
  { label: 'Template', href: '/template' },
  { label: 'AI Scientist', href: '/ai-scientist' },
]

export const NavLinks: React.FC<NavLinksProps> = ({ variant }) => {
  const isDesktop = variant === 'desktop'

  return (
    <div
      className={cn(
        isDesktop ? 'flex items-center space-x-7' : 'flex flex-col space-y-6',
      )}
    >
      {navItems.map(item => (
        <Link
          key={item.label}
          href={item.href}
          className="text-gray-900 hover:text-blue-900 text-[1.1rem] font-semibold transition-colors"
        >
          {item.label}
        </Link>
      ))}
      <UserMenu variant={variant} />
    </div>
  )
}
