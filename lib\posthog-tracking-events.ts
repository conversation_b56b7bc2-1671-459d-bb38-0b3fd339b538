import { posthog } from '@/lib/posthog'

// PostHog 事件发送函数
export const sendPostHogEvent = (eventName: string) => {
  if (typeof window !== 'undefined' && posthog) {
    posthog.capture(eventName)
  }
}

// 事件配置类型
export interface EventConfig {
  event: string
  handler: () => void
}

// 页面事件映射类型
export type PageEventMapping = {
  [key: string]: EventConfig
}

// 事件映射配置
export const eventMapping: Record<string, PageEventMapping> = {
  '/login': {
    'login-button': {
      event: 'login_success',
      handler: () => sendPostHogEvent('login_success'),
    },
  },
  '/register': {
    'register-button': {
      event: 'signup_completed',
      handler: () => sendPostHogEvent('signup_completed'),
    },
  },

  '/forgot-password': {
    'reset-password-submit': {
      event: 'get_reset_password_link',
      handler: () => sendPostHogEvent('get_reset_password_link'),
    },
  },

  '/projects': {
    'create-project-button': {
      event: 'project_create',
      handler: () => sendPostHogEvent('project_create'),
    },

    'upload-project-button': {
      event: 'project_upload',
      handler: () => sendPostHogEvent('project_upload'),
    },

    'delete-project-button': {
      event: 'project_delete',
      handler: () => sendPostHogEvent('project_delete'),
    },

    'delete-project-cancel': {
      event: 'project_delete_cancel',
      handler: () => sendPostHogEvent('project_delete_cancelled'),
    },

    'delete-project-accomplish': {
      event: 'project_delete_accomplish',
      handler: () => sendPostHogEvent('project_delete_accomplished'),
    },

    'download-project-button': {
      event: 'project_download',
      handler: () => sendPostHogEvent('project_download'),
    },

    'download-pdf-button': {
      event: 'project_download_pdf',
      handler: () => sendPostHogEvent('project_download_pdf'),
    },

    'duplicate-project-button': {
      event: 'project_duplicate',
      handler: () => sendPostHogEvent('project_duplicate'),
    },

    'duplicate-project-cancel': {
      event: 'project_duplicate_cancel',
      handler: () => sendPostHogEvent('project_duplicate_cancelled'),
    },

    'duplicate-project-accomplish': {
      event: 'project_duplicate_accomplish',
      handler: () => sendPostHogEvent('project_duplicate_accomplished'),
    },

    'share-project-button': {
      event: 'project_share',
      handler: () => sendPostHogEvent('project_share'),
    },

    'share-project-link': {
      event: 'project_share',
      handler: () => sendPostHogEvent('project_share'),
    },
    'share-project-email': {
      event: 'project_share',
      handler: () => sendPostHogEvent('project_share'),
    },
    'share-link-created': {
      event: 'project_share_link_created',
      handler: () => sendPostHogEvent('project_share_link_created'),
    },
    'share-link-failed': {
      event: 'project_share_error',
      handler: () => sendPostHogEvent('project_share_link_failed'),
    },
    'share-email-sent': {
      event: 'project_share_email_sent',
      handler: () => sendPostHogEvent('project_share_email_sent'),
    },
    'share-email-failed': {
      event: 'project_share_email_failed',
      handler: () => sendPostHogEvent('project_share_email_failed'),
    },
    'generate-link-success': {
      event: 'project_share_link_created',
      handler: () => sendPostHogEvent('project_share_link_created'),
    },
  },

  '/documents': {
    'create-document-button': {
      event: 'document_create',
      handler: () => sendPostHogEvent('document_create'),
    },
    'delete-document-button': {
      event: 'document_delete',
      handler: () => sendPostHogEvent('document_delete'),
    },
  },

  '/editor': {
    // PageTopBar 事件
    'back-to-home': {
      event: 'editor_back_to_home',
      handler: () => sendPostHogEvent('editor_navigation_back_to_home'),
    },
    'edit-project-name': {
      event: 'editor_project_name_edit',
      handler: () => sendPostHogEvent('editor_project_name_edit_started'),
    },
    'save-project-name': {
      event: 'editor_project_name_save',
      handler: () => sendPostHogEvent('editor_project_name_saved'),
    },
    'publish-project': {
      event: 'editor_project_publish',
      handler: () => sendPostHogEvent('editor_project_published'),
    },
    'unpublish-project': {
      event: 'editor_project_unpublish',
      handler: () => sendPostHogEvent('editor_project_unpublished'),
    },
    'view-history': {
      event: 'editor_view_history',
      handler: () => sendPostHogEvent('editor_version_history_viewed'),
    },
    'share-project': {
      event: 'editor_share_project',
      handler: () => sendPostHogEvent('project_share'),
    },
    'download-project': {
      event: 'editor_download_project',
      handler: () => sendPostHogEvent('project_download'),
    },

    // Explorer 事件
    'create-new-file': {
      event: 'editor_create_file',
      handler: () => sendPostHogEvent('text_create'),
    },
    'create-new-folder': {
      event: 'editor_create_folder',
      handler: () => sendPostHogEvent('editor_folder_created'),
    },
    'upload-file': {
      event: 'editor_upload_file',
      handler: () => sendPostHogEvent('text_create'),
    },
    'delete-file': {
      event: 'editor_delete_file',
      handler: () => sendPostHogEvent('text_delete'),
    },
    'rename-file': {
      event: 'editor_rename_file',
      handler: () => sendPostHogEvent('editor_file_renamed'),
    },
    'open-file': {
      event: 'editor_open_file',
      handler: () => sendPostHogEvent('editor_file_opened'),
    },

    // Output/Compilation 事件
    'compile-document': {
      event: 'editor_compile',
      handler: () => sendPostHogEvent('editor_compile_started'),
    },
    'compile-success': {
      event: 'editor_compile_success',
      handler: () => sendPostHogEvent('editor_compile_succeeded'),
    },
    'compile-error': {
      event: 'editor_compile_error',
      handler: () => sendPostHogEvent('editor_compile_failed'),
    },
    'change-engine-pdftex': {
      event: 'editor_engine_change',
      handler: () => sendPostHogEvent('editor_compile_engine_changed'),
    },
    'change-engine-xetex': {
      event: 'editor_engine_change',
      handler: () => sendPostHogEvent('editor_compile_engine_changed'),
    },
    'view-compile-log': {
      event: 'editor_view_compile_log',
      handler: () => sendPostHogEvent('editor_compile_log_viewed'),
    },
    'download-pdf': {
      event: 'editor_download_pdf',
      handler: () => sendPostHogEvent('project_download_pdf'),
    },

    // Text Editor 事件
    'text-edit': {
      event: 'editor_text_edit',
      handler: () => sendPostHogEvent('text_edit'),
    },
    'auto-save': {
      event: 'editor_auto_save',
      handler: () => sendPostHogEvent('editor_text_auto_saved'),
    },

    // Import Files 事件
    'import-files-dialog': {
      event: 'editor_import_dialog_opened',
      handler: () => sendPostHogEvent('editor_import_dialog_opened'),
    },
    'import-upload-files': {
      event: 'editor_import_upload_files',
      handler: () => sendPostHogEvent('editor_import_upload_files'),
    },
    'import-upload-folder': {
      event: 'editor_import_upload_folder',
      handler: () => sendPostHogEvent('editor_import_upload_folder'),
    },
    'import-drag-drop': {
      event: 'editor_import_drag_drop',
      handler: () => sendPostHogEvent('editor_import_drag_drop'),
    },
    'import-add-more-files': {
      event: 'editor_import_add_more_files',
      handler: () => sendPostHogEvent('editor_import_add_more_files'),
    },
    'import-add-more-folder': {
      event: 'editor_import_add_more_folder',
      handler: () => sendPostHogEvent('editor_import_add_more_folder'),
    },
    'import-select-all': {
      event: 'editor_import_select_all',
      handler: () => sendPostHogEvent('editor_import_select_all'),
    },
    'import-remove-file': {
      event: 'editor_import_remove_file',
      handler: () => sendPostHogEvent('editor_import_remove_file'),
    },
    'import-confirm': {
      event: 'editor_import_confirm',
      handler: () => sendPostHogEvent('editor_import_confirm'),
    },
    'import-cancel': {
      event: 'editor_import_cancel',
      handler: () => sendPostHogEvent('editor_import_cancel'),
    },
  },
}

export function getEventConfig(
  pathname: string,
  tagId: string,
): EventConfig | undefined {
  return eventMapping[pathname]?.[tagId]
}
