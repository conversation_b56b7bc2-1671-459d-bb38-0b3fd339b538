import { z } from 'zod'
import { YProject } from '.'
import * as Y from 'yjs'

const YProjectConfigInputSchema = z.object({
  mainFileId: z.string().uuid().optional(),
  engineType: z.enum(['pdftex', 'xetex']),
})

export type YProjectConfigInput = z.infer<typeof YProjectConfigInputSchema>
export type YProjectConfigOutput = YProjectConfigInput & {
  /**
   * Computed property from mainFileId
   */
  mainFile: string
}

export const defaultConfig: YProjectConfigInput = {
  engineType: 'pdftex',
}

export default class ConfigHandler {
  private PROJECT_CONFIG_KEY = 'PROJECT_CONFIG'
  private MAIN_CONFIG_KEY = 'MAIN_CONFIG'
  public yproject: YProject
  public configMap: Y.Map<string>

  constructor(yproject: YProject) {
    this.yproject = yproject
    this.configMap = yproject.ydoc.getMap(this.PROJECT_CONFIG_KEY)
  }

  public getConfig(): YProjectConfigOutput {
    let config = defaultConfig
    const configString = this.configMap.get(this.MAIN_CONFIG_KEY)
    if (configString) {
      const parsed = JSON.parse(configString)
      // Validate fields individually
      const partialSchema = YProjectConfigInputSchema.partial()
      const partialValidation = partialSchema.safeParse(parsed)
      if (partialValidation.success) {
        // Merge valid fields with default
        config = {
          ...defaultConfig,
          ...partialValidation.data,
        }
      }
    }

    const findMainFile = () => {
      if (config.mainFileId) {
        const configPath = this.yproject.fileIndex.getTextNodePathByUuid(
          config.mainFileId,
        )
        if (configPath) {
          return configPath
        }
      }

      const firstTexPath = this.yproject.fileIndex.getDefaultTexFilePath()
      if (firstTexPath) {
        return firstTexPath
      }

      return 'main.tex'
    }

    return {
      ...config,
      mainFile: findMainFile(),
    }
  }

  public setConfig(config: YProjectConfigInput): void {
    this.configMap.set(this.MAIN_CONFIG_KEY, JSON.stringify(config))
  }
}
