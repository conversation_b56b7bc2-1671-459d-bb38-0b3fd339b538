// components/PostHogPageViewTracking.tsx
'use client'

import { usePathname, useSearchParams } from 'next/navigation'
import { Suspense, useEffect } from 'react'
import { posthog } from '@/lib/posthog'
import {
  initializeGlobalClickTracking,
  cleanupGlobalClickTracking,
} from '@/hooks/usePostHogTracking'

function InternalPostHogPageViewTracking() {
  const pathname = usePathname()
  const searchParams = useSearchParams()

  useEffect(() => {
    const url =
      pathname + (searchParams?.toString() ? `?${searchParams.toString()}` : '')

    trackPageView({
      url,
      path: pathname,
      search: searchParams?.toString() || '',
    })
  }, [pathname, searchParams])

  // initialize global click tracking
  useEffect(() => {
    initializeGlobalClickTracking()
    return () => {
      cleanupGlobalClickTracking()
    }
  }, [pathname, searchParams])

  return null
}

// page view tracking
export default function PostHogPageViewTracking() {
  return (
    <Suspense>
      <InternalPostHogPageViewTracking />
    </Suspense>
  )
}

function trackPageView({
  url,
  path,
  search,
}: {
  url: string
  path: string
  search: string
}) {
  try {
    if (typeof window === 'undefined') {
      console.warn(
        'PostHog PageView tracking skipped: not in browser environment',
      )
      return
    }

    if (posthog) {
      posthog.capture('$pageview', {
        $current_url: url,
        $pathname: path,
        $search: search,
        timestamp: new Date().toISOString(),
      })

      console.warn('PostHog page view tracked successfully:', {
        event: '$pageview',
        $current_url: url,
        $pathname: path,
        $search: search,
      })
    } else {
      console.warn('PostHog not available for page view tracking')
    }
  } catch (error) {
    console.error('Error tracking PostHog page view:', error)
  }
}
