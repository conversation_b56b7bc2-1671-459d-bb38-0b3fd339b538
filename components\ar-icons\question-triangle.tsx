import { ComponentProps } from 'react'
import Image from 'next/image'
import { getSize, IconSize } from './icon-sizes'

export default function QuestionTriangle(
  props: Partial<ComponentProps<typeof Image>> & {
    size?: IconSize | [number, number]
  },
) {
  const [width, height] = Array.isArray(props.size)
    ? props.size
    : getSize(props.size)
  return (
    <Image
      src="/assets/chat/question-triangle.svg"
      alt="QuestionTriangle"
      width={width}
      height={height}
      {...props}
    />
  )
}
