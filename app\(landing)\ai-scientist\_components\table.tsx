'use client'

import * as React from 'react'
import { Button } from '@/components/ui/button'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import getExperiment from '../../../../services/ai-scientist/get-experiment'
import importZipProject from '@/lib/y-adapter/import-zip-project'
import { useRouter } from 'next/navigation'
import { toast } from 'sonner'
import { useExperimentStatus } from '@/services/ai-scientist/get-experiment-status'
import { EXPERIMENT_NAMES } from '../constant'

export function AIScientistTable() {
  const router = useRouter()
  const { data: status } = useExperimentStatus()

  const formatRunningTime = (minutes: number | undefined) => {
    if (!minutes || minutes === 0) return 'N/A'

    const hours = Math.floor(minutes / 60)
    const remainingMinutes = Math.floor(minutes % 60)

    if (hours > 0) {
      return `${hours}h ${remainingMinutes}m`
    }
    return `${remainingMinutes}m`
  }

  const handleImportTemplate = async (experiment: string) => {
    try {
      const toastId = toast.loading('Downloading Template', {
        duration: Infinity,
      })
      const file = await getExperiment(experiment)
      const projectId = await importZipProject(file)
      router.push(`editor?project-id=${projectId}`)
      toast.success('Template imported successfully!', {
        id: toastId,
        duration: 3000,
      })
    } catch (error) {
      console.error('Error importing template:', error)
      toast.error('Failed to import template. Please try again.')
    }
  }

  return (
    <div className="w-full">
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Experiment</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Running Time</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {EXPERIMENT_NAMES.map(experiment => {
              const experimentStatus = status?.find(
                s => s.job_id === experiment,
              )
              return (
                <TableRow key={experiment}>
                  <TableCell>{experiment}</TableCell>
                  <TableCell>
                    {experimentStatus?.status || 'loading...'}
                  </TableCell>
                  <TableCell>
                    {formatRunningTime(experimentStatus?.running_time_minutes)}
                  </TableCell>
                  <TableCell>
                    <Button
                      disabled={experimentStatus?.status !== 'finished'}
                      variant="link"
                      className="p-0"
                      onClick={() => handleImportTemplate(experiment)}
                    >
                      Import as Template
                    </Button>
                  </TableCell>
                </TableRow>
              )
            })}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}
