import api from '../api'
import {
  Conversion,
  ConversionListItem,
  ConversionStatusResponse,
  DownloadResponse,
  CreateConversionRequest,
} from './types'

/**
 * 创建转换任务
 */
export const createConversion = async (
  data: CreateConversionRequest,
): Promise<{ message: string; conversion: Conversion }> => {
  const formData = new FormData()
  formData.append('source', data.source)
  formData.append('taskName', data.taskName)
  formData.append('targetTemplateId', data.targetTemplateID)

  const response = await api.post<{ message: string; conversion: Conversion }>(
    '/conversions',
    formData,
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    },
  )

  return response.data
}

/**
 * 获取转换任务列表
 */
export const getConversions = async (): Promise<{
  conversions: ConversionListItem[]
}> => {
  const response = await api.get<{ conversions: ConversionListItem[] }>(
    '/conversions',
  )
  return response.data
}

/**
 * 获取转换任务详情
 */
export const getConversion = async (
  id: string,
): Promise<{ conversion: Conversion }> => {
  const response = await api.get<{ conversion: Conversion }>(
    `/conversions/${id}`,
  )
  return response.data
}

/**
 * 获取转换状态
 */
export const getConversionStatus = async (
  id: string,
): Promise<ConversionStatusResponse> => {
  // 直接调用转换详情接口，它会返回完整的状态信息包括AI思路
  const response = await api.get<ConversionStatusResponse>(`/conversions/${id}`)
  return response.data
}

/**
 * 下载转换结果
 */
export const downloadConversionResult = async (
  id: string,
): Promise<DownloadResponse> => {
  const response = await api.get<DownloadResponse>(
    `/conversions/${id}/download`,
  )
  return response.data
}

/**
 * 取消转换任务（如果后端支持）
 */
export const cancelConversion = async (
  id: string,
): Promise<{ message: string }> => {
  const response = await api.delete<{ message: string }>(`/conversions/${id}`)
  return response.data
}

/**
 * 轮询转换状态直到完成
 */
export const pollConversionStatus = async (
  id: string,
  onProgress?: (status: ConversionStatusResponse) => void,
  interval: number = 3000, // 3秒轮询间隔
  timeout: number = 30 * 60 * 1000, // 30分钟超时
): Promise<ConversionStatusResponse> => {
  const startTime = Date.now()

  return new Promise((resolve, reject) => {
    const poll = async () => {
      try {
        // 检查超时
        if (Date.now() - startTime > timeout) {
          reject(new Error('Conversion status polling timeout'))
          return
        }

        const status = await getConversionStatus(id)

        if (onProgress) {
          onProgress(status)
        }

        // 如果完成或失败，停止轮询
        if (status.status === 'completed' || status.status === 'failed') {
          resolve(status)
          return
        }

        // 继续轮询
        setTimeout(poll, interval)
      } catch (error) {
        console.error('Error polling conversion status:', error)
        // 轮询出错时也继续轮询，但减少频率
        setTimeout(poll, interval * 2)
      }
    }

    poll()
  })
}

/**
 * 创建转换任务并开始状态轮询
 */
export const createConversionWithPolling = async (
  data: CreateConversionRequest,
  onProgress?: (status: ConversionStatusResponse) => void,
): Promise<{
  conversion: Conversion
  finalStatus: ConversionStatusResponse
}> => {
  // 首先创建转换任务
  const createResult = await createConversion(data)

  // 开始轮询状态
  const finalStatus = await pollConversionStatus(
    createResult.conversion.id,
    onProgress,
  )

  return {
    conversion: createResult.conversion,
    finalStatus,
  }
}

/**
 * 获取转换状态的简化版本（用于快速检查）
 */
export const getConversionStatusSimple = async (
  id: string,
): Promise<{ status: string; isCompleted: boolean; hasError: boolean }> => {
  try {
    const response = await getConversionStatus(id)
    return {
      status: response.status,
      isCompleted: response.status === 'completed',
      hasError: response.status === 'failed',
    }
  } catch (error) {
    console.error('Failed to get conversion status:', error)
    return {
      status: 'unknown',
      isCompleted: false,
      hasError: true,
    }
  }
}
