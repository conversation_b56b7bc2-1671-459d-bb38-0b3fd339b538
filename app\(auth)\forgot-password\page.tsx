// app/(auth)/forgot-password/page.tsx
'use client'

import React, { useState } from 'react'
import { useForm, FormProvider } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import Link from 'next/link'
import { toast } from 'sonner'
import { FormInput } from '@/components/form/form-input'
import { Button } from '@/components/ui/button'
import { Mail } from 'lucide-react'

import {
  forgotPassword,
  ForgotPasswordFormValues,
  forgotPasswordSchema,
} from '@/services/auth/password-reset'

export default function ForgotPasswordPage() {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [emailSent, setEmailSent] = useState(false)

  const methods = useForm<ForgotPasswordFormValues>({
    resolver: zodResolver(forgotPasswordSchema),
    defaultValues: {
      email: '',
    },
  })

  const {
    handleSubmit,
    formState: { errors },
  } = methods

  const onSubmit = async (data: ForgotPasswordFormValues) => {
    setIsSubmitting(true)
    try {
      const response = await forgotPassword(data.email)
      toast.success(
        response.message || 'Password reset link sent to your email',
      )
      setEmailSent(true)
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="flex items-center justify-center h-full overflow-auto bg-[#ffffff]">
      <div
        className="flex flex-col items-center justify-center bg-white p-8 rounded shadow-md"
        style={{ maxWidth: '33rem', width: '100%', marginTop: '10rem' }}
      >
        <h1
          className="text-4xl font-bold text-center mb-6 pt-2 lg:text-5xl bg-gradient-to-r from-[#33b4ff] to-[#ffae00] 
          bg-clip-text text-transparent"
        >
          Forget Password
        </h1>
        <h2 className="text-md mb-6">
          {emailSent
            ? 'Check your email for the password reset link'
            : 'Enter your email address and we will send you a verification email'}
        </h2>

        <FormProvider {...methods}>
          <form
            noValidate
            autoComplete="off"
            className="w-full"
            onSubmit={handleSubmit(onSubmit)}
          >
            <FormInput
              label="Email"
              type="email"
              name="email"
              placeholder="Email"
              register={methods.register}
              errors={errors}
              icon={<Mail className="h-5 w-5" />}
              required
            />
            <Button
              type="submit"
              className="w-full bg-[#33B4FF] text-white py-2 rounded hover:bg-blue-600 transition-colors mt-6"
              disabled={isSubmitting}
              tag-id="forgot-password-submit"
            >
              {isSubmitting ? 'Sending...' : 'Get The Password Reset Link'}
            </Button>
            <div className="my-4 text-center">
              <Link href="/login" className="text-[#33B4FF] hover:underline" tag-id="return-to-login-button">
                Back to Login
              </Link>
            </div>
          </form>
        </FormProvider>
      </div>
    </div>
  )
}
