import { useEffect } from 'react'
import { MonacoBinding } from 'y-monaco'
import type { editor as monacoEditor } from 'monaco-editor'
import { YProject } from '@/lib/y-project'

export function useBindYDoc(
  yproject: YProject,
  documentId?: string,
  editor?: monacoEditor.IStandaloneCodeEditor,
) {
  useEffect(() => {
    if (!editor || !documentId) return
    const ytext = yproject.fileIndex.documentMap.get(documentId)
    let binding: MonacoBinding | undefined = undefined
    if (!ytext) {
      editor.setValue('The document is not found in the version')
    } else {
      const provider = yproject.onlineYDoc?.websocketProvider
      binding = new MonacoBinding(
        ytext,
        editor.getModel()!,
        new Set([editor]),
        provider?.awareness,
      )

      const undoManager = yproject.undoHandler.getTextManager(documentId)
      undoManager?.addTrackedOrigin(binding)
    }

    return () => {
      if (binding) {
        const undoManager = yproject.undoHandler.getTextManager(documentId)
        undoManager?.removeTrackedOrigin(binding)
      }
      binding?.destroy()
    }
  }, [documentId, editor, yproject])
}
