import { useEffect, useState } from 'react'
import { YProject } from '../y-project'

export interface CollaborateUser {
  key: number
  color: string
  id?: string
  name?: string
}

// Function to generate a consistent number from UUID
function getNumberFromUUID(uuid: string): number {
  // Take first 8 characters of UUID and convert to number
  const firstPart = uuid.replace(/-/g, '').slice(0, 8)
  return parseInt(firstPart, 16) % 360
}

export default function useAwareness(yproject: YProject) {
  const [userList, setUserList] = useState<CollaborateUser[]>([])

  useEffect(() => {
    const awareness = yproject.onlineYDoc?.websocketProvider?.awareness
    if (!awareness) return

    const observe = () => {
      const states = awareness.getStates()
      const userList = [...states.entries()]
        .map(([key, state]) => {
          const userId = state.user?.id
          const hue = userId ? getNumberFromUUID(userId) : key % 360
          return {
            key,
            color: `oklch(0.7211 0.1416 ${hue})`,
            id: userId,
            name: state.user?.name || 'Anonymous',
          }
        })
        .filter((user: CollaborateUser) => user.name !== 'Anonymous')
      setUserList(userList)
    }

    observe()
    awareness?.on('change', observe)

    return () => {
      awareness?.off('change', observe)
    }
  }, [yproject.onlineYDoc?.websocketProvider?.awareness])

  return userList
}
