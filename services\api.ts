import axios, { AxiosError } from 'axios'
import { trackError } from '@/hooks/useErrorTracking'
import { sendPostHogEvent } from '@/lib/posthog-tracking-events'

interface ErrorResponse {
  message?: string
  code?: string | number
  details?: unknown
}

declare module 'axios' {
  export interface AxiosRequestConfig {
    metadata?: {
      startTime?: number
      responseTime?: number
    }
  }
}

export const BASE_URL = '/api/v1/'
const api = axios.create({
  baseURL: BASE_URL,
})

const calculateResponseTime = (startTime?: number): number => {
  if (!startTime) return 0
  return new Date().getTime() - startTime
}

api.interceptors.request.use(
  config => {
    config.metadata = { startTime: new Date().getTime() }
    return config
  },
  error => Promise.reject(error),
)

api.interceptors.response.use(
  response => {
    const responseTime = calculateResponseTime(
      response.config.metadata?.startTime,
    )
    response.config.metadata = {
      ...response.config.metadata,
      responseTime,
    }
    return response
  },
  (error: AxiosError<ErrorResponse>) => {
    const apiName = error.config?.url?.split('/').pop() || 'unknown'

    trackError.api(apiName, error)

    sendPostHogEvent('api_failure')

    if (error.response?.status === 401) {
      sendPostHogEvent('session_timeout')
    }

    return Promise.reject(error)
  },
)

export default api
