/**
 * FileIndex class manages the file structure and core file operations
 */
import { fileToUint8Array, sha256 } from '@/lib/utils'
import * as Y from 'yjs'
import { v4 as uuidv4 } from 'uuid'
import { downloadAssets, storeToCache } from '@/lib/file-asset'
import { getMissingFiles, retrieveFile } from '@/lib/file-cache'
import { YProject } from '.'
import { isTextFile } from './utils'
import postAssetBatch from '@/services/projects/post-asset-batch'

export type FileNode = AssetNode | TextNode | FolderNode
export type FolderNode = Y.Map<FileNode>
export interface AssetNode {
  isAsset: true
  hash: string
}

export interface TextNode {
  isText: true
  uuid: string
}

export interface InputFileEntry {
  path: string
  file: Blob
}

export interface OutputFileEntry {
  path: string
  buffer: ArrayBufferLike
}

/**
 * FileIndex class manages the file structure and core file operations \
 * Path is always absolute, starting without a leading slash.
 * For example: "folder1/folder2/file.txt"
 */
export default class FileIndex {
  private PROJECT_INDEX_KEY = 'PROJECT_INDEX'
  private DOCUMENT_MAP_KEY = 'DOCUMENT_MAP'

  public rootFolder: FolderNode
  public documentMap: Y.Map<Y.Text>
  public projectId: string
  public yproject: YProject

  constructor(yproject: YProject) {
    this.projectId = yproject.projectId
    this.yproject = yproject
    this.rootFolder = yproject.ydoc.getMap<FileNode>(this.PROJECT_INDEX_KEY)
    this.documentMap = yproject.ydoc.getMap<Y.Text>(this.DOCUMENT_MAP_KEY)
  }

  prepareDirectory(segments: string[]): FolderNode {
    let currentFolder = this.rootFolder
    for (let i = 0; i < segments.length - 1; i++) {
      const segment = segments[i]
      let node = currentFolder.get(segment)
      if (!node || !isFolderNode(node)) {
        node = new Y.Map<FileNode>()
        currentFolder.set(segment, node)
      }
      currentFolder = node
    }
    return currentFolder
  }

  public async importFiles(entries: InputFileEntry[]) {
    await this.yproject.initialSynced
    const textFileEntries = entries.filter(entry => isTextFile(entry.path))

    const createTextFilesPromise = Promise.all(
      textFileEntries.map(async entry => {
        const segments = entry.path
          .split('/')
          .filter(segment => segment.length > 0)

        const folderNode = this.prepareDirectory(segments)
        const fileName = segments[segments.length - 1]
        const uuid = uuidv4()
        folderNode.set(fileName, {
          isText: true,
          uuid,
        })

        const ytext = new Y.Text()
        const textContent = await entry.file.text()
        ytext.insert(0, textContent)
        this.documentMap.set(uuid, ytext)
      }),
    )

    const assetFileEntries = entries.filter(entry => !isTextFile(entry.path))
    const assets = assetFileEntries.map(entry => entry.file)
    const uploadResults = assets.length
      ? await postAssetBatch(this.projectId, assets)
      : []
    const successfulHashes = new Set<string>()
    const failedMessages: string[] = []
    uploadResults.forEach(result => {
      if (result.success) {
        successfulHashes.add(result.hash)
      } else {
        failedMessages.push(result.error)
      }
    })

    const createAssetNodesPromise = Promise.all(
      assetFileEntries.map(async entry => {
        const segments = entry.path
          .split('/')
          .filter(segment => segment.length > 0)

        const folderNode = this.prepareDirectory(segments)
        const fileName = segments[segments.length - 1]
        const uint8Array = await fileToUint8Array(entry.file)
        const hash = await sha256(uint8Array)
        if (successfulHashes.has(hash)) {
          folderNode.set(fileName, {
            isAsset: true,
            hash,
          })
          storeToCache(hash, uint8Array.buffer)
        }
      }),
    )

    await Promise.all([createTextFilesPromise, createAssetNodesPromise])

    return failedMessages
  }

  public clearFiles() {
    this.rootFolder.clear()
  }
  /**
   * Depth-first search through the file structure
   * @param callback - Function to call for each node, returning true to stop traversal
   */
  public dfs(callback: (node: FileNode, prefix: string) => boolean | void) {
    const queue: Array<{
      prefix: string
      node: FileNode
    }> = [
      {
        prefix: '',
        node: this.rootFolder,
      },
    ]

    while (queue.length > 0) {
      const { prefix, node } = queue.pop()!
      if (node) {
        if (isFolderNode(node)) {
          for (const [segment, child] of node) {
            queue.push({
              prefix: prefix ? prefix + '/' + segment : segment,
              node: child,
            })
          }
        }
        const shouldStop = callback(node, prefix)
        if (shouldStop) {
          break
        }
      }
    }
  }

  public bfs(callback: (node: FileNode, prefix: string) => boolean | void) {
    const queue: Array<{
      prefix: string
      node: FileNode
    }> = [
      {
        prefix: '',
        node: this.rootFolder,
      },
    ]
    while (queue.length > 0) {
      const { prefix, node } = queue.shift()!
      if (node) {
        if (isFolderNode(node)) {
          for (const [segment, child] of node) {
            queue.push({
              prefix: prefix ? prefix + '/' + segment : segment,
              node: child,
            })
          }
        }
        const shouldStop = callback(node, prefix)
        if (shouldStop) {
          break
        }
      }
    }
  }

  public async prepareAssets() {
    const hashes: string[] = []
    this.dfs(node => {
      if (isAssetNode(node)) {
        hashes.push(node.hash)
      }
    })
    const missingFiles = await getMissingFiles(hashes)
    if (missingFiles.length > 0) {
      await downloadAssets(this.projectId, missingFiles)
    }
  }

  // TODO: refactor to use exportIndex
  public async exportFiles(): Promise<OutputFileEntry[]> {
    await this.yproject.initialSynced
    await this.prepareAssets()
    const entries: Promise<OutputFileEntry>[] = []
    this.dfs((node, path) => {
      if (isAssetNode(node)) {
        entries.push(
          new Promise(async resolve => {
            const asset = await retrieveFile(node.hash)
            if (!asset) {
              throw new Error(`Asset not found for file ${path}`)
            }
            resolve({
              path,
              buffer: asset,
            })
          }),
        )
      } else if (isTextNode(node)) {
        const yText = this.documentMap.get(node.uuid)
        if (!yText) {
          throw new Error(`Document not found for file ${path}`)
        }
        const str = yText.toString()
        const uint8array = new TextEncoder().encode(str)
        entries.push(
          new Promise(res => {
            res({
              path,
              buffer: uint8array.buffer,
            })
          }),
        )
      }
    })
    return Promise.all(entries)
  }

  public exportIndex() {
    const entries: Array<{
      path: string
      content?: string
      hash?: string
    }> = []
    this.dfs((node, path) => {
      if (isAssetNode(node)) {
        entries.push({
          path,
          hash: node.hash,
        })
      } else if (isTextNode(node)) {
        const yText = this.documentMap.get(node.uuid)
        if (!yText) {
          throw new Error(`Document not found for file ${path}`)
        }
        entries.push({
          path,
          content: yText.toString(),
        })
      }
    })

    entries.sort((a, b) => a.path.localeCompare(b.path))

    return entries
  }

  public getNodeStrict(path: string) {
    const segments = path.split('/').filter(Boolean)
    if (segments.length === 0) {
      throw new Error('Path cannot be empty')
    }

    let currentNode: FileNode = this.rootFolder
    for (const [i, segment] of segments.entries()) {
      const nextNode: FileNode | undefined = isFolderNode(currentNode)
        ? currentNode.get(segment)
        : undefined
      if (!nextNode) {
        throw new Error(
          `Node not found at: ${segments.slice(0, i + 1).join('/')}`,
        )
      }
      currentNode = nextNode
    }
    return currentNode
  }

  public getNode(path: string) {
    const segments = path.split('/').filter(Boolean)
    if (segments.length === 0) {
      throw new Error('Path cannot be empty')
    }

    let currentNode: FileNode = this.rootFolder
    for (const [, segment] of segments.entries()) {
      const nextNode: FileNode | undefined = isFolderNode(currentNode)
        ? currentNode.get(segment)
        : undefined
      if (!nextNode) {
        return null
      }
      currentNode = nextNode
    }
    return currentNode
  }

  public getTextNodePathByUuid(uuid: string): string | null {
    let result: string | null = null
    this.dfs((node, path) => {
      if (isTextNode(node) && node.uuid === uuid) {
        result = path
        return true // Stop traversal once found
      }
    })
    return result
  }

  public getDefaultTexFilePath(): string | null {
    let result: string | null = null

    if (this.getNode('main.tex')) {
      return 'main.tex'
    }

    this.bfs((node, path) => {
      if (isTextNode(node) && path.endsWith('.tex')) {
        result = path
        return true // Stop traversal once found
      }
    })
    return result
  }
}

export function isAssetNode(node: unknown): node is AssetNode {
  return (node as AssetNode).isAsset === true
}

export function isTextNode(node: unknown): node is TextNode {
  return (node as TextNode).isText === true
}

export function isFolderNode(node: unknown): node is FolderNode {
  return node instanceof Y.Map
}
