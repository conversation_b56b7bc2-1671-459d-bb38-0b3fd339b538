'use client'

import { useState, useCallback, useRef, ReactNode } from 'react'
import { cn } from '@/lib/utils'
import { FileEntry } from './types'

// File System Entry API types
interface FileSystemEntry {
  isFile: boolean
  isDirectory: boolean
  name: string
  fullPath: string
}

interface FileSystemFileEntry extends FileSystemEntry {
  file(
    successCallback: (file: File) => void,
    errorCallback?: (error: Error) => void,
  ): void
}

interface FileSystemDirectoryEntry extends FileSystemEntry {
  createReader(): FileSystemDirectoryReader
}

interface FileSystemDirectoryReader {
  readEntries(
    successCallback: (entries: FileSystemEntry[]) => void,
    errorCallback?: (error: Error) => void,
  ): void
}

interface FileDropProps {
  onDrop: (files: FileEntry[]) => void
  children: ReactNode
  className?: string
  accept?: string[]
  multiple?: boolean
  disabled?: boolean
  onDragEnter?: () => void
  onDragLeave?: () => void
  onDragOver?: () => void
}

export default function FileDrop({
  onDrop,
  children,
  className,
  accept,
  multiple = true,
  disabled = false,
  onDragEnter,
  onDragLeave,
  onDragOver,
}: FileDropProps) {
  const [isDragging, setIsDragging] = useState(false)
  const [isActive, setIsActive] = useState(false)
  const dragCounter = useRef(0)
  const dropRef = useRef<HTMLDivElement>(null)

  const validateFileType = useCallback(
    (file: File): boolean => {
      if (!accept || accept.length === 0) return true

      const fileExtension = file.name.split('.').pop()?.toLowerCase()
      const mimeType = file.type.toLowerCase()

      return accept.some(acceptedType => {
        // Handle MIME types (e.g., "image/*", "application/pdf")
        if (acceptedType.includes('/')) {
          if (acceptedType.endsWith('/*')) {
            const baseType = acceptedType.split('/')[0]
            return mimeType.startsWith(baseType + '/')
          }
          return mimeType === acceptedType
        }

        // Handle file extensions (e.g., ".pdf", ".jpg")
        const cleanAcceptedType = acceptedType.startsWith('.')
          ? acceptedType.slice(1).toLowerCase()
          : acceptedType.toLowerCase()
        return fileExtension === cleanAcceptedType
      })
    },
    [accept],
  )

  const handleDrop = useCallback(
    (e: React.DragEvent<HTMLDivElement>) => {
      e.preventDefault()
      e.stopPropagation()

      if (disabled) return

      setIsDragging(false)
      setIsActive(false)
      dragCounter.current = 0

      // CRITICAL: Extract all data synchronously to prevent loss
      const droppedItems = Array.from(e.dataTransfer.items)

      // Extract all possible data SYNCHRONOUSLY
      const extractedData: Array<{
        index: number
        kind: string
        type: string
        entry: FileSystemEntry | null
        file: File | null
        fileName: string
      }> = []

      // Process each item immediately while DataTransferItemList is still valid
      for (let i = 0; i < droppedItems.length; i++) {
        const item = droppedItems[i]

        try {
          // Extract entry (for folders/files with path info)
          const entry = item.webkitGetAsEntry ? item.webkitGetAsEntry() : null

          // Extract file (direct file access)
          const file = item.getAsFile ? item.getAsFile() : null

          // Determine filename from available sources
          const fileName = file?.name || entry?.name || `unknown-${i}`

          extractedData.push({
            index: i,
            kind: item.kind || '',
            type: item.type || '',
            entry,
            file,
            fileName,
          })
        } catch (error) {
          console.error(`Failed to extract item ${i}:`, error)

          // Add empty entry to maintain index consistency
          extractedData.push({
            index: i,
            kind: '',
            type: '',
            entry: null,
            file: null,
            fileName: `failed-${i}`,
          })
        }
      }

      if (extractedData.length === 0) {
        return
      }

      const processFileSystemEntry = async (
        entry: FileSystemEntry,
        relativePath = '',
      ): Promise<FileEntry[]> => {
        const results: FileEntry[] = []

        if (entry.isFile) {
          return new Promise(resolve => {
            const fileEntry = entry as FileSystemFileEntry
            fileEntry.file(
              (file: File) => {
                const fullPath = relativePath
                  ? `${relativePath}/${file.name}`
                  : file.name

                // Apply file type validation
                if (validateFileType(file)) {
                  results.push({
                    file,
                    path: file.name,
                    relativePath: fullPath,
                  })
                }
                resolve(results)
              },
              (error: Error) => {
                console.error('Error reading file entry:', error)
                resolve(results) // Resolve with empty results instead of rejecting
              },
            )
          })
        } else if (entry.isDirectory) {
          return new Promise(resolve => {
            const dirEntry = entry as FileSystemDirectoryEntry
            const reader = dirEntry.createReader()

            const readAllEntries = async (): Promise<FileEntry[]> => {
              const allResults: FileEntry[] = []

              const readBatch = (): Promise<FileSystemEntry[]> => {
                return new Promise(resolveBatch => {
                  reader.readEntries(
                    (entries: FileSystemEntry[]) => {
                      resolveBatch(entries)
                    },
                    (error: Error) => {
                      console.error('Error reading directory entries:', error)
                      resolveBatch([]) // Return empty array on error
                    },
                  )
                })
              }

              let entries: FileSystemEntry[]
              do {
                entries = await readBatch()
                for (const childEntry of entries) {
                  const childPath = relativePath
                    ? `${relativePath}/${entry.name}`
                    : entry.name
                  try {
                    const childResults = await processFileSystemEntry(
                      childEntry,
                      childPath,
                    )
                    allResults.push(...childResults)
                  } catch (error) {
                    console.error('Error processing child entry:', error)
                  }
                }
              } while (entries.length > 0)

              return allResults
            }

            readAllEntries()
              .then(resolve)
              .catch(error => {
                console.error('Error reading all entries:', error)
                resolve(results)
              })
          })
        }

        return results
      }

      const processDroppedItems = async () => {
        const allFileEntries: FileEntry[] = []

        for (const itemData of extractedData) {
          try {
            // Priority 1: Use FileSystem Entry API (supports folders and proper paths)
            if (itemData.entry) {
              const fileEntries = await processFileSystemEntry(itemData.entry)
              allFileEntries.push(...fileEntries)
            }
            // Priority 2: Use direct file access
            else if (itemData.file) {
              if (validateFileType(itemData.file)) {
                allFileEntries.push({
                  file: itemData.file,
                  path: itemData.file.name,
                  relativePath: itemData.file.name,
                })
              }
            }
          } catch (error) {
            console.error(`Error processing item ${itemData.index}:`, error)
          }
        }

        if (allFileEntries.length === 0) {
          return
        }

        // Handle multiple file restriction
        const filesToProcess = multiple ? allFileEntries : [allFileEntries[0]]

        onDrop(filesToProcess)
      }

      processDroppedItems().catch(console.error)
    },
    [onDrop, multiple, disabled, validateFileType],
  )

  const handleDragOver = useCallback(
    (e: React.DragEvent<HTMLDivElement>) => {
      e.preventDefault()
      e.stopPropagation()

      if (disabled) return

      if (!isDragging) {
        setIsDragging(true)
      }

      // Check if dropped items contain files
      const hasFiles = Array.from(e.dataTransfer.types).includes('Files')
      if (hasFiles && !isActive) {
        setIsActive(true)
      }

      onDragOver?.()
    },
    [isDragging, isActive, disabled, onDragOver],
  )

  const handleDragEnter = useCallback(
    (e: React.DragEvent<HTMLDivElement>) => {
      e.preventDefault()
      e.stopPropagation()

      if (disabled) return

      dragCounter.current++

      if (!isDragging) {
        setIsDragging(true)
        onDragEnter?.()
      }
    },
    [isDragging, disabled, onDragEnter],
  )

  const handleDragLeave = useCallback(
    (e: React.DragEvent<HTMLDivElement>) => {
      e.preventDefault()
      e.stopPropagation()

      if (disabled) return

      dragCounter.current--

      if (dragCounter.current === 0) {
        setIsDragging(false)
        setIsActive(false)
        onDragLeave?.()
      }
    },
    [disabled, onDragLeave],
  )

  const getAcceptString = useCallback(() => {
    if (!accept || accept.length === 0) return undefined
    return accept.join(',')
  }, [accept])

  return (
    <div
      ref={dropRef}
      onDrop={handleDrop}
      onDragOver={handleDragOver}
      onDragEnter={handleDragEnter}
      onDragLeave={handleDragLeave}
      className={cn(
        'relative transition-colors duration-200',
        isDragging && !disabled && 'ring-2 ring-primary ring-offset-2',
        isActive && !disabled && 'bg-primary/5',
        disabled && 'pointer-events-none opacity-50',
        className,
      )}
      data-testid="file-drop"
      data-dragging={isDragging}
      data-active={isActive}
      data-disabled={disabled}
      data-accept={getAcceptString()}
    >
      {children}

      {/* Overlay for visual feedback */}
      {isDragging && !disabled && (
        <div className="absolute inset-0 z-10 pointer-events-none">
          <div className="h-full w-full rounded-lg border-2 border-dashed border-primary bg-primary/10 flex items-center justify-center">
            <div className="text-center">
              <div className="text-2xl mb-2">📎</div>
              <p className="text-sm font-medium text-primary">
                {isActive ? 'Drop files here' : 'Drop to upload'}
              </p>
              {accept && accept.length > 0 && (
                <p className="text-xs text-muted-foreground mt-1">
                  Accepts: {accept.join(', ')}
                </p>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

// Export types for easier usage
export type { FileDropProps }
