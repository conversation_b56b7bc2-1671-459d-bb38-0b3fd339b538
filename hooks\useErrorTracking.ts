import { useCallback } from 'react'
import { posthog } from '@/lib/posthog'
import { AxiosError } from 'axios'

interface ErrorResponse {
  message?: string
  code?: string | number
  details?: unknown
}

export const trackError = {
  api: (apiName: string, error: AxiosError<ErrorResponse>) => {
    if (typeof window === 'undefined' || !posthog) return

    const errorData = error.response
      ? {
          error_type: 'http_error',
          status: error.response.status,
          url: error.response.config.url,
          method: error.response.config.method?.toUpperCase(),
          api_name: apiName,
          message: error.response.data?.message || error.message,
          response_time_ms: error.config?.metadata?.responseTime,
        }
      : {
          error_type: 'network_error',
          message: error.message,
          url: error.config?.url,
          method: error.config?.method?.toUpperCase(),
          api_name: apiName,
          error_code: error.code || 'UNKNOWN_ERROR',
          response_time_ms: error.config?.metadata?.responseTime,
        }

    posthog.capture('$exception', errorData)
  },
}

export const useErrorTracking = () => {
  const trackApiError = useCallback(
    (apiName: string, error: AxiosError<ErrorResponse>) => {
      trackError.api(apiName, error)
    },
    [],
  )

  return {
    trackApiError,
  }
}
