'use client'

import React, { useRef, useState, MouseEvent, useEffect } from 'react'

interface ThreePaneLayoutProps {
  left?: React.ReactNode
  center: React.ReactNode
  right?: React.ReactNode
}

export default function ThreePaneLayout({
  left,
  center,
  right,
}: ThreePaneLayoutProps) {
  // ==================== REFS ====================
  const didDrag = useRef(false)
  const containerRef = useRef<HTMLDivElement>(null)

  // ==================== STATE ====================
  // Panel dimensions
  const [leftWidth, setLeftWidth] = useState(200)
  const [rightWidth, setRightWidth] = useState(200)

  // Panel visibility
  const [leftCollapsed, setLeftCollapsed] = useState(false)
  const [rightCollapsed, setRightCollapsed] = useState(false)

  // Virtual drag state
  const [isDragging, setIsDragging] = useState(false)
  const [dragDirection, setDragDirection] = useState<'left' | 'right' | null>(
    null,
  )
  const [virtualBarPosition, setVirtualBarPosition] = useState(0)

  // ==================== EFFECTS ====================
  // Set initial widths based on container size to achieve a
  // sidebar:editor:preview ratio of 1:4:5. Recalc on window resize
  // to keep the ratio consistent when the viewport changes.
  useEffect(() => {
    const handleResize = () => {
      const containerWidth = containerRef.current?.clientWidth
      if (containerWidth) {
        setLeftWidth(containerWidth * 0.1)
        setRightWidth(containerWidth * 0.5)
      }
    }

    handleResize()
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  // ==================== DRAG HANDLERS ====================
  const startResize = (e: MouseEvent, direction: 'left' | 'right') => {
    e.preventDefault()
    didDrag.current = false

    // Capture initial state
    const startX = e.clientX
    const startLeftWidth = leftWidth
    const startRightWidth = rightWidth
    const containerWidth = containerRef.current?.clientWidth ?? 0

    // Track current widths during drag
    let currentLeftWidth = startLeftWidth
    let currentRightWidth = startRightWidth

    // Initialize virtual drag bar
    setIsDragging(true)
    setDragDirection(direction)

    // Calculate initial virtual bar position (position of the splitter being dragged)
    const initialPosition =
      direction === 'left'
        ? leftCollapsed
          ? 0
          : leftWidth
        : containerWidth - (rightCollapsed ? 0 : rightWidth) - 4 // -4 for splitter width
    setVirtualBarPosition(initialPosition)

    // Set global drag state
    document.body.style.userSelect = 'none'
    document.body.style.cursor = 'col-resize'

    // Mouse move handler - updates virtual bar position
    const onMouseMove = (_moveEvent: Event) => {
      didDrag.current = true
      const moveEvent = _moveEvent as unknown as MouseEvent
      const deltaX = moveEvent.clientX - startX

      if (direction === 'left') {
        currentLeftWidth = Math.min(
          Math.max(100, startLeftWidth + deltaX),
          containerWidth - 100 - rightWidth - 8, // Account for both splitters (4px each)
        )
        setVirtualBarPosition(currentLeftWidth)
      } else {
        currentRightWidth = Math.min(
          Math.max(100, startRightWidth - deltaX),
          containerWidth - 100 - leftWidth - 8, // Account for both splitters (4px each)
        )
        setVirtualBarPosition(containerWidth - currentRightWidth - 4)
      }
    }

    // Mouse up handler - applies final sizes and cleans up
    const onMouseUp = () => {
      // Remove event listeners
      window.removeEventListener('mousemove', onMouseMove)
      window.removeEventListener('mouseup', onMouseUp)

      // Reset global drag state
      document.body.style.userSelect = ''
      document.body.style.cursor = ''

      // Apply final sizes if drag occurred
      if (didDrag.current) {
        if (direction === 'left') {
          setLeftWidth(currentLeftWidth)
        } else {
          setRightWidth(currentRightWidth)
        }
      }

      // Reset virtual drag state
      setIsDragging(false)
      setDragDirection(null)
      setVirtualBarPosition(0)

      // Reset drag flag after current event loop
      setTimeout(() => {
        didDrag.current = false
      }, 0)
    }

    // Attach event listeners
    window.addEventListener('mousemove', onMouseMove)
    window.addEventListener('mouseup', onMouseUp)
  }

  // ==================== COLLAPSE HANDLERS ====================
  const handleCollapseToggle = (side: 'left' | 'right') => {
    if (didDrag.current) return

    if (side === 'left') {
      setLeftCollapsed(c => !c)
    } else {
      setRightCollapsed(c => !c)
    }
  }

  // ==================== RENDER ====================
  return (
    <div ref={containerRef} className="flex flex-1 relative overflow-hidden">
      {/* ===== DRAG OVERLAY ===== */}
      {/* Prevents mouse events from being captured by child elements during drag */}
      {isDragging && (
        <div
          className="absolute inset-0 z-40 cursor-col-resize"
          style={{ backgroundColor: 'transparent' }}
        />
      )}

      {/* ===== VIRTUAL DRAG BAR ===== */}
      {/* Shows preview position during dragging */}
      {isDragging && (
        <div
          className="absolute top-0 bottom-0 bg-blue-500 opacity-50 z-50 pointer-events-none"
          style={{
            left: virtualBarPosition - 1,
            width: 2,
          }}
        />
      )}

      {/* ===== LEFT PANEL ===== */}
      {!leftCollapsed && (
        <div className="h-full" style={{ width: leftWidth }}>
          {left}
        </div>
      )}

      {/* ===== LEFT SPLITTER ===== */}
      <div
        className={`relative cursor-col-resize transition-colors ${
          isDragging && dragDirection === 'left'
            ? 'bg-blue-200'
            : 'hover:bg-border-hover'
        }`}
        style={{ width: 4, zIndex: 10 }}
        onMouseDown={e => {
          if (!leftCollapsed) startResize(e, 'left')
        }}
      >
        <div className="absolute inset-0 bg-[#EEEEEE]" style={{ height: 50 }} />
        <div className="absolute inset-0 bg-border" style={{ top: 50 }} />
        <button
          className="absolute top-1/2 -translate-y-1/2 left-0 w-4 h-6 text-xs text-muted-foreground bg-muted hover:bg-accent border rounded"
          onClick={e => {
            e.stopPropagation()
            e.preventDefault()
            handleCollapseToggle('left')
          }}
        >
          {!leftCollapsed ? '⟨' : '⟩'}
        </button>
      </div>

      {/* ===== CENTER PANEL ===== */}
      <div className="flex-1 h-full overflow-auto bg-background">{center}</div>

      {/* ===== RIGHT SPLITTER ===== */}
      <div
        className={`relative cursor-col-resize transition-colors ${
          isDragging && dragDirection === 'right'
            ? 'bg-blue-200'
            : 'hover:bg-border-hover'
        }`}
        style={{ width: 4, zIndex: 10 }}
        onMouseDown={e => {
          if (!rightCollapsed) startResize(e, 'right')
        }}
      >
        <div className="absolute inset-0 bg-[#EEEEEE]" style={{ height: 50 }} />
        <div className="absolute inset-0 bg-border" style={{ top: 50 }} />
        <button
          className="absolute top-1/2 -translate-y-1/2 right-0 w-4 h-6 text-xs text-muted-foreground bg-muted hover:bg-accent border rounded"
          onClick={e => {
            e.stopPropagation()
            e.preventDefault()
            handleCollapseToggle('right')
          }}
        >
          {!rightCollapsed ? '⟩' : '⟨'}
        </button>
      </div>

      {/* ===== RIGHT PANEL ===== */}
      {!rightCollapsed && (
        <div
          className="h-full bg-muted flex flex-col"
          style={{ width: rightWidth }}
        >
          {right}
        </div>
      )}
    </div>
  )
}
