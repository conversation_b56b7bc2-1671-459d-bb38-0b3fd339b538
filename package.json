{"name": "@tommyjqliu/arxtect-hawking", "version": "0.0.25", "files": ["out"], "scripts": {"dev": "next dev --port=3001", "build": "next build", "start": "next start", "lint": "prettier . --write && next lint --fix", "typecheck": "tsc --noEmit", "test": "vitest", "gen-icons": "tsx scripts/generate-icon.ts", "prepare": "husky"}, "dependencies": {"@eslint/eslintrc": "^3", "@fortawesome/fontawesome-free": "^6.7.2", "@hocuspocus/provider": "^2.15.2", "@hookform/resolvers": "^4.1.3", "@monaco-editor/react": "^4.7.0", "@octokit/core": "^6.1.5", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.1.8", "@radix-ui/react-visually-hidden": "^1.1.2", "@splinetool/react-spline": "^4.0.0", "@splinetool/runtime": "^1.9.82", "@tailwindcss/postcss": "^4", "@tanstack/react-query": "^5.69.0", "@tanstack/react-table": "^8.21.2", "@types/aos": "^3.0.7", "@types/express": "^5.0.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "ahooks": "^3.8.4", "aos": "^2.3.4", "axios": "^1.8.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "eslint": "^9", "eslint-config-next": "15.2.0", "express": "^4.21.2", "husky": "^9.1.7", "immer": "^10.1.1", "jszip": "^3.10.1", "lint-staged": "^15.5.2", "localforage": "^1.10.0", "lucide-react": "^0.477.0", "next": "15.3.2", "next-themes": "^0.4.6", "pdfjs-dist": "^5.2.133", "posthog-js": "^1.257.0", "prettier": "3.5.3", "puppeteer": "^24.0.0", "rc-tree": "^5.13.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "react-toastify": "^11.0.5", "sass": "^1.85.1", "sonner": "^2.0.2", "tailwind-merge": "^3.0.2", "tailwindcss": "^4", "tailwindcss-animate": "^1.0.7", "tsx": "^4.19.3", "typescript": "^5", "uuid": "^11.1.0", "vaul": "^1.1.2", "vitest": "^3.1.1", "y-indexeddb": "^9.0.12", "y-monaco": "^0.1.6", "y-websocket": "^2.1.0", "yjs": "^13.6.24", "zod": "^3.25.17", "zustand": "^5.0.3"}, "overrides": {"ahooks": {"react": "$react", "react-dom": "$react-dom"}}, "publishConfig": {"access": "public"}}