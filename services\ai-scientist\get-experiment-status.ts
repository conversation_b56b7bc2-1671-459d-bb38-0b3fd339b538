import api from '@/services/api'
import { useQuery } from '@tanstack/react-query'
import queryClient from '../query-client'
import { EXPERIMENT_NAMES } from '@/app/(landing)/ai-scientist/constant'

export interface ExperimentStatus {
  job_id: string
  status: string
  running_time_minutes: number
  start_time: string
}

export default async function getExperimentStatus(): Promise<
  ExperimentStatus[]
> {
  const status = await Promise.all(
    EXPERIMENT_NAMES.map(async experiment => {
      const res = await api.get(`/ai-scientist/status/${experiment}`, {
        validateStatus: status => status < 500,
      })
      return res.data
    }),
  )
  return status
}

export function useExperimentStatus() {
  return useQuery(
    {
      queryKey: ['experiment-status'],
      queryFn: () => getExperimentStatus(),
    },
    queryClient,
  )
}

export function invalidateExperimentStatus() {
  queryClient.invalidateQueries({ queryKey: ['experiment-status'] })
}
