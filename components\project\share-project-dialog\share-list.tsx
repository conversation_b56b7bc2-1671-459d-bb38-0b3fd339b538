import { useGetProjectMembers } from '@/services/projects/get-project-members'
import { Button } from '@/components/ui/button'
import { Trash2 } from 'lucide-react'
import removeProjectMember from '@/services/projects/remove-project-member'
import queryClient from '@/services/query-client'
import { useGetProject } from '@/services/projects/get-project'
import { useGetMe } from '@/services/auth/get-me'
import { confirmDialog } from '@/components/global-dialog'

interface ShareListProps {
  projectId: string
}

export default function ShareList({ projectId }: ShareListProps) {
  const { data: members } = useGetProjectMembers(projectId)
  const { data: project } = useGetProject(projectId)
  const { data: user } = useGetMe()

  const handleRemoveMember = async (memberId: string) => {
    try {
      await removeProjectMember(projectId, memberId)
      queryClient.invalidateQueries({
        queryKey: ['project-members', projectId],
      })
    } catch (error) {
      console.error('Failed to remove member:', error)
    }
  }

  const isOwner = user?.id === project?.owner?.id

  return (
    <div className="rounded-md border p-4">
      <h3 className="font-medium mb-2">Project Members</h3>
      <div className="space-y-2">
        {project?.owner && (
          <div
            className="flex items-center justify-between text-sm border-b pb-2"
            id="owner"
          >
            <div>
              <p className="font-medium">{project.owner.name}</p>
              <p className="text-xs text-gray-400">Owner</p>
            </div>
          </div>
        )}
        {members?.length ? (
          members.map(member => (
            <div
              key={member.userId}
              className="flex items-center justify-between text-sm"
            >
              <div>
                <p className="font-medium">{member.user.name}</p>
                <p className="text-gray-500">{member.user.email}</p>
                <p className="text-xs text-gray-400 capitalize">
                  {member.role}
                </p>
              </div>
              {isOwner && (
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={async () => {
                    const confirm = await confirmDialog()
                    if (!confirm) return
                    handleRemoveMember(member.userId)
                  }}
                  className="h-8 w-8"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              )}
            </div>
          ))
        ) : (
          <p className="text-sm text-gray-500">No members yet</p>
        )}
      </div>
    </div>
  )
}
