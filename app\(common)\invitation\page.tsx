'use client'

import { Suspense, useState } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { useGetTokenInfo } from '@/services/projects/get-token-info'
import useRequireLogin from '@/lib/use-require-login'
import acceptProjectInvitation from '@/services/projects/accept-invitation'

function Page() {
  useRequireLogin({
    message: 'You must be logged in to accept project invitations.',
    redirectBack: true,
  })
  const router = useRouter()
  const searchParams = useSearchParams()
  const token = searchParams.get('token')
  const {
    data,
    isLoading: isInfoLoading,
    isError,
  } = useGetTokenInfo(token || '')

  const [isLoading, setIsLoading] = useState(false)

  const handleJoinProject = async () => {
    setIsLoading(true)
    try {
      await acceptProjectInvitation(data!.projectId, token!)
      router.push(`/editor?project-id=${data!.projectId}`)
    } finally {
      setIsLoading(false)
    }
  }

  if (isError) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-100">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Invalid Invitation</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600">
              The invitation link is invalid.
            </p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Project Invitation</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {isInfoLoading ? (
            <p className="text-sm text-gray-600">Loading project details...</p>
          ) : data ? (
            <>
              <p className="text-sm text-gray-600">
                You have been invited to join{' '}
                <span className="font-medium">{data.projectName}</span> as&nbsp;
                <span className="font-medium">{data.role}</span>.
              </p>
              <p className="text-sm text-gray-600">
                Click the button below to accept the invitation and join the
                project.
              </p>
              <Button
                onClick={handleJoinProject}
                disabled={isLoading}
                className="w-full"
              >
                {isLoading ? 'Joining...' : 'Join Project'}
              </Button>
            </>
          ) : (
            <p className="text-sm text-gray-600">
              Unable to load project details. The invitation may be invalid or
              expired.
            </p>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

export default function SuspensePage() {
  return (
    <Suspense>
      <Page />
    </Suspense>
  )
}
