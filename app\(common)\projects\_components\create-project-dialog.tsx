// components/create-project-dialog.tsx
'use client'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import createProject from '@/services/projects/create-project'

const formSchema = z.object({
  projectName: z
    .string()
    .min(1, {
      message: 'Project name is required',
    })
    .max(50, {
      message: 'Project name must not exceed 50 characters',
    }),
})

export function CreateProjectDialog({
  open,
  setOpen,
  refreshProjects,
}: {
  open: boolean
  setOpen: (open: boolean) => void
  refreshProjects: () => void
}) {
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zod<PERSON><PERSON>olver(formSchema),
    defaultValues: {
      projectName: '',
    },
  })

  async function onSubmit(values: z.infer<typeof formSchema>) {
    await createProject(values.projectName)
    refreshProjects()
    setOpen(false)
    form.reset()
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Create New Project</DialogTitle>
          <DialogDescription>
            Enter a name for your new project. Click save when you&apos;re done.
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
            <FormField
              control={form.control}
              name="projectName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Project Name</FormLabel>
                  <FormControl>
                    <Input placeholder="My Awesome Project" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setOpen(false)}
              >
                Cancel
              </Button>
              <Button type="submit">Save</Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
