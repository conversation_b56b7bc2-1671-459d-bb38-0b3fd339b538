import * as fs from 'fs'
import * as path from 'path'

// Configuration
const SOURCE_DIR = './public/assets' // Source directory containing SVGs
const OUTPUT_DIR = 'components/ar-icons' // Output directory for components
const RELATIVE_IMPORT_PATH = '/assets' // Relative path for imports

// Component template
const getComponentTemplate = (name: string, importPath: string) =>
  `import { ComponentProps } from 'react'
import Image from 'next/image'
import { getSize, IconSize } from './icon-sizes'

export default function ${name}(
  props: Partial<ComponentProps<typeof Image>> & {
    size?: IconSize | [number, number]
  },
) {
  const [width, height] = Array.isArray(props.size)
      ? props.size
      : getSize(props.size)
  return (
    <Image
      src="${importPath}"
      alt="${name}"
      width={width}
      height={height}
      {...props}
    />
  )
}
`

// Index export template
const getExportTemplate = (componentName: string, fileName: string) =>
  `export { default as ${componentName} } from '@/${OUTPUT_DIR}/${fileName}'\n`

// Capitalize first letter
function capitalize(str: string): string {
  return str.charAt(0).toUpperCase() + str.slice(1)
}

// Convert filename to component name (e.g., 'left-arrow' -> 'LeftArrow')
function toComponentName(filename: string): string {
  return filename
    .replace('.svg', '')
    .split('-')
    .map(part => capitalize(part))
    .join('')
}

// Recursively find SVG files
function findSvgFiles(dir: string): string[] {
  let results: string[] = []

  const files = fs.readdirSync(dir)

  files.forEach(file => {
    const fullPath = path.join(dir, file)
    const stat = fs.statSync(fullPath)

    if (stat.isDirectory()) {
      results = results.concat(findSvgFiles(fullPath))
    } else if (path.extname(file) === '.svg') {
      results.push(fullPath)
    }
  })

  return results
}
const existedIcon = new Map<string, string>()
// Main function to generate components
function generateComponents() {
  // Create output directory if it doesn't exist
  if (!fs.existsSync(OUTPUT_DIR)) {
    fs.mkdirSync(OUTPUT_DIR, { recursive: true })
  }

  const svgFiles = findSvgFiles(SOURCE_DIR)
  let indexContent = ''

  svgFiles.forEach(svgPath => {
    const relativePath = path.relative(SOURCE_DIR, svgPath)
    const filename = path.basename(svgPath, '.svg')
    const componentName = toComponentName(filename)
    const importPath = `${RELATIVE_IMPORT_PATH}/${relativePath.replace(/\\/g, '/')}`

    // Generate component file
    const componentContent = getComponentTemplate(componentName, importPath)
    const componentPath = path.join(OUTPUT_DIR, `${filename}.tsx`)

    fs.writeFileSync(componentPath, componentContent)
    if (existedIcon.has(componentName)) {
      console.log(
        `Duplicated: ${componentName} in ${svgPath}, ${existedIcon.get(componentName)}`,
      )
    }
    existedIcon.set(componentName, svgPath)
    // Add to index exports
    indexContent += getExportTemplate(componentName, filename)
  })

  // Write index.ts file
  const indexPath = path.join(OUTPUT_DIR, 'index.ts')
  fs.writeFileSync(indexPath, indexContent.trim())
}

// Execute
try {
  generateComponents()
  console.log('Component generation completed successfully!')
} catch (error) {
  console.error('Error generating components:', error)
}
