'use client'
import { login } from '@/services/auth/login'
import { But<PERSON> } from './ui/button'
import { Label } from './ui/label'
import { Popover, PopoverContent, PopoverTrigger } from './ui/popover'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { fetchGetMe } from '@/services/auth/get-me'

const userList = Array.from({ length: 10 }, (_, i) => ({
  name: 'inside_test' + (i + 1),
  email: 'inside_test' + (i + 1) + '@gmail.com',
  password: 'inside_test' + (i + 1) + '@gmail.com',
}))

userList.push({
  name: 'Admin',
  email: '<EMAIL>',
  password: 'test12312312',
})

export default function DevController() {
  return (
    <Popover>
      <PopoverTrigger
        asChild
        className="fixed bottom-[10px] right-[20px] rounded-2xl"
      >
        <Button>Dev</Button>
      </PopoverTrigger>
      <PopoverContent>
        <Label className="pb-2">Switch Account</Label>
        <Select
          onValueChange={value => {
            const user = userList.find(user => user.email === value)
            if (user) {
              login({
                email: user.email,
                password: user.password,
              })
                .then(() => {
                  return fetchGetMe()
                })
                .catch(err => {
                  console.error('Login failed', err)
                })
            }
          }}
        >
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Account" />
          </SelectTrigger>
          <SelectContent>
            {userList.map((user, index) => (
              <SelectItem key={index} value={user.email}>
                {user.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </PopoverContent>
    </Popover>
  )
}
