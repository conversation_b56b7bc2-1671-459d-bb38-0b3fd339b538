import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuTrigger,
} from '@/components/ui/context-menu'
import { useYProjectContext } from '@/lib/y-adapter/use-yproject-context'
import {
  isAssetNode,
  isFolderNode,
  isTextNode,
} from '@/lib/y-project/file-index'
import {
  expandToPath,
  setExplorerStore,
  useExplorerStore,
} from '@/stores/explorer-store'
import Tree from 'rc-tree'
import { useEffect, useMemo } from 'react'
import InputTitle from './input-title'
import {
  Eye,
  EyeClosed,
  File,
  FileDigit,
  FolderClosed,
  FolderOpen,
} from 'lucide-react'
import { useYConfig } from '@/lib/y-adapter/use-yconfig'
import { FolderNodeObject, useYIndex } from '@/lib/y-adapter/use-yindex'
import { DataNode } from 'rc-tree/lib/interface'
import { cn } from '@/lib/utils'

import './explorer-tree.css'
import { toast } from 'sonner'
export interface TreeNode extends DataNode {
  key: string
  filename: string
}

export default function ExplorerTree() {
  const { yproject } = useYProjectContext()
  const [config, setConfig] = useYConfig(yproject)
  const { mainFile } = config
  const index = useYIndex(yproject)
  const expandedKeys = useExplorerStore(state => state.expandedKeys)
  const addingPath = useExplorerStore(state => state.addingPath)
  const renamingPath = useExplorerStore(state => state.renamingPath)
  const rightClickedPath = useExplorerStore(state => state.rightClickedPath)

  const tree = useMemo(() => {
    if (!index) return []
    const dfs = (node: FolderNodeObject, currentPath: string): TreeNode[] => {
      const children: TreeNode[] = []
      for (const [filename, child] of Object.entries(node)) {
        const fullPath = currentPath ? `${currentPath}/${filename}` : filename
        const className = cn({
          outline: rightClickedPath === fullPath,
        })
        const editing = renamingPath === fullPath
        const title = editing ? (
          <InputTitle defaultValue={filename} />
        ) : (
          <div className="flex justify-between items-center">
            <div>{filename}</div>
            {isTextNode(child) &&
              fullPath.endsWith('.tex') &&
              (fullPath === mainFile ? (
                <Eye size={16} />
              ) : (
                <EyeClosed
                  size={16}
                  className="text-gray-500 hover:text-blue-500 cursor-pointer"
                  onClick={e => {
                    e.stopPropagation()
                    setConfig({
                      mainFileId: child.uuid,
                    })
                  }}
                />
              ))}
          </div>
        )

        const isLeaf = isAssetNode(child) || isTextNode(child)

        children.push({
          key: fullPath,
          filename,
          title,
          isLeaf,
          children: !isLeaf ? dfs(child, fullPath) : undefined,
          className,
          icon: isAssetNode(child) ? <FileDigit size={16} /> : undefined,
        })
      }

      children.sort((a, b) => {
        if (a.children && !b.children) {
          return -1
        } else if (!a.children && b.children) {
          return 1
        } else {
          return a.filename.localeCompare(b.filename)
        }
      })

      const [addingNodePath, addingNodeType] = addingPath || []
      if (currentPath === addingNodePath) {
        // If this is the folder where we are adding a new node, mark it
        children.unshift({
          key: '/INTERNAL_NEW_FILE_NODE/', // Use a unique key for the temporary node
          filename: 'temp-newfile',
          isLeaf: addingNodeType === 'file',
          title: <InputTitle />,
        })
      }
      return children
    }

    return dfs(index, '')
  }, [addingPath, index, mainFile, renamingPath, rightClickedPath, setConfig])

  const selectedPath = useExplorerStore(state => state.selectedPath)
  useEffect(() => {
    if (selectedPath) return
    const node = yproject.fileIndex.getNode(mainFile)
    if (!node) return
    expandToPath(mainFile)
    setExplorerStore(state => {
      state.selectedPath = mainFile
      state.selectedNode = node
    })
    // Here listen to index is necessary to ensure
    // when the tree updates, we try to select the main file
  }, [mainFile, selectedPath, yproject.fileIndex, index])

  return (
    <ContextMenu modal={false}>
      <ContextMenuTrigger>
        <Tree
          draggable={node => node.key !== renamingPath}
          treeData={tree}
          expandedKeys={expandedKeys}
          onExpand={keys => useExplorerStore.setState({ expandedKeys: keys })}
          onRightClick={({ event, node }) => {
            event.defaultPrevented = false
            setExplorerStore(state => {
              state.rightClickedPath = node.key
            })
          }}
          selectedKeys={selectedPath ? [selectedPath] : []}
          onSelect={(keys, { node }) => {
            const fileNode = yproject.fileIndex.getNodeStrict(node.key)
            if (!fileNode) return
            setExplorerStore(state => {
              state.selectedNode = fileNode
              state.selectedPath = node.key
            })
          }}
          icon={props => {
            if (props.isLeaf === false) {
              return null
            }
            return <File size={16} />
          }}
          switcherIcon={props => {
            if (props.isLeaf) {
              return null
            }
            if (props.expanded) {
              return <FolderOpen size={16} />
            }
            return <FolderClosed size={16} />
          }}
          dropIndicatorRender={() => null}
          onDrop={async props => {
            const { dragNode, node: dropNode } = props
            const node = yproject.fileIndex.getNodeStrict(dropNode.key)
            const oldPath = dragNode.key
            const targetPath = dropNode.key
              .split('/')
              .slice(0, isFolderNode(node) ? undefined : -1)
              .concat(dragNode.filename)
              .join('/')
            try {
              await yproject.fileHandler.move(oldPath, targetPath)
            } catch (error) {
              toast.error(
                `Failed to move file: ${error instanceof Error ? error.message : 'Unknown error'}`,
              )
            }
          }}
        />
      </ContextMenuTrigger>
      <ContextMenuContent>
        {rightClickedPath?.endsWith('.tex') && (
          <ContextMenuItem
            onClick={() => {
              const node = yproject.fileIndex.getNodeStrict(rightClickedPath)
              if (!isTextNode(node)) return
              setConfig({
                mainFileId: node.uuid,
              })
            }}
          >
            Set as Main File
          </ContextMenuItem>
        )}
        <ContextMenuItem
          onClick={() => {
            if (!rightClickedPath) return
            setExplorerStore(state => {
              state.renamingPath = rightClickedPath
            })
          }}
        >
          Rename
        </ContextMenuItem>
        <ContextMenuItem
          onClick={() => {
            if (!rightClickedPath) return
            yproject.fileHandler.delete(rightClickedPath)
          }}
        >
          Delete
        </ContextMenuItem>
      </ContextMenuContent>
    </ContextMenu>
  )
}
