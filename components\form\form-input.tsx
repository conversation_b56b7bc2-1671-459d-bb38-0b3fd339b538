// components/ui/form-input.tsx
import React from 'react'
import {
  UseFormRegister,
  FieldErrors,
  Path,
  FieldValues,
} from 'react-hook-form'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { cn } from '@/lib/utils'

interface FormInputProps<T extends FieldValues> {
  label: string
  name: Path<T>
  type?: string
  placeholder?: string
  register: UseFormRegister<T>
  errors: FieldErrors<T>
  icon?: React.ReactNode
  required?: boolean
  className?: string
}

export function FormInput<T extends FieldValues>({
  label,
  name,
  type = 'text',
  placeholder,
  register,
  errors,
  icon,
  required = false,
  className,
}: FormInputProps<T>) {
  return (
    <div className={cn('mb-4', className)}>
      <Label
        htmlFor={name}
        className="block text-sm font-medium mb-1 text-gray-700"
      >
        {label} {required && <span className="text-red-500">*</span>}
      </Label>
      <div className="relative">
        {icon && (
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none text-gray-500">
            {icon}
          </div>
        )}
        <Input
          id={name}
          type={type}
          placeholder={placeholder}
          {...register(name)}
          className={cn(
            'w-full rounded border py-2 px-3 text-gray-700',
            icon && 'pl-10',
            errors[name] && 'border-red-500 focus-visible:ring-red-500',
          )}
        />
      </div>
      {errors[name] && (
        <p className="mt-1 text-xs text-red-500">
          {errors[name]?.message as string}
        </p>
      )}
    </div>
  )
}
