import { OutputFileEntry } from '@/lib/y-project/file-index'
import { promises as fs } from 'fs'
import path from 'path'

export function readExampleProject(name: string) {
  const folderPath = path.join(__dirname, '../example-projects', name)
  return readWorkspaceFromFolder(folderPath)
}

/**
 * Reads a directory and returns a list of file information objects
 * @param folderPath - Absolute path to the folder to read
 * @param folderPath - Optional base path for calculating relative paths (defaults to folderPath)
 * @returns Promise<Array<FileInfo>> - Array of objects containing relative paths and file information
 */
export async function readWorkspaceFromFolder(folderPath: string) {
  try {
    await fs.access(folderPath)
    const entries: OutputFileEntry[] = []
    const files = await fs.readdir(folderPath, { withFileTypes: true })
    for (const file of files) {
      const absolutePath = path.join(folderPath, file.name)
      const relativePath = path.relative(folderPath, absolutePath)

      if (file.isDirectory()) {
        const subFolderFiles = await readWorkspaceFromFolder(folderPath)
        entries.push(...subFolderFiles)
      } else if (file.isFile()) {
        const buffer = await fs.readFile(absolutePath)
        entries.push({
          path: relativePath,
          buffer: buffer.buffer,
        })
      }
    }

    return entries
  } catch (error) {
    console.error(`Error reading folder ${folderPath}:`, error)
    throw error
  }
}
