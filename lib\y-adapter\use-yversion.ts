import { useEffect, useState } from 'react'
import { ProjectVersion } from '@/lib/y-project/version-handler'
import { YProject } from '../y-project'

export default function useYVersions(yproject: YProject) {
  const versionHandler = yproject.versionHandler
  const [versions, setVersions] = useState<ProjectVersion[]>([])
  useEffect(() => {
    const observe = () => {
      const versions = versionHandler.versions.toJSON().reverse()
      setVersions(versions)
    }
    observe()
    versionHandler.versions.observe(observe)
    return () => {
      versionHandler.versions.unobserve(observe)
    }
  }, [versionHandler])
  return versions
}
