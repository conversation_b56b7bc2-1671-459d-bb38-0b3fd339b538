import { useQuery } from '@tanstack/react-query'
import api from '../api'
import queryClient from '../query-client'

interface Result {
  collaboratorShareToken: string
  viewerShareToken: string
}

export default async function getTokens(projectId: string): Promise<Result> {
  const response = await api.get(`/projects/${projectId}/share-tokens`)
  return response.data
}

export function useGetTokens(projectId: string) {
  return useQuery(
    {
      queryKey: ['project', projectId, 'tokens'],
      queryFn: () => getTokens(projectId),
    },
    queryClient,
  )
}
