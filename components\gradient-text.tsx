import React from 'react'
import { cn } from '@/lib/utils'

interface GradientTextProps {
  children: React.ReactNode
  className?: string
  gradient?: string
}

export function GradientText({
  children,
  className,
  gradient = 'linear-gradient(90deg, #33b4ff 0%, #ffae00 100%)',
}: GradientTextProps) {
  return (
    <span
      className={cn('gradient-text', className)}
      style={{
        background: gradient,
        WebkitTextFillColor: 'transparent',
        WebkitBackgroundClip: 'text',
        backgroundClip: 'text',
      }}
    >
      {children}
    </span>
  )
}
