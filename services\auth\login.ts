import api from '../api'
import { z } from 'zod'
import { AuthResponse } from './type'

export const loginSchema = z.object({
  email: z.string().min(1, 'Email is required'),
  password: z
    .string()
    .min(8, 'Password must be at least 8 characters')
    .max(32, 'Password cannot exceed 32 characters'),
})

export type LoginFormValues = z.infer<typeof loginSchema>

export async function login(data: LoginFormValues) {
  return api.post<AuthResponse>('/auth/login', data)
}
