import { FileNode, LogEntry, ParseResult } from './types'

// Define constants
const LOG_WRAP_LIMIT: number = 79
const LATEX_WARNING_REGEX: RegExp = /^LaTeX(?:3| Font)? Warning: (.*)$/
const HBOX_WARNING_REGEX: RegExp = /^(Over|Under)full \\(v|h)box/
const PACKAGE_WARNING_REGEX: RegExp =
  /^((?:Package|Class|Module) \b.+\b Warning:.*)$/
const LINES_REGEX: RegExp = /lines? ([0-9]+)/
const PACKAGE_REGEX: RegExp = /^(?:Package|Class|Module) (\b.+\b) Warning/
const FILE_LINE_ERROR_REGEX: RegExp = /^([./].*):(\d+): (.*)/

// Define enums and interfaces
enum State {
  NORMAL = 0,
  ERROR = 1,
}

export interface ParserOptions {
  fileBaseNames?: RegExp[]
  ignoreDuplicates?: boolean
}

class LogText {
  private text: string
  private lines: string[]
  private row: number

  constructor(text: string) {
    this.text = text.replace(/(\r\n)|\r/g, '\n')
    const wrappedLines: string[] = this.text.split('\n')
    this.lines = [wrappedLines[0]]

    for (let i = 1; i < wrappedLines.length; i++) {
      const prevLine: string = wrappedLines[i - 1]
      const currentLine: string = wrappedLines[i]

      if (
        prevLine.length === LOG_WRAP_LIMIT &&
        prevLine.slice(-3) !== '...' &&
        currentLine.charAt(0) !== '!'
      ) {
        this.lines[this.lines.length - 1] += currentLine
      } else {
        this.lines.push(currentLine)
      }
    }
    this.row = 0
  }

  nextLine(): string | false {
    this.row++
    if (this.row >= this.lines.length) {
      return false
    }
    return this.lines[this.row]
  }

  rewindLine(): void {
    this.row--
  }

  linesUpToNextWhitespaceLine(stopAtError: boolean = false): string[] {
    return this.linesUpToNextMatchingLine(/^ *$/, stopAtError)
  }

  linesUpToNextMatchingLine(
    match: RegExp,
    stopAtError: boolean = false,
  ): string[] {
    const lines: string[] = []

    while (true) {
      const nextLine: string | false = this.nextLine()

      if (nextLine === false) {
        break
      }

      if (stopAtError && nextLine.match(/^! /)) {
        this.rewindLine()
        break
      }

      lines.push(nextLine)

      if (nextLine.match(match)) {
        break
      }
    }

    return lines
  }
}

export default class LatexParser {
  private state: State
  private fileBaseNames: RegExp[]
  private ignoreDuplicates: boolean
  private data: LogEntry[]
  private fileStack: FileNode[]
  private currentFileList: FileNode[]
  private rootFileList: FileNode[]
  private openParens: number
  private latexWarningRegex: RegExp
  private packageWarningRegex: RegExp
  private packageRegex: RegExp
  private log: LogText
  private currentLine: string | false
  private currentError: Partial<LogEntry> | null
  private currentFilePath: string

  constructor(text: string, options: ParserOptions = {}) {
    this.state = State.NORMAL
    this.fileBaseNames = options.fileBaseNames || [/compiles/, /\/usr\/local/]
    this.ignoreDuplicates = !!options.ignoreDuplicates
    this.data = []
    this.fileStack = []
    this.currentFileList = this.rootFileList = []
    this.openParens = 0
    this.latexWarningRegex = LATEX_WARNING_REGEX
    this.packageWarningRegex = PACKAGE_WARNING_REGEX
    this.packageRegex = PACKAGE_REGEX
    this.log = new LogText(text)
    this.currentLine = ''
    this.currentError = null
    this.currentFilePath = ''
  }

  parse(): ParseResult {
    while ((this.currentLine = this.log.nextLine()) !== false) {
      if (this.state === State.NORMAL) {
        if (this.currentLineIsError()) {
          this.state = State.ERROR
          this.currentError = {
            line: undefined,
            file: this.currentFilePath,
            level: 'error',
            message: (this.currentLine as string).slice(2),
            content: '',
            raw: (this.currentLine as string) + '\n',
          }
        } else if (this.currentLineIsFileLineError()) {
          this.state = State.ERROR
          this.parseFileLineError()
        } else if (this.currentLineIsRunawayArgument()) {
          this.parseRunawayArgumentError()
        } else if (this.currentLineIsWarning()) {
          this.parseSingleWarningLine(this.latexWarningRegex)
        } else if (this.currentLineIsHboxWarning()) {
          this.parseHboxLine()
        } else if (this.currentLineIsPackageWarning()) {
          this.parseMultipleWarningLine()
        } else {
          this.parseParensForFilenames()
        }
      }
      if (this.state === State.ERROR && this.currentError) {
        this.currentError.content =
          (this.currentError.content || '') +
          this.log.linesUpToNextMatchingLine(/^l\.[0-9]+/).join('\n') +
          '\n' +
          this.log.linesUpToNextWhitespaceLine(true).join('\n') +
          '\n' +
          this.log.linesUpToNextWhitespaceLine(true).join('\n')
        this.currentError.raw =
          (this.currentError.raw || '') + (this.currentError.content || '')
        const lineNo = this.currentError.raw.match(/l\.([0-9]+)/)
        if (lineNo && this.currentError.line === null) {
          this.currentError.line = String(parseInt(lineNo[1], 10))
        }
        this.data.push(this.currentError as LogEntry)
        this.state = State.NORMAL
        this.currentError = null
      }
    }
    return this.postProcess(this.data)
  }

  private currentLineIsError(): boolean {
    return (
      typeof this.currentLine === 'string' &&
      this.currentLine[0] === '!' &&
      this.currentLine !==
        '!  ==> Fatal error occurred, no output PDF file produced!'
    )
  }

  private currentLineIsFileLineError(): boolean {
    return (
      typeof this.currentLine === 'string' &&
      FILE_LINE_ERROR_REGEX.test(this.currentLine)
    )
  }

  private currentLineIsRunawayArgument(): boolean {
    return (
      typeof this.currentLine === 'string' &&
      !!this.currentLine.match(/^Runaway argument/)
    )
  }

  private currentLineIsWarning(): boolean {
    return (
      typeof this.currentLine === 'string' &&
      !!this.currentLine.match(this.latexWarningRegex)
    )
  }

  private currentLineIsPackageWarning(): boolean {
    return (
      typeof this.currentLine === 'string' &&
      !!this.currentLine.match(this.packageWarningRegex)
    )
  }

  private currentLineIsHboxWarning(): boolean {
    return (
      typeof this.currentLine === 'string' &&
      !!this.currentLine.match(HBOX_WARNING_REGEX)
    )
  }

  private parseFileLineError(): void {
    if (typeof this.currentLine !== 'string') return
    const result: RegExpMatchArray | null = this.currentLine.match(
      FILE_LINE_ERROR_REGEX,
    )
    if (!result) return
    this.currentError = {
      line: String(parseInt(result[2], 10)),
      file: result[1],
      level: 'error',
      message: result[3],
      content: '',
      raw: this.currentLine + '\n',
    }
  }

  private parseRunawayArgumentError(): void {
    if (typeof this.currentLine !== 'string') return
    this.currentError = {
      line: undefined,
      file: this.currentFilePath,
      level: 'error',
      message: this.currentLine,
      content: '',
      raw: this.currentLine + '\n',
    }
    this.currentError.content =
      this.log.linesUpToNextWhitespaceLine().join('\n') +
      '\n' +
      this.log.linesUpToNextWhitespaceLine().join('\n')
    this.currentError.raw += this.currentError.content
    const lineNo: RegExpMatchArray | null | undefined =
      this.currentError.raw?.match(/l\.([0-9]+)/)
    if (lineNo) {
      this.currentError.line = String(parseInt(lineNo[1], 10))
    }
    this.data.push(this.currentError as LogEntry)
    this.currentError = null
  }

  private parseSingleWarningLine(prefixRegex: RegExp): void {
    if (typeof this.currentLine !== 'string') return
    const warningMatch: RegExpMatchArray | null =
      this.currentLine.match(prefixRegex)
    if (!warningMatch) return
    const warning: string = warningMatch[1]
    const lineMatch: RegExpMatchArray | null = warning.match(LINES_REGEX)
    const line = lineMatch ? String(parseInt(lineMatch[1], 10)) : undefined
    this.data.push({
      line,
      file: this.currentFilePath,
      level: 'warning',
      message: warning,
      raw: warning,
    })
  }

  private parseMultipleWarningLine(): void {
    if (typeof this.currentLine !== 'string') return
    let warningMatch: RegExpMatchArray | null = this.currentLine.match(
      this.packageWarningRegex,
    )
    if (!warningMatch) return
    const warningLines: string[] = [warningMatch[1]]
    let lineMatch: RegExpMatchArray | null = this.currentLine.match(LINES_REGEX)
    let line = lineMatch ? String(parseInt(lineMatch[1], 10)) : undefined
    const packageMatch: RegExpMatchArray | null = this.currentLine.match(
      this.packageRegex,
    )
    if (!packageMatch) return
    const packageName: string = packageMatch[1]
    const prefixRegex: RegExp = new RegExp(
      `(?:\\(${packageName}\\))*[\\s]*(.*)`,
      'i',
    )

    while ((this.currentLine = this.log.nextLine())) {
      lineMatch =
        typeof this.currentLine === 'string'
          ? this.currentLine.match(LINES_REGEX)
          : null
      line = lineMatch ? String(parseInt(lineMatch[1], 10)) : line
      warningMatch =
        typeof this.currentLine === 'string'
          ? this.currentLine.match(prefixRegex)
          : null
      if (warningMatch && warningMatch[1]) {
        warningLines.push(warningMatch[1])
      } else {
        break
      }
    }
    const rawMessage: string = warningLines.join(' ')
    this.data.push({
      line,
      file: this.currentFilePath,
      level: 'warning',
      message: rawMessage,
      raw: rawMessage,
    })
  }

  private parseHboxLine(): void {
    if (typeof this.currentLine !== 'string') return
    const lineMatch: RegExpMatchArray | null =
      this.currentLine.match(LINES_REGEX)
    const line = lineMatch ? String(parseInt(lineMatch[1], 10)) : undefined
    this.data.push({
      line,
      file: this.currentFilePath,
      level: 'typesetting',
      message: this.currentLine,
      raw: this.currentLine,
    })
  }

  private parseParensForFilenames(): void {
    if (typeof this.currentLine !== 'string') return
    const pos: number = this.currentLine.search(/[()]/)
    if (pos === -1) return
    const token: string = this.currentLine[pos]
    this.currentLine = this.currentLine.slice(pos + 1)
    if (token === '(') {
      const filePath: string | false = this.consumeFilePath()
      if (filePath) {
        this.currentFilePath = filePath
        const newFile: FileNode = {
          path: filePath,
          files: [],
        }
        this.fileStack.push(newFile)
        this.currentFileList.push(newFile)
        this.currentFileList = newFile.files
      } else {
        this.openParens++
      }
    } else if (token === ')') {
      if (this.openParens > 0) {
        this.openParens--
      } else if (this.fileStack.length > 1) {
        this.fileStack.pop()
        const previousFile: FileNode = this.fileStack[this.fileStack.length - 1]
        this.currentFilePath = previousFile.path
        this.currentFileList = previousFile.files
      }
    }
    this.parseParensForFilenames()
  }

  private consumeFilePath(): string | false {
    if (typeof this.currentLine !== 'string') return false
    if (!this.currentLine.match(/^(\/?([^ ()\\]+\/)*[^ ()\\]+)/)) {
      return false
    }

    let endOfFilePath: number = this.currentLine.search(/[ ()\\]/)
    while (endOfFilePath !== -1 && this.currentLine[endOfFilePath] === ' ') {
      const partialPath: string = this.currentLine.slice(0, endOfFilePath)
      if (/\.\w+$/.test(partialPath)) {
        break
      }
      const remainingPath: string = this.currentLine.slice(endOfFilePath + 1)
      if (/^\s*["()[\]]/.test(remainingPath)) {
        break
      }
      const nextEndOfPath: number = remainingPath.search(/[ "()[\]]/)
      if (nextEndOfPath === -1) {
        endOfFilePath = -1
      } else {
        endOfFilePath += nextEndOfPath + 1
      }
    }
    let path: string
    if (endOfFilePath === -1) {
      path = this.currentLine
      this.currentLine = ''
    } else {
      path = this.currentLine.slice(0, endOfFilePath)
      this.currentLine = this.currentLine.slice(endOfFilePath)
    }
    return path
  }

  private postProcess(data: LogEntry[]): ParseResult {
    const all: LogEntry[] = []
    const errorsByLevel: { [key: string]: LogEntry[] } = {
      error: [],
      warning: [],
      typesetting: [],
    }
    const hashes: Set<string> = new Set()

    const hashEntry = (entry: LogEntry) => entry.raw

    data.forEach((item: LogEntry) => {
      const hash = hashEntry(item)
      if (!hash) {
        return
      }
      if (this.ignoreDuplicates && hashes.has(hash)) {
        return
      }

      errorsByLevel[item.level]?.push(item)
      all.push(item)
      hashes.add(hash)
    })

    return {
      errors: errorsByLevel.error,
      warnings: errorsByLevel.warning,
      typesetting: errorsByLevel.typesetting,
      all,
      files: this.rootFileList,
    }
  }
}
