import { But<PERSON> } from '@/components/ui/button'
import React, { useState, useEffect, useRef, useCallback } from 'react'
import { Tooltip } from '@/components/ui/tooltip'
import { Download, ChevronDownIcon, CheckIcon } from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
} from '@/components/ui/dropdown-menu'
import { Log } from '@/components/ar-icons'
import parseLog from '@/lib/compile/parse-log'
import FormattedCompilerLog from './formatted-compile-log'
import { LogEntry } from '@/lib/compile/parse-log/types'
import { useYConfig } from '@/lib/y-adapter/use-yconfig'
import { useYProjectContext } from '@/lib/y-adapter/use-yproject-context'
import { getFirstPageImage } from '@/lib/pdf'
import postPreview from '@/services/projects/post-preview'
import { dataURLtoBlob } from '@/lib/utils'
import { PDF_CACHE_NAME, retrieveFile, storeFile } from '@/lib/file-cache'
import { YProject } from '@/lib/y-project'

export default function PDFViewer() {
  const { yproject, version: currentVersion } = useYProjectContext()
  const [config, setConfig] = useYConfig(yproject)
  const [compiling, setCompiling] = useState(false)
  const [showCompilerLog, setShowCompilerLog] = useState(false)
  const [compileLog, setCompileLog] = useState('')
  const [compileMessage, setCompileMessage] = useState<LogEntry[]>([])
  const initialCompileDone = useRef(false)
  const [cachedPdfUrl, setCachedPdfUrl] = useState('')
  const [pdfUrl, setPdfUrl] = useState('')
  const fileName = 'main.pdf'

  const downloadPDF = () => {
    if (pdfUrl && fileName) {
      const link = document.createElement('a')
      link.href = pdfUrl
      link.download = fileName
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
  }

  const [logInfo, setLogInfo] = useState<{
    errorsLength: number
    warningsLength: number
    typesettingLength: number
  }>({
    errorsLength: 0,
    warningsLength: 0,
    typesettingLength: 0,
  })
  const messageCount = logInfo.errorsLength + logInfo.warningsLength
  const badgeColor = logInfo.errorsLength ? 'destructive' : 'secondary'

  const handleCompile = useCallback(async () => {
    let currentCompiling = compiling
    setCompiling(compiling => {
      currentCompiling = compiling
      return true
    })
    if (currentCompiling) return // Prevent multiple compilations
    try {
      const res = await YProject.compileYProject(
        yproject.projectId,
        currentVersion,
      )
      const { errors, warnings, typesetting } = parseLog(res.log)
      setLogInfo({
        errorsLength: errors.length,
        warningsLength: warnings.length,
        typesettingLength: typesetting.length,
      })
      setCompileLog(res.log)
      setCompileMessage([...errors, ...warnings, ...typesetting])
      if (res.pdf) {
        const pdfblob = new Blob([res.pdf], {
          type: 'application/pdf',
        })
        storeFile(
          yproject.projectId,
          await pdfblob.arrayBuffer(),
          PDF_CACHE_NAME,
        )
        const objectURL = URL.createObjectURL(pdfblob)
        if (pdfUrl) {
          URL.revokeObjectURL(pdfUrl)
        }
        setPdfUrl(objectURL)
        const imageURL = await getFirstPageImage(pdfblob, {
          scale: 0.5,
        })
        postPreview(yproject.projectId, dataURLtoBlob(imageURL))
      }
      return true
    } catch (error) {
      console.error('Error compiling project:', error)
      return false
    } finally {
      setCompiling(false)
    }
  }, [compiling, yproject.projectId, currentVersion, pdfUrl])

  useEffect(() => {
    ;(async () => {
      if (yproject?.projectId && !initialCompileDone.current) {
        // TODO: cache PDF based on input hash
        const cachedPDF = await retrieveFile(yproject.projectId, PDF_CACHE_NAME)
        if (cachedPDF) {
          const objectURL = URL.createObjectURL(
            new Blob([cachedPDF], { type: 'application/pdf' }),
          )
          setCachedPdfUrl(objectURL)
        } else {
          // Only compile if there's no cached PDF
          handleCompile()
        }
        initialCompileDone.current = true
      }
    })()
    return () => {
      setCachedPdfUrl(prevUrl => {
        if (prevUrl) {
          URL.revokeObjectURL(prevUrl)
        }
        return ''
      })
    }
  }, [yproject, handleCompile])

  // Cleanup URL object when component unmounts or new file is uploaded
  useEffect(() => {
    return () => {
      if (pdfUrl) {
        URL.revokeObjectURL(pdfUrl)
      }
    }
  }, [pdfUrl])

  return (
    <div className="flex-1 flex flex-col">
      {/* Toolbar */}
      <div className="flex items-center bg-[#EEEEEE] p-2 gap-1">
        <div className="flex items-center">
          <div className="flex items-center">
            <Button
              loading={compiling}
              onClick={handleCompile}
              size="sm"
              className="bg-orange-default hover:bg-orange-hover text-white font-medium rounded-r-none"
              disabled={compiling}
              tag-id="compile-document"
            >
              {compiling ? 'Compiling' : 'Compile'}
            </Button>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 rounded-l-none  bg-orange-default hover:bg-orange-hover text-white"
                  disabled={compiling}
                  tag-id="engine-dropdown"
                >
                  <ChevronDownIcon className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem
                  onClick={() => setConfig({ engineType: 'pdftex' })}
                  tag-id="change-engine-pdftex"
                >
                  <div className="flex items-center justify-between w-full">
                    <span>PDFTex</span>
                    {config.engineType === 'pdftex' && (
                      <CheckIcon className="h-4 w-4 ml-2" />
                    )}
                  </div>
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => setConfig({ engineType: 'xetex' })}
                  tag-id="change-engine-xetex"
                >
                  <div className="flex items-center justify-between w-full">
                    <span>XeTex</span>
                    {config.engineType === 'xetex' && (
                      <CheckIcon className="h-4 w-4 ml-2" />
                    )}
                  </div>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        <Tooltip content="Compile Log">
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 hover:bg-gray-200"
            onClick={() => setShowCompilerLog(!showCompilerLog)}
            tag-id="view-compile-log"
          >
            <Badge
              variant={badgeColor}
              content={String(!showCompilerLog ? messageCount : 0)}
            >
              <Log className="h-6 w-6 text-gray-800" />
            </Badge>
          </Button>
        </Tooltip>

        <Tooltip content="Download PDF">
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 hover:bg-gray-200"
            onClick={downloadPDF}
            tag-id="download-pdf"
          >
            <Download className="h-6 w-6 text-gray-800" />
          </Button>
        </Tooltip>
      </div>

      {/* PDF Viewer and Compiler Log */}
      <div className="flex-1 relative">
        {/* PDF Viewer */}
        {(pdfUrl || cachedPdfUrl) && (
          <div className="h-full border border-gray-200 rounded-lg shadow-md bg-background">
            <object
              data={pdfUrl || cachedPdfUrl}
              type="application/pdf"
              className="w-full rounded-md h-full"
            >
              <p className="text-gray-600 text-center p-4">
                Your browser does not support PDF rendering. Please{' '}
                <a
                  href={pdfUrl}
                  download={fileName}
                  className="text-blue-600 hover:underline"
                >
                  download the PDF
                </a>{' '}
                to view it.
              </p>
            </object>
          </div>
        )}

        {/* Compiler Log Overlay */}
        {showCompilerLog && (
          <div className="absolute top-0 left-0 w-full h-full bg-white border border-gray-200 rounded-lg shadow-lg p-6 overflow-y-auto z-10">
            <FormattedCompilerLog messages={compileMessage} log={compileLog} />
          </div>
        )}
      </div>
    </div>
  )
}
