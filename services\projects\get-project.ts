import { useQuery, UseQueryOptions } from '@tanstack/react-query'
import api from '../api'
import queryClient from '@/services/query-client'
import { User } from '../types'

export interface GetProjectResponse {
  projectName: string
  owner: User
  published: boolean
}

export default async function getProject(projectId: string) {
  const res = await api.get(`/projects/${projectId}`)
  return res.data as GetProjectResponse
}

export function useGetProject(
  projectId: string,
  options?: Partial<UseQueryOptions<GetProjectResponse>>,
) {
  return useQuery(
    {
      queryKey: ['project', projectId],
      queryFn: () => getProject(projectId),
      ...options,
    },
    queryClient,
  )
}
