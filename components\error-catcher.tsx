'use client'

import { AxiosError } from 'axios'
import { useEffect } from 'react'
import { toast } from 'sonner'

export default function ErrorCatcher() {
  useEffect(() => {
    const handleError = (event: ErrorEvent) => {
      const errorMessage = event.message || 'Unknown error occurred'
      toast.error(
        <div>
          <strong>An unexpected error occurred.</strong>
          <p>Please contact support.</p>
          <pre>{errorMessage}</pre>
        </div>,
      )

      console.error('Caught error:', {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        error: event.error,
      })

      // Optionally send to a logging service
      // logErrorToService(event)
    }

    const handleUnhandledRejection = async (event: PromiseRejectionEvent) => {
      event.preventDefault()
      const reason = event.reason

      if (reason instanceof AxiosError) {
        const res = reason.response

        if (!res) {
          toast.error('Network error. Please check your connection.')
          console.error('Axios network error:', {
            message: reason.message,
            reason: reason,
          })
          return
        }

        let errorMessage: string | undefined

        try {
          const data = res.data

          if (data instanceof Blob) {
            const text = await data.text()
            const parsed = JSON.parse(text)
            errorMessage = parsed.message ?? parsed.error
          } else if (typeof data === 'object' && data !== null) {
            const { message, error } = data as {
              message?: string
              error?: string
            }
            errorMessage = message ?? error
          }
        } catch (e) {
          console.warn('Failed to parse Axios error response:', e)
          errorMessage = 'Unable to parse server response'
        }

        console.error('Axios error response:', {
          error: reason,
        })

        toast.error(
          `API Error: ${res.status} - ${errorMessage ?? 'Unknown server error'}`,
        )
        return
      }

      // Handle other unhandled rejections with more descriptive message
      const reasonMessage =
        reason?.message || reason?.toString() || 'Unknown rejection'

      console.error('Unhandled rejection:', {
        reason,
        promise: event.promise,
      })

      toast.error(
        <div>
          <strong>An unexpected error occurred.</strong>
          <p>Please contact support.</p>
          <pre>{reasonMessage}</pre>
        </div>,
      )
    }

    window.addEventListener('error', handleError)
    window.addEventListener('unhandledrejection', handleUnhandledRejection)

    return () => {
      window.removeEventListener('error', handleError)
      window.removeEventListener('unhandledrejection', handleUnhandledRejection)
    }
  }, [])

  return null
}
