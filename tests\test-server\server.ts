import express, { Express, Request, Response } from 'express'
import path from 'path'

export function launchServer() {
  const app: Express = express()
  app.use(express.json())
  const port: number = 9000
  app.get('/', (req: Request, res: Response) => {
    res.sendFile(path.join(__dirname, 'index.html'))
  })
  // Latex engine files
  app.use('/', express.static(path.join(__dirname, '../..', 'public')))
  app.listen(port)
  return app
}
