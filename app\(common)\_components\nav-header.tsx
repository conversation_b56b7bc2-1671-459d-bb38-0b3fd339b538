'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { cn } from '@/lib/utils'
import { UserMenu } from './user-menu'
import logo from '@/public/assets/logo-icon.svg'
import { usePathname } from 'next/navigation'
import { PROJECT_CODE } from '@/lib/constant'

export const NavHeader = () => {
  const [isTop, setIsTop] = useState(true)
  const pathname = usePathname()

  useEffect(() => {
    const isHome = pathname === '/' || pathname === '/home'
    window.sessionStorage.setItem('isHomePage', String(isHome))
    const event = new CustomEvent('homePageStatusChange', {
      detail: { isHome },
    })
    window.dispatchEvent(event)
  }, [pathname])

  useEffect(() => {
    const scrollHandler = () => {
      setIsTop(window.scrollY <= 10)
    }
    window.addEventListener('scroll', scrollHandler)
    return () => window.removeEventListener('scroll', scrollHandler)
  }, [])

  return (
    <nav
      className={cn(
        'transition-shadow duration-300',
        isTop ? 'bg-white' : 'bg-white shadow-lg',
      )}
    >
      <div className="mx-auto px-4 py-3 flex items-center justify-between">
        <Link href="/home" className="flex items-center">
          <Image src={logo} alt="Logo" className="h-9 w-auto" />
          <h1
            className="font-bold"
            style={{
              backgroundImage:
                'linear-gradient(90deg, #33b4ff 0%, #ffae00 100%)',
              overflow: 'visible',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              backgroundClip: 'text',
              // textFillColor: 'transparent', // TODO: fix
              fontWeight: '600',
              fontFamily: 'public sans',
              textOverflow: 'clip',
              whiteSpace: 'nowrap',
              marginLeft: '10px',
              fontSize: '1.5rem',
            }}
          >
            {PROJECT_CODE}
          </h1>
        </Link>

        <div className="hidden lg:flex items-center space-x-6">
          <UserMenu variant="desktop" />
        </div>
        <div className="lg:hidden flex items-center">
          <UserMenu variant="mobile" />
        </div>
      </div>
    </nav>
  )
}
