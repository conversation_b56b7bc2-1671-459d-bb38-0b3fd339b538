import { useQuery, UseQueryOptions } from '@tanstack/react-query'
import api from '../api'
import queryClient from '@/services/query-client'

export interface GetProjectAccessResponse {
  readOnly: boolean
  published: boolean
}

export default async function getProjectAccess(projectId: string) {
  const res = await api.get(`/projects/${projectId}/access`)
  return res.data as GetProjectAccessResponse
}

export function useGetProjectAccess(
  projectId: string,
  options?: Partial<UseQueryOptions<GetProjectAccessResponse>>,
) {
  return useQuery(
    {
      queryKey: ['project', projectId, 'access'],
      queryFn: () => getProjectAccess(projectId),
      ...options,
    },
    queryClient,
  )
}
