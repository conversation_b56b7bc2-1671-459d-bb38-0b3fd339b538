import React, {
  useState,
  useRef,
  useEffect,
  cloneElement,
  useImperativeHandle,
  forwardRef,
  ReactElement,
} from 'react'
import * as DropdownMenu from '@radix-ui/react-dropdown-menu'
import { CheckIcon } from '@radix-ui/react-icons'
import Image from 'next/image'

interface MenuItemBase {
  key?: string
  label: string
  separator?: boolean
  icon?: string
  disabled?: boolean
  className?: string
}

interface MenuItemWithSelect extends MenuItemBase {
  onSelect?: () => void
  subMenu?: never
  type?: never
}

interface MenuItemWithSubMenu extends MenuItemBase {
  subMenu: MenuItem[]
  onSelect?: never
  type?: string
}

interface RadioMenuItem extends MenuItemBase {
  type: 'radio'
  value: string
  subMenu: Array<{
    value: string
    label: string
  }>
  onSelect?: (value: string) => void
}

interface CheckboxMenuItem extends MenuItemBase {
  type: 'checkbox'
  checked?: boolean
  onCheckedChange?: (checked: boolean) => void
  subMenu?: never
}

type MenuItem =
  | MenuItemWithSelect
  | MenuItemWithSubMenu
  | RadioMenuItem
  | CheckboxMenuItem

interface ArMenuRadixProps {
  items: MenuItem[]
  title?: string
  buttonClassName?: string
  isNeedIcon?: boolean
  align?: 'start' | 'center' | 'end'
  menuAlign?: 'start' | 'center' | 'end'
  buttonCom?: ReactElement<{
    forwardedRef?: React.Ref<HTMLButtonElement>
    className?: string
  }>
  getButtonClass?: (open: boolean) => string
  width?: string | number
}

export interface ArMenuRadixHandle {
  open: boolean
  setOpen: (open: boolean) => void
}

const ArMenuRadix = forwardRef<ArMenuRadixHandle, ArMenuRadixProps>(
  (
    {
      items,
      title,
      buttonClassName = '',
      isNeedIcon = true,
      align = 'center',
      menuAlign = 'end',
      buttonCom = null,
      getButtonClass = null,
      width,
    },
    ref,
  ) => {
    const [open, setOpen] = useState(false)
    const [buttonWidth, setButtonWidth] = useState<number | null>(null)
    const buttonRef = useRef<HTMLButtonElement>(null)

    useEffect(() => {
      if (buttonRef.current) {
        setButtonWidth(buttonRef.current.offsetWidth)
      }
    }, [])

    const getItems = (
      item: MenuItem,
      parentItem?: MenuItem,
    ): React.ReactNode => {
      const type = parentItem ? parentItem.type : item.type

      switch (type) {
        case 'radio':
          const radioItem = item as RadioMenuItem
          return (
            <DropdownMenu.RadioGroup
              key={item.key || item.label}
              value={radioItem.value}
              onValueChange={radioItem.onSelect}
            >
              {radioItem.subMenu.map((option, idx) => (
                <DropdownMenu.RadioItem
                  key={idx}
                  className={`flex items-center gap-6 px-6 py-1 hover:bg-gray-100 cursor-pointer outline-none ${
                    align === 'center' ? 'text-center' : 'text-left'
                  }`}
                  value={option.value}
                >
                  <DropdownMenu.ItemIndicator className="absolute left-0 w-[25px] inline-flex items-center justify-center">
                    <CheckIcon />
                  </DropdownMenu.ItemIndicator>
                  {option.label}
                </DropdownMenu.RadioItem>
              ))}
            </DropdownMenu.RadioGroup>
          )
        case 'checkbox':
          const checkboxItem = item as CheckboxMenuItem
          return (
            <DropdownMenu.CheckboxItem
              key={item.key || item.label}
              className={`flex items-center gap-4 px-4 py-2 hover:bg-gray-100 cursor-pointer outline-none ${
                align === 'center' ? 'text-center' : 'text-left'
              }`}
              checked={checkboxItem.checked}
              onCheckedChange={checkboxItem.onCheckedChange}
            >
              <CheckIcon />
              {item.label}
            </DropdownMenu.CheckboxItem>
          )
        default:
          const defaultItem = item as MenuItemWithSelect
          return (
            <DropdownMenu.Item
              key={item.key || item.label}
              className={`flex items-center gap-4 px-4 py-2 hover:bg-gray-100 cursor-pointer outline-none ${
                align === 'center' ? 'text-center' : 'text-left'
              }`}
              onSelect={defaultItem.onSelect}
              disabled={item.disabled}
            >
              {item.icon && (
                <Image
                  src={item.icon}
                  alt="Menu icon"
                  width={20}
                  height={20}
                  className="cursor-pointer"
                />
              )}
              {item.label}
            </DropdownMenu.Item>
          )
      }
    }

    const renderMenuItems = (
      items: MenuItem[],
      parentItem?: MenuItem,
    ): React.ReactNode => {
      return items.map(item => {
        return (
          <React.Fragment key={item.key || item.label}>
            {!item.subMenu ? (
              getItems(item, parentItem)
            ) : (
              <div
                className={`px-4 pt-2 text-gray-500 cursor-default flex justify-between items-center ${
                  align === 'center' ? 'text-center' : 'text-left'
                }`}
              >
                {item.label}
              </div>
            )}
            {item.separator && (
              <DropdownMenu.Separator className="h-px bg-gray-200 my-2" />
            )}
            {item.subMenu &&
              item?.type !== 'radio' &&
              renderMenuItems(item.subMenu, item)}
            {item?.type === 'radio' && getItems(item)}
          </React.Fragment>
        )
      })
    }

    useImperativeHandle(ref, () => ({
      open,
      setOpen,
    }))

    return (
      <div className="relative">
        <DropdownMenu.Root onOpenChange={isOpen => setOpen(isOpen)}>
          <DropdownMenu.Trigger asChild>
            {!buttonCom ? (
              <button
                ref={buttonRef}
                className={`flex items-center outline-none ${
                  isNeedIcon ? 'justify-between' : 'justify-center'
                } w-full ${buttonClassName}`}
              >
                {title}
                {isNeedIcon && (
                  <span className="ml-2">
                    {open ? (
                      <svg
                        className="w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M5 15l7-7 7 7"
                        />
                      </svg>
                    ) : (
                      <svg
                        className="w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M19 9l-7 7-7-7"
                        />
                      </svg>
                    )}
                  </span>
                )}
              </button>
            ) : (
              cloneElement(buttonCom, {
                forwardedRef: buttonRef,
                className: `${buttonCom.props?.className || ''} ${
                  getButtonClass ? getButtonClass(open) : ''
                }`,
              })
            )}
          </DropdownMenu.Trigger>

          <DropdownMenu.Content
            className="bg-white shadow-md rounded-md py-2 z-50 DropdownMenuContent"
            align={menuAlign || 'end'}
            sideOffset={5}
            style={{
              minWidth: buttonWidth || undefined,
              width: width || 'auto',
            }}
          >
            {renderMenuItems(items)}
          </DropdownMenu.Content>
        </DropdownMenu.Root>
      </div>
    )
  },
)

ArMenuRadix.displayName = 'ArMenuRadix'

export default React.memo(ArMenuRadix)
