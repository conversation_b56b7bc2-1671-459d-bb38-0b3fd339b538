import createProject from '@/services/projects/create-project'
import { YProject } from '@/lib/y-project'
import JSZip from 'jszip'
import { toast } from 'sonner'

export interface importZipProjectConfig {
  unwrapFirstFolder?: boolean
}

const defaultConfig: importZipProjectConfig = {
  unwrapFirstFolder: true,
}

const ignoreFolders = new Set(['__MACOSX'])

export default async function importZipProject(
  zip: File,
  config: importZipProjectConfig = defaultConfig,
) {
  const { unwrapFirstFolder } = config
  const contents = await JSZip.loadAsync(zip)
  const filePaths = Object.keys(contents.files).filter(
    path =>
      !contents.files[path].dir &&
      ignoreFolders.has(path.split('/')[0]) === false,
  )

  // Check if there's a single top-level folder and unwrap if selected
  const rootFolders = new Set(
    filePaths.map(path => (path.includes('/') ? path.split('/')[0] : '')),
  )
  const shouldUnwrap =
    unwrapFirstFolder && rootFolders.size === 1 && !rootFolders.has('')
  const fileEntries = await Promise.all(
    filePaths.map(async path => ({
      path: shouldUnwrap ? path.split('/').slice(1).join('/') : path,
      file: await contents.files[path].async('blob'),
    })),
  )

  const projectName = zip.name.replace(/\.zip$/, '')
  const { id } = await createProject(projectName)
  const projectId = id
  const yproject = new YProject({ projectId, readonly: false })
  const results = await yproject.fileIndex.importFiles(fileEntries)
  if (results.length > 0) {
    toast.error(
      `Failed to upload ${results.length} files:\n ${results.join('\n')}`,
    )
  } else {
    toast.success('Files imported successfully!')
  }
  yproject.destroy()
  return projectId
}
