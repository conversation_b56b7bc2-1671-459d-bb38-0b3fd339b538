import { useState } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  DialogTitle,
} from '@/components/ui/dialog'
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Mail, Link as LinkIcon, Users } from 'lucide-react'
import ShareLink from './share-link'
import ShareEmail from './share-email'
import ShareList from './share-list'

interface ShareLinkPopupProps {
  projectId: string
  isOpen: boolean
  onClose: () => void
}

export default function ShareProjectDialog({
  projectId,
  isOpen,
  onClose,
}: ShareLinkPopupProps) {
  const [activeTab, setActiveTab] = useState('link')

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle>Share Project</DialogTitle>
        </DialogHeader>
        <div className="flex-1 overflow-y-auto flex flex-col">
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="w-full"
          >
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger
                value="link"
                className="flex items-center gap-2"
                tag-id="share-project-link"
              >
                <LinkIcon className="w-4 h-4" />
                Share Link
              </TabsTrigger>
              <TabsTrigger
                value="email"
                className="flex items-center gap-2"
                tag-id="share-project-email"
              >
                <Mail className="w-4 h-4" />
                Share via Email
              </TabsTrigger>
              <TabsTrigger value="members" className="flex items-center gap-2">
                <Users className="w-4 h-4" />
                Project Members
              </TabsTrigger>
            </TabsList>

            <TabsContent value="link" className="mt-4">
              <ShareLink projectId={projectId} />
            </TabsContent>

            <TabsContent value="email" className="mt-4">
              <ShareEmail projectId={projectId} />
            </TabsContent>

            <TabsContent value="members" className="mt-4">
              <ShareList projectId={projectId} />
            </TabsContent>
          </Tabs>
        </div>
      </DialogContent>
    </Dialog>
  )
}

export function ShareLinkButton({
  projectId,
  children,
}: {
  projectId: string
  children: React.ReactNode
}) {
  const [isOpen, setIsOpen] = useState(false)

  return (
    <>
      <div onClick={() => setIsOpen(true)}>{children}</div>
      <ShareProjectDialog
        projectId={projectId}
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
      />
    </>
  )
}
