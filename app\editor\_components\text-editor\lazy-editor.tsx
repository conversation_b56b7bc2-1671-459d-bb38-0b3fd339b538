import latexMonarch from './latex.monarch.json'
import bibtexMonarch from './bibtex.monarch.json'
import Editor from '@monaco-editor/react'
import type { editor as monacoEditor } from 'monaco-editor'
import { useState } from 'react'
import { useExplorerStore } from '@/stores/explorer-store'
import { isTextNode } from '@/lib/y-project/file-index'
import { useBindYDoc } from './use-bind-ydoc'
import { loader } from '@monaco-editor/react'
import { useYProjectContext } from '@/lib/y-adapter/use-yproject-context'
import useAwareness from '@/lib/y-adapter/use-awareness'

loader.init().then(monaco => {
  const { languages } = monaco
  languages.register({ id: 'latex' })
  languages.register({ id: 'bibtex' })
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  languages.setMonarchTokensProvider('latex', latexMonarch as any)
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  languages.setMonarchTokensProvider('bibtex', bibtexMonarch as any)
})

function obtainLanguage(path: string): string {
  if (path.endsWith('.bib')) {
    return 'bibtex'
  } else if (path.endsWith('.tex')) {
    return 'latex'
  }
  return 'plaintext'
}

export default function LazyEditor() {
  const { yproject } = useYProjectContext()
  const selectedPath = useExplorerStore(state => state.selectedPath)
  const selectedNode = useExplorerStore(state => state.selectedNode)
  const textId =
    selectedNode && isTextNode(selectedNode) ? selectedNode.uuid : undefined

  const [editor, setEditor] = useState<monacoEditor.IStandaloneCodeEditor>()
  useBindYDoc(yproject, textId, editor)
  const userList = useAwareness(yproject)
  const language = obtainLanguage(selectedPath || '')

  return (
    <>
      <Editor
        onMount={editor => {
          setEditor(editor)
        }}
        language={language}
        options={{
          wordWrap: 'on',
        }}
      />
      <>
        {userList.map(user => (
          <style key={user.key}>
            {`
              .yRemoteSelection-${user.key} {
                background-color: ${user.color};
                opacity: 0.5;
              }

              .yRemoteSelectionHead-${user.key} {
                position: absolute;
                border-left: solid 2px;
                border-top: solid 2px;
                border-bottom: solid 2px;
                border-color: ${user.color};
                color: ${user.color};
                height: 100%;
                box-sizing: border-box;
              }

              .yRemoteSelectionHead-${user.key}::after {
                position: absolute;
                content: ' ';
                border-color: ${user.color};
                color: ${user.color};
                border: 3px solid;
                border-radius: 4px;
                left: -4px;
                top: -5px;
              }

              .yRemoteSelectionHead-${user.key}:hover::before {
                position: absolute;
                content: '${user.name}';
                background-color: ${user.color};
                color: white;
                font-size: small;
                padding: 0 2px;
                top: -2px;
              }
            `}
          </style>
        ))}
      </>
    </>
  )
}
