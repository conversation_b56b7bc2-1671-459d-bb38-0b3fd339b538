// app/(auth)/login/page.tsx
'use client'

import React, { Suspense, useEffect, useState } from 'react'
import { useForm, FormProvider } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import Link from 'next/link'
import { useRouter, useSearchParams } from 'next/navigation'
import { toast } from 'sonner'
import { FormInput } from '@/components/form/form-input'
import { Button } from '@/components/ui/button'
import { Mail, Lock } from 'lucide-react'
import { login, LoginFormValues, loginSchema } from '@/services/auth/login'
import styles from './login.module.css'
import loginButton from '@/public/assets/website/login_button.png'
import { PROJECT_CODE } from '@/lib/constant'
import { fetchGetMe } from '@/services/auth/get-me'

function Page() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [isSubmitting, setIsSubmitting] = useState(false)

  const methods = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  })

  const {
    handleSubmit,
    register,
    formState: { errors },
  } = methods

  const onSubmit = async (data: LoginFormValues) => {
    setIsSubmitting(true)
    try {
      await login(data)
      await fetchGetMe()
      toast.success('Login successful!')
      router.push(
        decodeURIComponent(searchParams.get('redirect') || '') || '/projects',
      )
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <main className={styles.loginContainer}>
      <div className={styles.loginFormContainer}>
        <h1 className={styles.loginTitle}>Join Us Today</h1>
        <FormProvider {...methods}>
          <form
            noValidate
            autoComplete="off"
            className={styles.loginForm}
            onSubmit={handleSubmit(onSubmit)}
          >
            <FormInput
              label="Email"
              type="email"
              name="email"
              placeholder="<EMAIL>"
              register={register}
              errors={errors}
              icon={<Mail className="h-5 w-5" />}
              required
            />
            <FormInput
              label="Password"
              type="password"
              name="password"
              placeholder="Password"
              register={register}
              errors={errors}
              icon={<Lock className="h-5 w-5" />}
              required
            />
            <div className={styles.loginFormFooter}>
              <Button
                type="submit"
                className={styles.loginButton}
                disabled={isSubmitting}
                style={{ backgroundImage: `url(${loginButton.src})` }}
                tag-id="login-button"
              >
                {isSubmitting ? 'Logging in...' : 'Log in'}
              </Button>
            </div>
            <div className={styles.loginFormFooter}>
              <div className="text-center flex flex-col">
                Need an account?{' '}
                <Link href="/register" className={styles.loginLink}>
                  Register
                </Link>
              </div>
              <Link href="/forgot-password" className={styles.loginLink}>
                Forgot Password?
              </Link>
            </div>
          </form>
        </FormProvider>
      </div>
    </main>
  )
}

function Redirect() {
  const router = useRouter()
  useEffect(() => {
    router.push('/projects')
  }, [router])
  return null
}

export default function SuspensePage() {
  return (
    <Suspense>{PROJECT_CODE === 'ArXtect' ? <Page /> : <Redirect />}</Suspense>
  )
}
