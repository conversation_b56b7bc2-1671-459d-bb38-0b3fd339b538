import { Button } from '@/components/ui/button'
import {
  <PERSON>er,
  Drawer<PERSON>ontent,
  DrawerD<PERSON><PERSON>,
  <PERSON>er<PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DrawerTrigger,
} from '@/components/ui/drawer'
import { ReactNode } from 'react'
import { VersionList } from './version-list'
import { useYProjectContext } from '@/lib/y-adapter/use-yproject-context'
import { Plus, History } from 'lucide-react'
import { toast } from 'sonner'

interface VersionDrawerProps {
  trigger: ReactNode
}

export default function VersionDrawer({
  trigger: children,
}: VersionDrawerProps) {
  const { yproject } = useYProjectContext()

  return (
    <Drawer direction="right">
      <DrawerTrigger asChild>{children}</DrawerTrigger>
      <DrawerContent className="h-full w-[400px] fixed right-0 top-0">
        <DrawerHeader className="border-b">
          <div className="flex items-center gap-2">
            <History className="h-5 w-5 text-muted-foreground" />
            <DrawerTitle>Version History</DrawerTitle>
          </div>
          <DrawerDescription>
            Manage and switch between different versions of your project
          </DrawerDescription>
        </DrawerHeader>

        <div className="h-0 flex-1 flex flex-col">
          <div className="p-4 border-b">
            <Button
              onClick={async () => {
                const added = await yproject.versionHandler.addVersion()
                if (!added) {
                  toast.info('No changes detected. Version not created.')
                }
              }}
              className="w-full"
              size="sm"
            >
              <Plus className="h-4 w-4 mr-2" />
              Create New Version
            </Button>
          </div>

          <VersionList />
        </div>

        <DrawerFooter className="border-t">
          <p className="text-xs text-muted-foreground text-center">
            Versions are automatically saved with timestamps
          </p>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  )
}
