import { clsx, type ClassValue } from 'clsx'
import { twMerge } from 'tailwind-merge'
import { createHash } from 'crypto'

export const mockProjectId = 'mock-project-id'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export async function fileToUint8Array(file: Blob) {
  return new Uint8Array(await file.arrayBuffer())
}

export async function sha256(input: Buffer | Uint8Array) {
  if (crypto?.subtle) {
    const hash = await crypto.subtle.digest('SHA-256', input)
    return Array.from(new Uint8Array(hash))
      .map(b => b.toString(16).padStart(2, '0'))
      .join('')
  }
  if (typeof createHash === 'function') {
    return createHash('sha256').update(input).digest('hex')
  }
  throw new Error('SHA-256 unsupported')
}

export function removeFirstFolder(path: string) {
  const parts = path.split('/')
  return parts.slice(1).join('/')
}

export function clientOnly(node: React.ReactNode) {
  if (typeof window !== 'undefined') {
    return node
  }
  return null
}

export function getFoldersToCreate(filePaths: string[]): string[] {
  const folderSet = new Set<string>()

  filePaths.forEach(path => {
    const segments = path.split('/')
    let currentPath = ''
    // Skip the last segment (the file name)
    for (let i = 0; i < segments.length - 1; i++) {
      if (segments[i] === '') continue
      currentPath = currentPath ? `${currentPath}/${segments[i]}` : segments[i]
      folderSet.add(currentPath)
    }
  })

  const folders = Array.from(folderSet)

  // Sort by length first (shorter paths are parents), then alphabetically
  folders.sort((a, b) => {
    if (a.length !== b.length) {
      return a.length - b.length
    }
    return a.localeCompare(b)
  })

  return folders
}

export function dataURLtoBlob(dataURL: string) {
  const [header, base64] = dataURL.split(',')
  const mime = header.match(/:(.*?);/)?.[1]
  const binary = atob(base64)
  const len = binary.length
  const uint8Array = new Uint8Array(len)

  for (let i = 0; i < len; i++) {
    uint8Array[i] = binary.charCodeAt(i)
  }

  return new Blob([uint8Array], { type: mime })
}

export function requireEnvVar(name: string): string {
  const value = process.env[name]
  if (!value) {
    throw new Error(`Environment variable ${name} is required`)
  }
  return value
}
