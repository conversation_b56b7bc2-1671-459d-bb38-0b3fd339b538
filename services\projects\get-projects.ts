import { useQuery } from '@tanstack/react-query'
import api from '../api'
import queryClient from '../query-client'

export type Project = {
  id: string
  projectName: string
  ownerId: string
  status: 'pending' | 'processing' | 'success' | 'failed'
  email: string
  updatedAt: string
  owner: {
    id: string
    name: string
  }
  previewUrl?: string
}

export async function getProjects(relation: string = 'all') {
  const res = await api.get(`/projects?relation=${relation}`)
  return res.data
}

export function useGetProjects(relation?: string) {
  return useQuery(
    {
      queryKey: ['projects', relation],
      queryFn: () => getProjects(relation),
    },
    queryClient,
  )
}

export function invalidateGetProjects() {
  return queryClient.invalidateQueries({
    queryKey: ['projects'],
  })
}
