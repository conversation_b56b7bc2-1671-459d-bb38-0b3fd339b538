'use client'

import { useState, useCallback } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  <PERSON>alogTitle,
  DialogFooter,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { ScrollAreaFlex } from '@/components/ui/scroll-area'
import {
  UploadFile,
  UploadFolder,
  File as FileIcon,
} from '@/components/ar-icons'
import { X, FileText, Image, Archive } from 'lucide-react'
import { cn } from '@/lib/utils'
import { FileEntry } from './types'

interface ImportFilesDialogProps {
  open: boolean
  setOpen: (open: boolean) => void
  onConfirm: (files: FileEntry[]) => void
}

interface InternalFileEntry extends FileEntry {
  selected: boolean
}

function getFileIcon(fileName: string) {
  const extension = fileName.split('.').pop()?.toLowerCase()

  if (!extension) return <FileIcon size="sm" />

  const textExtensions = [
    'txt',
    'tex',
    'md',
    'py',
    'js',
    'ts',
    'jsx',
    'tsx',
    'html',
    'css',
    'scss',
    'json',
    'xml',
    'yaml',
    'yml',
  ]
  const imageExtensions = [
    'jpg',
    'jpeg',
    'png',
    'gif',
    'svg',
    'webp',
    'bmp',
    'ico',
  ]
  const archiveExtensions = ['zip', 'rar', '7z', 'tar', 'gz']

  if (textExtensions.includes(extension)) {
    return <FileText size={16} className="text-blue-500" />
  }
  if (imageExtensions.includes(extension)) {
    // Image Icon from lucide-react does not have an alt text, so we disable the rule
    // eslint-disable-next-line jsx-a11y/alt-text
    return <Image size={16} className="text-green-500" />
  }
  if (archiveExtensions.includes(extension)) {
    return <Archive size={16} className="text-orange-500" />
  }

  return <FileIcon size="sm" />
}

function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

export default function ImportFilesDialog({
  open,
  setOpen,
  onConfirm,
}: ImportFilesDialogProps) {
  const [isDragging, setIsDragging] = useState(false)
  const [files, setFiles] = useState<InternalFileEntry[]>([])
  const [selectAll, setSelectAll] = useState(true)

  const processFiles = useCallback((fileList: File[]) => {
    const newFiles: InternalFileEntry[] = fileList.map(file => {
      const relativePath = file.webkitRelativePath || file.name
      return {
        file,
        path: file.name,
        relativePath,
        selected: true,
      }
    })
    setFiles(newFiles)
    setSelectAll(true)
  }, [])

  const triggerFileUpload = useCallback(() => {
    const input = document.createElement('input')
    input.type = 'file'
    input.multiple = true

    input.onchange = (event: Event) => {
      const target = event.target as HTMLInputElement
      if (target.files && target.files.length > 0) {
        processFiles(Array.from(target.files))
      }
      document.body.removeChild(input)
    }

    input.oncancel = () => {
      document.body.removeChild(input)
    }

    input.style.display = 'none'
    document.body.appendChild(input)
    input.click()
  }, [processFiles])

  const triggerFolderUpload = useCallback(() => {
    const input = document.createElement('input')
    input.type = 'file'
    input.setAttribute('webkitdirectory', '')
    input.setAttribute('directory', '')

    input.onchange = (event: Event) => {
      const target = event.target as HTMLInputElement
      if (target.files && target.files.length > 0) {
        processFiles(Array.from(target.files))
      }
      document.body.removeChild(input)
    }

    input.oncancel = () => {
      document.body.removeChild(input)
    }

    input.style.display = 'none'
    document.body.appendChild(input)
    input.click()
  }, [processFiles])

  const onDrop = useCallback(
    (e: React.DragEvent<HTMLDivElement>) => {
      e.preventDefault()
      setIsDragging(false)

      const droppedFiles = Array.from(e.dataTransfer.files)
      if (droppedFiles.length > 0) {
        processFiles(droppedFiles)
      }
    },
    [processFiles],
  )

  const onDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    setIsDragging(true)
  }, [])

  const onDragLeave = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    setIsDragging(false)
  }, [])

  const handleSelectAll = useCallback((checked: boolean) => {
    setSelectAll(checked)
    setFiles(prev => prev.map(file => ({ ...file, selected: checked })))
  }, [])

  const handleFileSelect = useCallback((index: number, checked: boolean) => {
    setFiles(prev => {
      const newFiles = [...prev]
      newFiles[index] = { ...newFiles[index], selected: checked }

      // Update selectAll state
      const allSelected = newFiles.every(f => f.selected)
      setSelectAll(allSelected)

      return newFiles
    })
  }, [])

  const removeFile = useCallback((index: number) => {
    setFiles(prev => {
      const newFiles = prev.filter((_, i) => i !== index)
      if (newFiles.length === 0) {
        setSelectAll(true)
      }
      return newFiles
    })
  }, [])

  const handleConfirm = useCallback(() => {
    const selectedFiles = files.filter(file => file.selected)
    onConfirm(selectedFiles)
    setFiles([])
    setOpen(false)
  }, [files, onConfirm, setOpen])

  const handleCancel = useCallback(() => {
    setFiles([])
    setOpen(false)
  }, [setOpen])

  const selectedCount = files.filter(f => f.selected).length
  const totalSize = files
    .filter(f => f.selected)
    .reduce((acc, f) => acc + f.file.size, 0)

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent
        className="sm:max-w-[700px] max-h-[85vh] flex flex-col"
        tag-id="import-files-dialog"
      >
        <DialogHeader>
          <DialogTitle>Import Files</DialogTitle>
        </DialogHeader>

        <div className="flex flex-col gap-4 flex-1 min-h-0 overflow-hidden">
          {files.length === 0 ? (
            <div
              onDrop={onDrop}
              onDragOver={onDragOver}
              onDragLeave={onDragLeave}
              className={cn(
                'border-2 border-dashed rounded-lg p-8 text-center transition-colors',
                isDragging
                  ? 'border-primary bg-primary/10'
                  : 'border-gray-300 hover:border-gray-400',
              )}
              tag-id="import-drag-drop"
            >
              <div className="flex flex-col items-center gap-4">
                <div className="text-4xl text-gray-400">📁</div>
                <div>
                  <h3 className="text-lg font-medium mb-2">
                    Choose files or drag them here
                  </h3>
                  <p className="text-sm text-gray-500 mb-4">
                    Select individual files or entire folders
                  </p>
                </div>

                <div className="flex gap-3">
                  <Button
                    variant="outline"
                    onClick={triggerFileUpload}
                    className="flex items-center gap-2"
                    tag-id="import-upload-files"
                  >
                    <UploadFile size="sm" />
                    Upload Files
                  </Button>
                  <Button
                    variant="outline"
                    onClick={triggerFolderUpload}
                    className="flex items-center gap-2"
                    tag-id="import-upload-folder"
                  >
                    <UploadFolder size="sm" />
                    Upload Folder
                  </Button>
                </div>
              </div>
            </div>
          ) : (
            <div className="space-y-4 flex-1 min-h-0 flex flex-col overflow-hidden">
              <div className="flex items-center justify-between flex-shrink-0">
                <div className="flex items-center gap-2">
                  <Checkbox
                    checked={selectAll}
                    onCheckedChange={handleSelectAll}
                    tag-id="import-select-all"
                  />
                  <span className="text-sm font-medium">
                    Select All ({selectedCount} of {files.length} selected)
                  </span>
                </div>
                <div className="text-sm text-gray-500">
                  Total size: {formatFileSize(totalSize)}
                </div>
              </div>

              <ScrollAreaFlex className="min-h-0 flex-1 border rounded-md">
                <div className="p-3 space-y-2">
                  {files.map((fileEntry, index) => (
                    <div
                      key={index}
                      className="flex items-center gap-3 p-2 rounded hover:bg-gray-50 group"
                    >
                      <Checkbox
                        checked={fileEntry.selected}
                        onCheckedChange={checked =>
                          handleFileSelect(index, !!checked)
                        }
                      />

                      <div className="flex items-center gap-2 flex-1 min-w-0">
                        {getFileIcon(fileEntry.file.name)}
                        <div className="flex-1 min-w-0">
                          <div className="text-sm font-medium truncate">
                            {fileEntry.file.name}
                          </div>
                          {fileEntry.relativePath !== fileEntry.file.name && (
                            <div className="text-xs text-gray-500 truncate">
                              {fileEntry.relativePath}
                            </div>
                          )}
                        </div>
                      </div>

                      <div className="text-xs text-gray-500 whitespace-nowrap">
                        {formatFileSize(fileEntry.file.size)}
                      </div>

                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity"
                        onClick={() => removeFile(index)}
                        tag-id="import-remove-file"
                      >
                        <X size={12} />
                      </Button>
                    </div>
                  ))}
                </div>
              </ScrollAreaFlex>

              <div className="flex gap-2 flex-shrink-0">
                <Button
                  variant="outline"
                  onClick={triggerFileUpload}
                  className="flex items-center gap-2"
                  tag-id="import-add-more-files"
                >
                  <UploadFile size="sm" />
                  Add More Files
                </Button>
                <Button
                  variant="outline"
                  onClick={triggerFolderUpload}
                  className="flex items-center gap-2"
                  tag-id="import-add-more-folder"
                >
                  <UploadFolder size="sm" />
                  Add Folder
                </Button>
              </div>
            </div>
          )}
        </div>

        <DialogFooter className="flex-shrink-0">
          <Button
            variant="outline"
            onClick={handleCancel}
            tag-id="import-cancel"
          >
            Cancel
          </Button>
          <Button
            onClick={handleConfirm}
            disabled={selectedCount === 0}
            tag-id="import-confirm"
          >
            Import{' '}
            {selectedCount > 0 &&
              `${selectedCount} file${selectedCount !== 1 ? 's' : ''}`}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
