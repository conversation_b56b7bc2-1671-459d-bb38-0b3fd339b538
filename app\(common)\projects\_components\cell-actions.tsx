import { Tooltip } from '@/components/ui/tooltip'
import {
  DownloadPdf,
  DownloadProject,
  Copy,
  Delete,
  Share,
} from '@/components/ar-icons'
import { ShareLinkButton } from '@/components/project/share-project-dialog'
import {
  invalidateGetProjects,
  Project,
} from '@/services/projects/get-projects'
import exportZip from '@/lib/y-adapter/export-zip'
import { Row } from '@tanstack/react-table'
import { downloadPdf } from '@/lib/pdf'
import { toast } from 'sonner'
import { confirmDialog } from '@/components/global-dialog'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import createProject from '@/services/projects/create-project'
import { YProject } from '@/lib/y-project'
import * as Y from 'yjs'
import { deleteProject } from '@/services/projects/delete-project'
import { buttonVariants } from '@/components/ui/button'

type ProjectActionProps = {
  project: Project
  className?: string
}

export function ProjectActions({
  project,
  className = 'flex items-center space-x-2 w-max',
}: ProjectActionProps) {

  return (
    <div className={className}>
      <Tooltip content="Download Project">
        <DownloadProject
          className="text-black w-4 h-4 cursor-pointer"
          tag-id="download-project-button"
          onClick={() => {
            exportZip(project.id, project.projectName)
          }}
        />
      </Tooltip>
      <Tooltip content="Copy Project">
        <Copy
          className="text-black w-4 h-4 cursor-pointer"
          tag-id="duplicate-project-button"
          onClick={async () => {
            const confirm = await confirmDialog(resolve => (
              <AlertDialog>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>
                      Are you sure to copy {project.projectName}?
                    </AlertDialogTitle>
                    <AlertDialogDescription>
                      This action cannot be undone.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel
                      tag-id="duplicate-project-cancel"
                      onClick={() => resolve(false)}
                    >
                      Cancel
                    </AlertDialogCancel>
                    <AlertDialogAction
                      tag-id="duplicate-project-accomplish"
                      onClick={() => resolve(true)}
                    >
                      Continue
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            ))
            if (!confirm) return
            const originalYProject = new YProject({
              projectId: project.id,
            })
            const { id } = await createProject(project.projectName + ' Copy')
            const newYProject = new YProject({
              projectId: id,
              readonly: false,
            })

            await originalYProject.initialSynced
            const update = Y.encodeStateAsUpdate(originalYProject.ydoc)
            Y.applyUpdate(newYProject.ydoc, update)
            await invalidateGetProjects()
            toast.success(`Project ${project.projectName} copied successfully!`)
            originalYProject.destroy()
            newYProject.destroy()
          }}
        />
      </Tooltip>
      <Tooltip content="Download Pdf">
        <DownloadPdf
          className="text-black w-4 h-4 cursor-pointer"
          tag-id="download-pdf-button"
          onClick={async () => {
            // TODO: refactor the process with pdf compile in editor output
            const toastId = toast.loading('Generating PDF, please wait...', {
              duration: Infinity,
            })
            const res = await YProject.compileYProject(project.id)
            if (res.pdf) {
              downloadPdf(res.pdf, `${project.projectName}.pdf`)
              toast.success('PDF generated successfully!', {
                id: toastId,
                duration: 5000,
              })
            } else {
              toast.error(
                'Failed to generate PDF. Please check the project for errors.',
                {
                  id: toastId,
                  duration: 5000,
                },
              )
            }
          }}
        />
      </Tooltip>
      <Tooltip content="Share">
        <ShareLinkButton projectId={project.id}>
          <Share className="text-black w-4 h-4 cursor-pointer"
            tag-id="share-project-button"
          />
        </ShareLinkButton>
      </Tooltip>
      <Tooltip content="Delete">
        <Delete
          className="text-black w-4 h-4 cursor-pointer"
          tag-id="delete-project-button"
          onClick={async () => {
            const confirm = await confirmDialog(resolve => (
              <AlertDialog>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>
                      Are you sure to delete {project.projectName}?
                    </AlertDialogTitle>
                    <AlertDialogDescription>
                      This action cannot be undone.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel
                      tag-id="delete-project-cancel"
                      onClick={() => resolve(false)}
                    >
                      Cancel
                    </AlertDialogCancel>
                    <AlertDialogAction
                      tag-id="delete-project-accomplish"
                      onClick={() => resolve(true)}
                      className={buttonVariants({ variant: 'destructive' })}
                    >
                      Continue
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            ))
            if (!confirm) return
            await deleteProject(project.id)
            await invalidateGetProjects()
            toast.success(`Project ${project.projectName} deleted!`)
          }}
        />
      </Tooltip>
    </div>
  )
}

export function CellActions({ row }: { row: Row<Project> }) {
  return <ProjectActions project={row.original} />
}
