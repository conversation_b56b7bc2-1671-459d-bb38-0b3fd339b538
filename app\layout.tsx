import type { Metadata } from 'next'
import { <PERSON>eist, Geist_Mono } from 'next/font/google'
import './globals.css'
import RegisterServiceWorker from '@/components/service-worker'
import DevController from '@/components/dev-login'
import { Toaster } from '@/components/ui/sonner'
import ErrorCatcher from '@/components/error-catcher'
import { PROJECT_CODE } from '@/lib/constant'
import { GlobalDialog } from '@/components/global-dialog'
import AutoLogin from '@/components/auto-login'
import { PHProvider } from '@/lib/posthog'
import PostHogPageViewTracking from '@/components/user-tracking/posthog-page-view-tracking'

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
})

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
})

export const metadata: Metadata = {
  title: PROJECT_CODE,
  description: 'Generated by create next app',
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <PHProvider>
          <PostHogPageViewTracking />
          <main>{children}</main>
          <Toaster richColors />
          <GlobalDialog />
          <ErrorCatcher />
          <RegisterServiceWorker />
          {PROJECT_CODE === 'HyperSheet' && <AutoLogin />}
          {process.env.NODE_ENV === 'development' && <DevController />}
        </PHProvider>
      </body>
    </html>
  )
}
