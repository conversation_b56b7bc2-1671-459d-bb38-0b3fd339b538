import ArMenuRadix from './menu-radix'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useGetMe } from '@/services/auth/get-me'
import { logoutUser } from '@/services/auth/logout'

const UserMenu = () => {
  const router = useRouter()
  const { data: user, refetch } = useGetMe()
  const handleLogout = async () => {
    await logoutUser()
    await refetch()
    router.push('/')
  }

  return (
    <div>
      {user ? (
        <div className="lg:ml-8 px-4">
          <ArMenuRadix
            buttonClassName={
              'bg-arxTheme hover:bg-arx-theme-hover text-[1.1rem] text-white font-bold py-2 shadow-xl rounded-[0.4rem] border border-arxTheme px-4 py-2 rounded-md'
            }
            title={'Account'}
            items={[
              {
                label: user.email,
                // onSelect: () => console.log('Email Selected'),
                separator: true,
              },
              {
                label: 'Account Settings',
                // onSelect: () => console.log('Account Settings Selected'),
                separator: true,
              },
              {
                label: 'Log Out',
                onSelect: handleLogout,
              },
            ]}
          />
        </div>
      ) : (
        <>
          <div className="hidden lg:block lg:flex item-center space-x-4 ml-20">
            <Link
              href="/login"
              className="text-gray-900 hover:bg-white-hover inline-flex items-center justify-center px-8 py-2 text-[1.1rem] font-bold shadow-xl rounded-[0.4rem] border border-gray-900"
            >
              Login
            </Link>
            <Link
              href="/register"
              className="text-white bg-arxTheme hover:bg-arx-theme-hover inline-flex items-center justify-center px-8 py-2 text-[1.1rem] font-bold shadow-xl rounded-[0.4rem] whitespace-nowrap overflow-hidden text-ellipsis"
            >
              Sign up
            </Link>
          </div>
          <div className="sm:block md:block lg:hidden space-y-6">
            <Link
              href="/login"
              className="block text-gray-900 hover:bg-white-hover items-center justify-center px-8 py-2 text-[1.1rem] font-bold shadow-xl rounded-[0.4rem] border border-gray-900 text-center"
            >
              Login
            </Link>
            <Link
              href="/register"
              className="block text-white bg-arxTheme hover:bg-arx-theme-hover items-center justify-center px-8 py-2 text-[1.1rem] font-bold shadow-xl rounded-[0.4rem] whitespace-nowrap"
            >
              Sign up
            </Link>
          </div>
        </>
      )}
    </div>
  )
}

export default UserMenu
