'use client'
import React, { useEffect, useState } from 'react'
import RightArrow from '@/public/assets/website/RightArrow.svg'
import Image from 'next/image'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import JSZ<PERSON> from 'jszip'

const GradientTitle: React.FC<{ step: string; title: string }> = ({
  step,
  title,
}) => (
  <>
    <h1
      className="inline-block text-2xl font-bold z-10 font-['Public_Sans'] leading-tight pb-1 bg-gradient-to-r from-[#33B4FF] to-[#FFAE00] bg-clip-text text-transparent"
      style={{
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        backgroundSize: '100% 100%',
      }}
    >
      {step}
    </h1>
    <h2 className="mt-2.5 text-xl font-semibold text-neutral-700 max-sm:text-lg">
      {title}
    </h2>
  </>
)

// 主面板组件
interface LeftPanelProps {
  onPdfGenerated?: (pdfData: ArrayBuffer) => void
  onCompileStatusChange?: (isCompiling: boolean) => void
}

const LeftPanel: React.FC<LeftPanelProps> = ({
  onPdfGenerated,
  onCompileStatusChange,
}) => {
  const [templates, setTemplates] = useState<string[]>([])
  const [selectedTemplate, setSelectedTemplate] = useState<string>('')
  const [selectedCompileMethod, setSelectedCompileMethod] = useState<string>('')
  const [isLoading, setIsLoading] = useState(false)
  const [uploadedFiles, setUploadedFiles] = useState<{
    zipFile: File | null
    mainTexFile: string
  }>({
    zipFile: null,
    mainTexFile: '',
  })
  const [showUploadForm, setShowUploadForm] = useState(false)
  const [templateForm, setTemplateForm] = useState({
    templateName: '',
    mainTexFile: '',
    recommendedCompile: '',
    templateZip: null as File | null,
  })
  const [isUploadingTemplate, setIsUploadingTemplate] = useState(false)

  // 编译方法选项
  const compileMethods = [
    { value: 'pdflatex', label: 'PdfLatex' },
    { value: 'xelatex', label: 'XeLatex' },
    { value: 'lualatex', label: 'LuaLatex' },
  ]

  // 获取模板列表
  const fetchTemplates = async () => {
    try {
      const response = await fetch(
        'http://localhost:8012/api/v1/latex-templates/',
      )
      if (!response.ok) {
        throw new Error('Failed to fetch templates')
      }
      const data = await response.json()
      setTemplates(data || [])

      // 如果有模板，默认选择第一个
      if (data.templates && data.templates.length > 0) {
        setSelectedTemplate(data.templates[0].name)
        if (data.templates[0].recommended_compile) {
          setSelectedCompileMethod(data.templates[0].recommended_compile)
        }
      }
    } catch (error) {
      console.error('Error fetching templates:', error)
    }
  }

  useEffect(() => {
    // 在组件加载时获取模板列表
    fetchTemplates()
  }, [])
  // 转换LaTeX项目 - 现在在点击按钮时调用
  const convertProject = async () => {
    if (!selectedTemplate || !selectedCompileMethod) {
      alert('请先选择模板和编译方法')
      return
    }

    if (!uploadedFiles.zipFile) {
      alert('请先上传ZIP文件')
      return
    }

    setIsLoading(true)
    onCompileStatusChange?.(true)
    try {
      const formData = new FormData()
      formData.append('source', uploadedFiles.zipFile)
      formData.append('template_name', selectedTemplate)
      formData.append('main_tex', uploadedFiles.mainTexFile)
      formData.append('compile_method', selectedCompileMethod)

      const response = await fetch('/api/v1/latex-templates/convert', {
        method: 'POST',
        body: formData,
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Conversion failed')
      }

      // 处理文件下载
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `converted_${selectedTemplate}.zip`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
    } catch (error) {
      console.error('Error converting project:', error)
      alert('转换失败: ' + (error as Error).message)
    } finally {
      setIsLoading(false)
      onCompileStatusChange?.(false)
    }
  }
  // 预览PDF功能 - 预览模板转换后的结果
  const previewPdf = async () => {
    if (!selectedTemplate || !selectedCompileMethod) {
      alert('请先选择模板和编译方法')
      return
    }

    if (!uploadedFiles.zipFile) {
      alert('请先上传ZIP文件')
      return
    }

    setIsLoading(true)
    onCompileStatusChange?.(true)
    try {
      // 首先调用模板转换API获取转换后的ZIP文件
      const formData = new FormData()
      formData.append('source', uploadedFiles.zipFile)
      formData.append('template_name', selectedTemplate)
      formData.append('main_tex', uploadedFiles.mainTexFile)
      formData.append('compile_method', selectedCompileMethod)

      const response = await fetch('/api/v1/latex-templates/convert', {
        method: 'POST',
        body: formData,
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Conversion failed')
      }

      // 获取转换后的ZIP文件
      const convertedZipBlob = await response.blob()

      // 使用JSZip解析转换后的ZIP文件
      const JSZip = (await import('jszip')).default
      const convertedZip = new JSZip()
      const convertedZipContent = await convertedZip.loadAsync(convertedZipBlob) // 提取文件到entries数组
      const entries = []
      const allTexFiles = []

      for (const [path, file] of Object.entries(convertedZipContent.files)) {
        if (!file.dir) {
          const buffer = await file.async('arraybuffer')
          entries.push({ path, buffer })

          // 收集所有.tex文件
          if (path.endsWith('.tex')) {
            allTexFiles.push(path)
          }
        }
      }

      // 智能查找主文件
      let mainTexFile = uploadedFiles.mainTexFile

      // 如果原主文件不存在，尝试查找主文件
      const mainFileExists = entries.some(entry => entry.path === mainTexFile)
      if (!mainFileExists && allTexFiles.length > 0) {
        // 首先尝试查找名为 main.tex 的文件
        const mainFile = allTexFiles.find(
          path => path.endsWith('/main.tex') || path === 'main.tex',
        )
        if (mainFile) {
          mainTexFile = mainFile
        } else {
          // 如果没有main.tex，查找包含原文件名的文件
          const originalBaseName =
            uploadedFiles.mainTexFile.split('/').pop() ||
            uploadedFiles.mainTexFile
          const matchingFile = allTexFiles.find(path =>
            path.endsWith(originalBaseName),
          )
          if (matchingFile) {
            mainTexFile = matchingFile
          } else {
            // 最后使用第一个.tex文件
            mainTexFile = allTexFiles[0]
          }
        }
      }
      console.warn('Found .tex files:', allTexFiles)
      console.warn('Using main file:', mainTexFile)
      console.warn(
        'All files in converted ZIP:',
        entries.map(e => e.path),
      )

      // 编译转换后的LaTeX
      const { compile } = await import('@/lib/compile')
      const result = await compile(entries, {
        engineType: selectedCompileMethod === 'xelatex' ? 'xetex' : 'pdftex',
        mainFile: mainTexFile,
      })

      if (result.status === 0 && result.pdf) {
        // 编译成功，传递PDF数据给父组件
        onPdfGenerated?.(result.pdf)
      } else {
        throw new Error('编译失败: ' + result.log)
      }
    } catch (error) {
      console.error('Error converting and compiling PDF:', error)
      alert('转换和编译失败: ' + (error as Error).message)
    } finally {
      setIsLoading(false)
      onCompileStatusChange?.(false)
    }
  }

  // 处理模板ZIP文件上传
  const handleTemplateZipUpload = (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const file = event.target.files?.[0]
    if (file && file.type === 'application/zip') {
      setTemplateForm(prev => ({ ...prev, templateZip: file }))
    } else {
      alert('请选择一个有效的ZIP文件')
    }
  }

  // 上传自定义模板
  const uploadTemplate = async () => {
    if (
      !templateForm.templateName ||
      !templateForm.mainTexFile ||
      !templateForm.recommendedCompile ||
      !templateForm.templateZip
    ) {
      alert('请填写所有必需字段')
      return
    }

    setIsUploadingTemplate(true)
    try {
      const formData = new FormData()
      formData.append('template', templateForm.templateZip)
      formData.append('template_name', templateForm.templateName)
      formData.append('main_tex', templateForm.mainTexFile)
      formData.append('recommended_compile', templateForm.recommendedCompile)

      const response = await fetch('/api/v1/template-converter/upload', {
        method: 'POST',
        body: formData,
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.detail || 'Upload failed')
      }

      // 重置表单
      setTemplateForm({
        templateName: '',
        mainTexFile: '',
        recommendedCompile: '',
        templateZip: null,
      })

      // 隐藏表单
      setShowUploadForm(false)

      // 重新获取模板列表
      await fetchTemplates()

      alert(`模板 "${templateForm.templateName}" 上传成功！`)
    } catch (error) {
      console.error('Error uploading template:', error)
      alert('上传失败: ' + (error as Error).message)
    } finally {
      setIsUploadingTemplate(false)
    }
  }

  const handleZipUpload = async (zipFile: File) => {
    try {
      // Create JSZip instance to read the uploaded file
      const zip = new JSZip()
      const zipContent = await zip.loadAsync(zipFile)

      // Extract all files from the zip
      const files = []
      for (const [path, file] of Object.entries(zipContent.files)) {
        if (!file.dir) {
          const fileContent = await file.async('blob')
          files.push({
            path: path,
            file: fileContent,
          })
        }
      }

      // Find .tex files to determine the main file
      const texFiles = files.filter(file => file.path.endsWith('.tex'))
      const mainTexFile = texFiles.length > 0 ? texFiles[0].path : 'main.tex' // Update state with the uploaded file info
      setUploadedFiles({
        zipFile: zipFile,
        mainTexFile: mainTexFile,
      })
    } catch (error) {
      console.error('Error processing ZIP file:', error)
      alert('处理ZIP文件时出错: ' + (error as Error).message)
    }
  }

  return (
    <section className="p-6 w-[750px] max-md:w-full ml-2 overflow-y-auto  max-h-[calc(100vh-72px)]">
      <div className="mb-10">
        <GradientTitle
          step="Step1"
          title="Choose Template and Compile Method"
        />
        <div className="mt-5">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <div // Outer div for gradient border
                className={`mb-4 rounded-lg bg-gradient-to-r from-[#33B4FF] to-[#FFAE00] w-[500px] max-sm:w-full cursor-pointer p-[2px]`}
              >
                <div // Inner div for content and solid background
                  className={`flex justify-between items-center px-5 h-[calc(2.75rem-4px)] text-xs font-medium rounded-[calc(0.5rem-2px)] bg-zinc-50 bg-opacity-60 text-neutral-500 w-full`}
                >
                  <div className="select-text">
                    {selectedTemplate || 'Choose Template'}
                  </div>
                  <i className="ti ti-chevron-down" />
                </div>
              </div>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-[500px]">
              {templates.map(template => (
                <DropdownMenuItem
                  key={template}
                  onClick={() => setSelectedTemplate(template)}
                >
                  {template}
                </DropdownMenuItem>
              ))}
              {templates.length === 0 && (
                <DropdownMenuItem disabled>
                  No templates available
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>{' '}
          <div
            className="ml-2.5 mx-0 mt-3 mb-6 text-sm font-medium underline text-neutral-500 hover:text-sky-500 cursor-pointer"
            onClick={() => setShowUploadForm(!showUploadForm)}
          >
            Didn&apos;t find the template here? Upload your template
          </div>
          {/* 上传模板表单 */}
          {showUploadForm && (
            <div className="bg-gray-100 p-6 rounded-lg mb-6">
              <h3 className="text-lg font-semibold mb-4 text-neutral-700">
                Upload New Template
              </h3>
              {/* 模板名称和主文件名输入框并排 */}
              <div className="mb-4 flex gap-4">
                {/* 模板名称输入框 */}
                <div className="flex-1">
                  <label className="block text-sm font-medium text-neutral-600 mb-2">
                    Template Name
                  </label>
                  <div className="rounded-lg bg-gradient-to-r from-[#33B4FF] to-[#FFAE00] w-full p-[2px]">
                    <input
                      type="text"
                      value={templateForm.templateName}
                      onChange={e =>
                        setTemplateForm(prev => ({
                          ...prev,
                          templateName: e.target.value,
                        }))
                      }
                      placeholder="Enter template name"
                      className="w-full px-4 py-2 text-sm font-medium rounded-[calc(0.5rem-2px)] bg-zinc-50 bg-opacity-60 text-neutral-700 border-none outline-none"
                    />
                  </div>
                </div>
                {/* 主文件名输入框 */}
                <div className="flex-1">
                  <label className="block text-sm font-medium text-neutral-600 mb-2">
                    Main TeX File
                  </label>
                  <div className="rounded-lg bg-gradient-to-r from-[#33B4FF] to-[#FFAE00] w-full p-[2px]">
                    <input
                      type="text"
                      value={templateForm.mainTexFile}
                      onChange={e =>
                        setTemplateForm(prev => ({
                          ...prev,
                          mainTexFile: e.target.value,
                        }))
                      }
                      placeholder="e.g., main.tex"
                      className="w-full px-4 py-2 text-sm font-medium rounded-[calc(0.5rem-2px)] bg-zinc-50 bg-opacity-60 text-neutral-700 border-none outline-none"
                    />
                  </div>
                </div>
              </div>

              {/* 推荐编译方法选择 */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-neutral-600 mb-2">
                  Recommended Compile Method
                </label>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <div className="rounded-lg bg-gradient-to-r from-[#33B4FF] to-[#FFAE00] w-full cursor-pointer p-[2px]">
                      <div className="flex justify-between items-center px-4 py-2 text-sm font-medium rounded-[calc(0.5rem-2px)] bg-zinc-50 bg-opacity-60 text-neutral-500 w-full">
                        <div className="select-text">
                          {templateForm.recommendedCompile
                            ? compileMethods.find(
                                m =>
                                  m.value === templateForm.recommendedCompile,
                              )?.label
                            : 'Choose Compile Method'}
                        </div>
                        <i className="ti ti-chevron-down" />
                      </div>
                    </div>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="w-full">
                    {compileMethods.map(method => (
                      <DropdownMenuItem
                        key={method.value}
                        onClick={() =>
                          setTemplateForm(prev => ({
                            ...prev,
                            recommendedCompile: method.value,
                          }))
                        }
                      >
                        {method.label}
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>

              {/* ZIP文件上传 */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-neutral-600 mb-2">
                  Template ZIP File
                </label>
                <div className="rounded-lg bg-gradient-to-r from-[#33B4FF] to-[#FFAE00] w-full p-[2px]">
                  <div className="rounded-[calc(0.5rem-2px)] bg-zinc-50 bg-opacity-60 p-3">
                    <input
                      type="file"
                      accept=".zip"
                      onChange={handleTemplateZipUpload}
                      className="w-full text-sm text-neutral-700 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-sky-50 file:text-sky-700 hover:file:bg-sky-100"
                    />
                    {templateForm.templateZip && (
                      <p className="text-xs text-green-600 mt-2">
                        Selected: {templateForm.templateZip.name}
                      </p>
                    )}
                  </div>
                </div>
              </div>

              {/* 按钮组 */}
              <div className="flex gap-3">
                <button
                  onClick={uploadTemplate}
                  disabled={
                    isUploadingTemplate ||
                    !templateForm.templateName ||
                    !templateForm.mainTexFile ||
                    !templateForm.recommendedCompile ||
                    !templateForm.templateZip
                  }
                  className="inline-flex gap-2.5 items-center px-6 py-2 text-base font-semibold text-white bg-sky-500 rounded-[50px] hover:bg-sky-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span>
                    {isUploadingTemplate ? 'Uploading...' : 'Add Template'}
                  </span>
                  <Image
                    src={RightArrow}
                    alt="Right Arrow"
                    width={20}
                    height={20}
                    className="w-6 h-6"
                    style={{ filter: 'brightness(0) invert(1)' }}
                  />
                </button>
                <button
                  onClick={() => setShowUploadForm(false)}
                  className="px-6 py-2 text-base font-semibold text-neutral-600 bg-gray-200 rounded-[50px] hover:bg-gray-300 transition-colors"
                >
                  Cancel
                </button>
              </div>
            </div>
          )}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <div // Outer div for gradient border
                className={`mb-4 rounded-lg bg-gradient-to-r from-[#33B4FF] to-[#FFAE00] w-[500px] max-sm:w-full cursor-pointer p-[2px]`}
              >
                <div // Inner div for content and solid background
                  className={`flex justify-between items-center px-5 h-[calc(2.75rem-4px)] text-xs font-medium rounded-[calc(0.5rem-2px)] bg-zinc-50 bg-opacity-60 text-neutral-500 w-full`}
                >
                  <div className="select-text">
                    {selectedCompileMethod || 'Choose Compile Method'}
                  </div>
                  <i className="ti ti-chevron-down" />
                </div>
              </div>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-[500px]">
              {compileMethods.map(method => (
                <DropdownMenuItem
                  key={method.value}
                  onClick={() => setSelectedCompileMethod(method.value)}
                >
                  {method.label}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      <div className="mb-10">
        <GradientTitle step="Step2" title="Upload LaTeX ZIP Package" />
        <div
          className="flex flex-col justify-center items-center mb-8 cursor-pointer rounded-xl border-2 border-sky-400 border-dashed bg-zinc-50 bg-opacity-60 h-[120px] w-[500px] max-sm:w-full transition-colors hover:bg-sky-50 relative"
          onClick={() => document.getElementById('zip-upload')?.click()}
          onDragOver={e => {
            e.preventDefault()
            e.stopPropagation()
          }}
          onDrop={async e => {
            e.preventDefault()
            e.stopPropagation()
            const files = Array.from(e.dataTransfer.files)
            const zipFile = files.find(
              file =>
                file.type === 'application/zip' || file.name.endsWith('.zip'),
            )
            if (zipFile) {
              await handleZipUpload(zipFile)
            } else {
              alert('请上传ZIP文件')
            }
          }}
        >
          {uploadedFiles.zipFile ? (
            <div className="text-center p-4">
              <p className="text-sm font-medium text-green-600">
                {uploadedFiles.zipFile.name}
              </p>
              {uploadedFiles.mainTexFile && (
                <p className="text-xs text-neutral-500 mt-1">
                  Main file: {uploadedFiles.mainTexFile}
                </p>
              )}
              <p className="text-xs text-neutral-400 mt-2">
                Click to upload a different file
              </p>
            </div>
          ) : (
            <>
              <div className="mb-2 text-3xl text-neutral-500">+</div>
              <p className="text-sm font-medium text-neutral-500">
                Click or drag & drop your LaTeX ZIP package here
              </p>
            </>
          )}
          <input
            id="zip-upload"
            type="file"
            accept=".zip"
            className="hidden"
            onChange={async e => {
              const file = e.target.files?.[0]
              if (file) {
                await handleZipUpload(file)
              }
            }}
          />
        </div>
      </div>
      <div className="mb-10">
        <GradientTitle step="Step3" title="Preview and Download" />
        <div className="mt-5 flex gap-4">
          <button
            className="inline-flex gap-2.5 items-center px-6 py-2 text-base font-semibold text-white bg-green-500 rounded-[50px] hover:bg-green-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={
              isLoading ||
              !selectedTemplate ||
              !selectedCompileMethod ||
              !uploadedFiles.zipFile
            }
            onClick={previewPdf}
          >
            <span>
              {isLoading
                ? 'Converting & Compiling...'
                : 'Preview Converted PDF'}
            </span>
            <Image
              src={RightArrow}
              alt="Right Arrow"
              width={20}
              height={20}
              className="w-6 h-6"
              style={{ filter: 'brightness(0) invert(1)' }}
            />
          </button>
          <button
            className="inline-flex gap-2.5 items-center px-6 py-2 text-base font-semibold text-white bg-sky-500 rounded-[50px] hover:bg-sky-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={
              isLoading ||
              !selectedTemplate ||
              !selectedCompileMethod ||
              !uploadedFiles.zipFile
            }
            onClick={convertProject}
          >
            <span>{isLoading ? 'Processing...' : 'Download Template'}</span>
            <Image
              src={RightArrow}
              alt="Right Arrow"
              width={20}
              height={20}
              className="w-6 h-6"
              style={{ filter: 'brightness(0) invert(1)' }}
            />
          </button>
        </div>
      </div>
    </section>
  )
}

export default LeftPanel
