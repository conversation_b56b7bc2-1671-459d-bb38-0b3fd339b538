// components/login-dialog.tsx
'use client'

import React from 'react'
import { Dialog, DialogContent, DialogOverlay } from '@/components/ui/dialog'
import { useLoginDialogStore } from '@/stores/auth-store'
import LoginPage from '@/app/(auth)/login/page'

interface LoginDialogProps {
  dialogStyle?: {
    width?: string
    height?: string
    maxWidth?: string
    maxHeight?: string
  }
}

export function LoginDialog({
  dialogStyle = {
    width: '100%',
    height: 'auto',
    maxWidth: '32rem',
    maxHeight: '90vh',
  },
}: LoginDialogProps) {
  const { isOpen, setIsOpen } = useLoginDialogStore()

  // const handleClose = () => {
  //   setIsOpen(false)
  //   // Execute any pending operation after successful login
  //   if (pendingOperation) {
  //     pendingOperation()
  //     setPendingOperation(null)
  //   }
  // }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogOverlay className="bg-black/50" />
      <DialogContent
        className="sm:max-w-md md:max-w-lg p-0 overflow-hidden bg-transparent border-none shadow-none"
        style={dialogStyle}
      >
        {/* <LoginPage isDialog={true} onClose={handleClose} /> */}
        <LoginPage />
      </DialogContent>
    </Dialog>
  )
}
