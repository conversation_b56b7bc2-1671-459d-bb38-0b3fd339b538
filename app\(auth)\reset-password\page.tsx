// app/(auth)/reset-password/[token]/page.tsx
'use client'

import React, { Suspense, useEffect, useState } from 'react'
import { useForm, FormProvider } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import Link from 'next/link'
import { useRouter, useSearchParams } from 'next/navigation'
import { toast } from 'sonner'
import { FormInput } from '@/components/form/form-input'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from '@/components/ui/card'

import {
  resetPassword,
  ResetPasswordFormValues,
  resetPasswordSchema,
} from '@/services/auth/password-reset'
import { Lock } from 'lucide-react'
import { FormField } from '@/components/ui/form'

function ResetPasswordPage() {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const router = useRouter()
  const searchParams = useSearchParams()
  const resetToken = searchParams.get('code') || ''

  const form = useForm<ResetPasswordFormValues>({
    resolver: zodResolver(resetPasswordSchema),
    defaultValues: {
      password: '',
      passwordConfirm: '',
      resetToken: resetToken,
    },
  })

  const {
    handleSubmit,
    formState: { errors },
  } = form

  const onSubmit = async (data: ResetPasswordFormValues) => {
    setIsSubmitting(true)
    try {
      await resetPassword(data)
      toast.success(
        'Password updated successfully. You can now log in with your new password.',
      )
      router.push('/login')
    } finally {
      setIsSubmitting(false)
    }
  }

  useEffect(() => {
    if (!resetToken) {
      router.push('/login')
    }
  }, [resetToken, router])

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <CardTitle className="text-4xl font-bold text-primary lg:text-5xl">
            Reset Password
          </CardTitle>
          <CardDescription className="text-lg">
            Please enter your new password
          </CardDescription>
        </CardHeader>

        <CardContent>
          <FormProvider {...form}>
            <form
              onSubmit={handleSubmit(onSubmit)}
              noValidate
              className="space-y-4"
            >
              <FormInput
                label="New Password"
                name="password"
                type="password"
                placeholder="Enter your new password"
                register={form.register}
                errors={errors}
                icon={<Lock className="h-5 w-5" />}
                required
              />

              <FormInput
                label="Confirm Password"
                name="passwordConfirm"
                type="password"
                placeholder="Confirm your new password"
                register={form.register}
                errors={errors}
                icon={<Lock className="h-5 w-5" />}
                required
              />

              <FormField
                control={form.control}
                name="resetToken"
                render={() => <></>}
              />

              <Button
                type="submit"
                className="w-full"
                disabled={isSubmitting}
                tag-id="reset-password-submit"
              >
                {isSubmitting ? 'Resetting...' : 'Reset Password'}
              </Button>

              <div className="text-center mt-4">
                <Link href="/login" className="text-primary hover:underline" tag-id="return-to-login-button">
                  Back to Login
                </Link>
              </div>
            </form>
          </FormProvider>
        </CardContent>
      </Card>
    </div>
  )
}

export default function Page() {
  return (
    <Suspense>
      <ResetPasswordPage />
    </Suspense>
  )
}
