'use client'
import { Tooltip } from '@/components/ui/tooltip'
import { Button } from '@/components/ui/button'
import { LogoIcon, Redo, Undo } from '@/components/ar-icons'
import dynamic from 'next/dynamic'
import { Suspense } from 'react'
import { useYProjectContext } from '@/lib/y-adapter/use-yproject-context'
import { useExplorerStore } from '@/stores/explorer-store'
import { isTextNode } from '@/lib/y-project/file-index'

const LazyEditor = dynamic(() => import('./lazy-editor'), {
  ssr: false,
})

export default function InternalEditor() {
  const { yproject } = useYProjectContext()
  const selectedNode = useExplorerStore(state => state.selectedNode)
  const textId =
    selectedNode && isTextNode(selectedNode) ? selectedNode.uuid : undefined
  const undoManager = textId
    ? yproject.undoHandler.getTextManager(textId)
    : undefined

  return (
    <Suspense>
      <div className="flex flex-col h-full overflow-hidden">
        <div className="flex bg-[#EEEEEE] p-2 justify-between items-center">
          <div>
            <LogoIcon />
          </div>
          <div className="flex items-center gap-1">
            <Tooltip content="AI" asChild>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 hover:bg-gray-200"
              >
                <span className="h-6 w-6 text-gray-800">✨</span>
              </Button>
            </Tooltip>
            <Tooltip content="Undo" asChild>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 hover:bg-gray-200"
                onClick={() => undoManager?.undo()}
              >
                <Undo className="h-6 w-6 text-gray-800" />
              </Button>
            </Tooltip>
            <Tooltip content="Redo" asChild>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 hover:bg-gray-200"
                onClick={() => undoManager?.redo()}
              >
                <Redo className="h-6 w-6 text-gray-800" />
              </Button>
            </Tooltip>
          </div>
        </div>
        <div className="flex-1 h-0">
          {textId ? (
            <LazyEditor />
          ) : (
            <div className="flex items-center justify-center h-full">
              <p className="text-gray-500">Please select a text file to edit</p>
            </div>
          )}
        </div>
      </div>
    </Suspense>
  )
}
