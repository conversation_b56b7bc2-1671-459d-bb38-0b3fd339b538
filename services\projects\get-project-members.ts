import { useQuery } from '@tanstack/react-query'
import api from '../api'
import queryClient from '@/services/query-client'
import { User } from '../types'

export interface ProjectMember {
  userId: string
  user: User
  role: 'collaborator' | 'viewer'
}

export async function getProjectMembers(projectId: string) {
  const res = await api.get(`/projects/${projectId}/members`)
  return res.data as ProjectMember[]
}

export function useGetProjectMembers(projectId: string) {
  return useQuery(
    {
      queryKey: ['project-members', projectId],
      queryFn: () => getProjectMembers(projectId),
    },
    queryClient,
  )
}
