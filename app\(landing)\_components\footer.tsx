'use client'

import React, { useEffect, useState } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import logo from '@/public/assets/logo-icon.svg'
import instagram from '@/public/assets/website/instagram.svg'
import dribbble from '@/public/assets/website/dribbble.svg'
import twitter from '@/public/assets/website/twitter.svg'
import youtube from '@/public/assets/website/youtube.svg'
import { usePathname } from 'next/navigation'
import { PROJECT_CODE } from '@/lib/constant'

const Footer = () => {
  const [isHomePage, setIsHomePage] = useState(false)
  const pathname = usePathname()

  useEffect(() => {
    const isHome = pathname === '/' || pathname === '/home'
    setIsHomePage(isHome)
  }, [pathname])

  return (
    <footer
      id="Footer"
      className={`${isHomePage ? 'bg-black text-white' : 'bg-white text-black'} py-0 px-6 md:px-24 md:py-12 lg:py-14 lg:pl-28 lg:pr-36 h-auto`}
    >
      <div className="container mx-auto flex flex-col lg:flex-row lg:justify-between lg:items-start my-6">
        {/* First Block */}
        <div className="flex flex-col md:inline-block lg:mb-0 lg:flex-1 lg:pl-16 mb-6 gap-[15px]">
          <div className="flex justify-center md:block items-center text-center font-semibold">
            <Link href="/" className="flex items-center">
              <Image
                src={logo}
                alt="logo"
                width={28}
                height={40}
                className="w-7 h-10"
              />
              <span className="ml-2">{PROJECT_CODE}</span>
            </Link>
          </div>

          <div
            className={`text-md ${isHomePage ? 'text-white' : 'text-black'} text-center md:text-left leading-loose mt-4`}
          >
            Copyright &copy; {new Date().getFullYear()}
            <Link
              href="#"
              className={`hover:${isHomePage ? 'text-gray-300' : 'text-gray-700'} hover:underline`}
            >
              &nbsp;{PROJECT_CODE}
            </Link>
            &nbsp;ltd.
          </div>

          <div
            className={`text-md ${isHomePage ? 'text-white' : 'text-black'} text-center md:text-left leading-loose mt-4`}
          >
            All rights reserved.
          </div>

          <div className="mx-auto text-center mt-4">
            <ul className="flex mb-4 md:mb-0">
              <li>
                <Link
                  href="#"
                  className="rounded-full shadow transition duration-150 ease-in-out"
                  aria-label="instagram"
                >
                  <Image
                    src={instagram}
                    alt="Instagram"
                    width={24}
                    height={24}
                    className="bg-black rounded-full"
                  />
                </Link>
              </li>
              <li className="ml-4">
                <Link
                  href="#"
                  className="rounded-full shadow transition duration-150 ease-in-out"
                  aria-label="dribbble"
                >
                  <Image
                    src={dribbble}
                    alt="Dribbble"
                    width={24}
                    height={24}
                    className="bg-black rounded-full"
                  />
                </Link>
              </li>
              <li className="ml-4">
                <Link
                  href="#"
                  className="rounded-full shadow transition duration-150 ease-in-out"
                  aria-label="twitter"
                >
                  <Image
                    src={twitter}
                    alt="Twitter"
                    width={24}
                    height={24}
                    className="bg-black rounded-full"
                  />
                </Link>
              </li>
              <li className="ml-4">
                <Link
                  href="#"
                  className="rounded-full shadow transition duration-150 ease-in-out"
                  aria-label="youtube"
                >
                  <Image
                    src={youtube}
                    alt="YouTube"
                    width={24}
                    height={24}
                    className="bg-black rounded-full"
                  />
                </Link>
              </li>
            </ul>
          </div>
        </div>

        {/* Second Block */}
        <div className="mb-6 lg:mb-0 lg:w-[16%] hidden md:block">
          <h4
            className={`font-semibold mb-4 text-xl ${isHomePage ? 'text-white' : 'text-black'}`}
          >
            Contact
          </h4>
          <ul>
            <li
              className={`pb-3 ${isHomePage ? 'text-[#f5f7fae0] hover:text-white' : 'text-gray-700 hover:text-black'}`}
            >
              <Link href="mailto:<EMAIL>"><EMAIL></Link>
            </li>
          </ul>
        </div>
      </div>
    </footer>
  )
}

export default Footer
