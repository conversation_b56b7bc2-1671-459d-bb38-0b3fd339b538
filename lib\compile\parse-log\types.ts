import { JSX } from 'react'

export interface Rule {
  ruleId: string
  regexToMatch: RegExp
  newMessage?: string
  contentRegex?: RegExp
  improvedTitle?: (
    currentTitle: string | JSX.Element,
    details?: string[],
  ) => [string, JSX.Element] | string | JSX.Element
  cascadesFrom?: string[]
  types?: string[]
  package?: string
}

export interface LogEntry {
  level: 'info' | 'warning' | 'error' | 'typesetting'
  message: string
  content?: string
  ruleId?: string
  contentDetails?: string[]
  suppressed?: boolean
  file?: string
  line?: string
  raw?: string
}

export interface FileNode {
  path: string
  files: FileNode[]
}

export interface ParseResult {
  all: LogEntry[]
  errors: LogEntry[]
  warnings: LogEntry[]
  typesetting: LogEntry[]
  files: FileNode[]
}
