import React from 'react'
import Link from 'next/link'

const Join = () => {
  return (
    <div
      id="services"
      className="py-4 bg-black m-auto px-4 sm:px-8 md:px-16 lg:px-32"
    >
      <section>
        <div className="my-12 md:my-16 lg:my-20 py-4 h-full">
          <h2 className="my-2 text-center text-3xl lg:text-[40px] font-bold text-white max-w-[80%] mx-auto">
            Join us to build the future of academic writing
          </h2>
        </div>

        <div className="flex justify-center pb-2">
          <Link
            href="/explore"
            className="relative inline-flex items-center justify-center w-full px-8 sm:px-12 py-3 sm:py-4 my-4 text-base sm:text-lg text-white shadow-xl rounded-[50px] sm:w-auto sm:mb-0 group"
          >
            {/* Gradient Border */}
            <span className="absolute inset-y-3 inset-x-6 sm:inset-x-11 rounded-[50px] p-[0.5px] bg-gradient-to-r from-[#35B4FC] to-[#FEAE02] z-0"></span>

            {/* Button Infill */}
            <span className="relative flex items-center justify-center w-full h-full bg-[rgba(250,250,250,0.55)] rounded-[50px] px-8 sm:px-12 py-3 sm:py-4 z-10 text-lg sm:text-[20px]">
              Apply for Job
            </span>
          </Link>
        </div>
      </section>
    </div>
  )
}

export default Join
