/**
 * Refs: https://discuss.yjs.dev/t/snapshots-syncing-and-history/1083/3
 */
import * as Y from 'yjs'
import { YProject, YProjectConfig } from '.'
import { v4 as uuidv4 } from 'uuid'

export interface ProjectVersion {
  id: string
  title: string
  date: number
  clientID: number
  snapshot: Uint8Array
  hash: string
  contributors: Record<string, Contributor>
}

interface Contributor {
  id: string
  name: string
}
export default class VersionHandler {
  private static VERSION_STORAGE_KEY = 'VERSION'
  private static CONTRIBUTORS_KEY = 'CONTRIBUTOR'

  private yproject: YProject
  private lastDocumentCopy: Y.Doc | null = null

  versions: Y.Array<ProjectVersion>
  contributors: Y.Map<Contributor>
  onDocUpdate: ((update: Uint8Array, origin: unknown) => void) | null = null

  constructor(yproject: YProject, config: YProjectConfig) {
    this.yproject = yproject
    this.versions = yproject.ydoc.getArray<ProjectVersion>(
      VersionHandler.VERSION_STORAGE_KEY,
    )
    this.contributors = yproject.ydoc.getMap<Contributor>(
      VersionHandler.CONTRIBUTORS_KEY,
    )

    if (!config.readonly && config.user) {
      this.onDocUpdate = (_update, origin) => {
        if (yproject.internalOrigins.has(origin)) return
        if (!this.contributors.get(config.user!.id)) {
          this.contributors.set(config.user!.id, {
            id: config.user!.id,
            name: config.user!.name,
          })
        }
      }
      yproject.ydoc.on('update', this.onDocUpdate)
    }
  }

  public async addVersion(title: string = '') {
    const versions = this.versions
    const prevVersion =
      versions.length === 0 ? null : versions.get(versions.length - 1)
    const hash = await this.yproject.getSha256Hash()
    if (!prevVersion || prevVersion.hash !== hash) {
      const snapshot = Y.snapshot(this.yproject.ydoc)
      versions.push([
        {
          id: uuidv4(),
          title,
          date: new Date().getTime(),
          snapshot: Y.encodeSnapshot(snapshot),
          clientID: this.yproject.ydoc.clientID,
          hash,
          contributors: this.contributors.toJSON(),
        },
      ])

      this.yproject.ydoc.transact(() => {
        this.contributors.clear()
      }, this)

      return true
    }
    return false
  }

  /**
   * Prepares the YProject instance for a specific version. \
   * Ensures that the YProject is synced before returning.
   */
  public async switchVersion(versionId?: string) {
    if (versionId === undefined) {
      return this.yproject
    }

    await this.yproject.initialSynced
    let version: ProjectVersion | undefined = undefined
    for (const v of this.versions) {
      if (v.id === versionId) {
        version = v
        break
      }
    }

    if (!version) {
      throw new Error(`Version with id ${versionId} not found`)
    }

    if (this.lastDocumentCopy) {
      this.lastDocumentCopy.destroy()
      this.lastDocumentCopy = null
    }

    const snapshot = Y.decodeSnapshot(version.snapshot)
    const documentCopy = new Y.Doc({ gc: false })
    const update = Y.encodeStateAsUpdate(this.yproject.ydoc)
    Y.applyUpdate(documentCopy, update)
    const documentSnapshot = Y.createDocFromSnapshot(documentCopy, snapshot)
    const projectSnapshot = new YProject({
      projectId: this.yproject.projectId,
      ydoc: documentSnapshot,
      connectivity: false,
    })

    return new Proxy(this.yproject, {
      get(target, prop) {
        if (prop === 'destroy') {
          return () => {
            documentCopy.destroy()
            target.destroy()
          }
        } else if (prop === 'versionHandler') {
          return target.versionHandler
        } else {
          return Reflect.get(projectSnapshot, prop)
        }
      },
    })
  }

  public getVersions() {
    return this.versions.toJSON()
  }

  public deleteVersion(versionId: string) {
    for (let i = 0; i < this.versions.length; i++) {
      if (this.versions.get(i).id === versionId) {
        this.versions.delete(i)
        return true
      }
    }
    return false
  }

  destroy() {
    if (this.onDocUpdate) {
      this.yproject.ydoc.off('update', this.onDocUpdate)
      this.onDocUpdate = null
    }
    this.lastDocumentCopy?.destroy()
    this.lastDocumentCopy = null
  }
}
