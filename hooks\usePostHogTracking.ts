'use client'
import { getEventConfig } from '@/lib/posthog-tracking-events'

export function initializeGlobalClickTracking() {
  console.warn('initializeGlobalClickTracking')
  if (typeof window === 'undefined') return

  document.removeEventListener('click', globalClickHandler)
  document.addEventListener('click', globalClickHandler)
}

// global click handler
function globalClickHandler(event: Event) {
  const target = event.target as HTMLElement
  if (!target) return

  const elementWithTagId = target.closest('[tag-id]') as HTMLElement
  if (!elementWithTagId) return
  const tagId = elementWithTagId.getAttribute('tag-id')
  if (!tagId) return

  // get current page pathname
  const pathname = window.location.pathname

  console.warn('globalClickHandler:', {
    tagId,
    pathname,
    elementWithTagId,
  })

  // get event config by pathname and tagId
  const eventConfig = getEventConfig(pathname, tagId)
  if (!eventConfig) {
    console.warn('No event config found for:', { pathname, tagId })
    return
  }
  console.warn('Found event config:', {
    event: eventConfig.event,
    handler: eventConfig.handler.name,
  })

  // directly call event handler and sendPostHogEvent in posthog-tracking.ts
  try {
    eventConfig.handler()
    console.warn('Event handler called successfully for:', eventConfig.event)
  } catch (error) {
    console.error('Error calling event handler:', error)
  }
}

// cleanup global click tracking
export function cleanupGlobalClickTracking() {
  if (typeof window === 'undefined') return
  document.removeEventListener('click', globalClickHandler)
}
