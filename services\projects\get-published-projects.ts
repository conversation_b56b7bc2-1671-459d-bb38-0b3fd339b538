import { useQuery } from '@tanstack/react-query'
import api from '../api'
import queryClient from '../query-client'
import { Project } from './get-projects'

export default async function getPublishedProjects() {
  const response = await api.get('/published-projects')
  return response.data as Project[]
}

export function useGetPublishedProjects() {
  return useQuery(
    {
      queryKey: ['published-projects'],
      queryFn: () => getPublishedProjects(),
    },
    queryClient,
  )
}
