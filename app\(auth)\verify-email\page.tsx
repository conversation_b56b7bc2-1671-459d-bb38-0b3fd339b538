/*
 * @Description: Email verification page
 * @Author: Devin
 * @Date: 2024-05-28 13:48:03
 */
'use client'

import React, { Suspense, useEffect, useState } from 'react'
import { useForm, FormProvider } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { useRouter, useSearchParams } from 'next/navigation'
import { toast } from 'sonner'
import Link from 'next/link'
import { verifyEmail } from '@/services/auth/verify-email'
import { Button } from '@/components/ui/button'
import { Code } from 'lucide-react'
import { FormInput } from '@/components/form/form-input'

// Define schema with TypeScript integration
const verificationCodeSchema = z.object({
  verificationCode: z.string().nonempty('Verification code is required'),
})

type VerificationFormData = z.infer<typeof verificationCodeSchema>

const EmailVerificationPage = () => {
  const router = useRouter()
  const searchParams = useSearchParams()
  const verifyCode = searchParams.get('code')
  const [isLoading, setIsLoading] = useState<boolean>(false)

  const methods = useForm<VerificationFormData>({
    resolver: zodResolver(verificationCodeSchema),
    defaultValues: {
      verificationCode: '',
    },
  })

  const {
    reset,
    register,
    handleSubmit,
    formState: { errors },
  } = methods

  useEffect(() => {
    if (verifyCode) {
      reset({ verificationCode: verifyCode })
    }
  }, [verifyCode, reset])

  const onSubmitHandler = async (data: VerificationFormData) => {
    setIsLoading(true)
    try {
      const response = await verifyEmail(data.verificationCode)
      toast.success(response.data.message)
      router.push('/login')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="flex items-center justify-center h-full overflow-auto bg-[#ffffff]">
      <div
        className="flex flex-col items-center justify-center bg-white p-8 rounded shadow-md"
        style={{ maxWidth: '33rem', width: '100%' }}
      >
        <h1 className="text-4xl font-bold text-center mb-6 pt-2 text-arxTheme lg:text-5xl">
          Verification Email
        </h1>

        <FormProvider {...methods}>
          <form
            noValidate
            autoComplete="off"
            className="w-full"
            onSubmit={handleSubmit(onSubmitHandler)}
          >
            <FormInput
              label="Verification Code"
              type="input"
              name="verificationCode"
              placeholder="Verification Code"
              register={register}
              errors={errors}
              icon={<Code className="h-5 w-5" />}
            />
            <Button
              loading={isLoading}
              className="w-full py-2 rounded hover:bg-primary-dark"
              type="submit"
              tag-id="verify-email-submit"
            >
              Verify Email
            </Button>
            <div className="my-4 text-center">
              <Link href="/login" className="text-arxTheme hover:underline" tag-id="return-to-login-button">
                Back to Login
              </Link>
            </div>
          </form>
        </FormProvider>
      </div>
    </div>
  )
}

export default function Page() {
  return (
    <Suspense>
      <EmailVerificationPage />
    </Suspense>
  )
}
