import Script from 'next/script'

// export default function RegisterServiceWorker() {
//   return (
//     <Script id="service-worker" strategy="afterInteractive">
//       {`if ('serviceWorker' in navigator) {
//             window.addEventListener('load', () => {
//               navigator.serviceWorker.register('/sw.js')
//                 .then(reg => console.log('Service Worker registered:', reg))
//                 .catch(err => console.error('Registration failed:', err));
//             });
//           }`}
//     </Script>
//   )
// }

export default function RegisterServiceWorker() {
  return (
    <Script id="service-worker" strategy="afterInteractive">
      {`if ('serviceWorker' in navigator) {
      window.addEventListener('load', () => {
        navigator.serviceWorker.getRegistrations()
          .then(registrations => {
            // Unregister all service workers
            for (let registration of registrations) {
              registration.unregister()
                .then(success => {
                  if (success) {
                    console.log('Service Worker unregistered successfully');
                  } else {
                    console.log('Service Worker unregistration failed');
                  }
                });
            }
            // If no registrations found
            if (registrations.length === 0) {
              console.log('No Service Workers found to unregister');
            }
          })
          .catch(err => console.error('Error getting registrations:', err));
      });
      }`}
    </Script>
  )
}
