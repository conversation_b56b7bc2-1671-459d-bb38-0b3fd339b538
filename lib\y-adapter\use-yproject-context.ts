import { YProject } from '@/lib/y-project'
import { createContext, useContext } from 'react'

export const YProjectContext = createContext<{
  yproject: YProject
  version?: string
  setVersion: (version?: string) => void
} | null>(null)

export function useYProjectContext() {
  const context = useContext(YProjectContext)
  if (!context) {
    throw new Error('useYProject must be used within a YProjectProvider')
  }
  return context
}
