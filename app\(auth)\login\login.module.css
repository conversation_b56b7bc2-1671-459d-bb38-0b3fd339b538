.loginContainer {
  flex-grow: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 6vh;
  margin-bottom: 10vh;
  max-height: 100vh;
}

.loginFormContainer {
  width: 100%;
  max-width: 52rem;
  margin: 0;
  padding-left: 10rem;
}

.loginTitle {
  background-image: linear-gradient(90deg, #33b4ff 0%, #ffae00 100%);
  overflow: visible;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  height: 90px;
  width: 450px;
  margin-bottom: 5%;
  font-size: 64px;
  font-weight: 600;
  font-family: 'Public Sans', sans-serif;
  text-overflow: clip;
  white-space: nowrap;
  transform: translateX(-10%);
}

.loginForm {
  display: flex;
  flex-direction: column;
  gap: 1rem; /* 相当于 space-y-4 */
  margin-top: 2rem;
  width: 50%;
}

.loginFormFooter {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-direction: row-reverse;
}

.loginButton {
  background-size: cover;
  background-repeat: no-repeat;
  background-color: transparent;
  background-position: center;
  border: none;
  color: transparent;
  width: 117px;
  height: 50px;
  cursor: pointer;
}

.loginLink {
  color: #3b82f6; /* 相当于 text-blue-500 */
  text-decoration: underline;
}

.loginLink:hover {
  text-decoration: none;
}

/* 响应式设计 */
@media (max-width: 1000px) {
  .loginContainer {
    flex-direction: column;
    margin-top: 10vh;
    margin-bottom: 5vh;
  }

  .loginFormContainer {
    padding: 1rem;
    padding-left: 1rem;
  }

  .loginTitle {
    font-size: 48px;
    width: 100%;
    height: auto;
    margin-bottom: 2rem;
    transform: translateX(0);
  }

  .loginForm {
    width: 100%;
  }
}
