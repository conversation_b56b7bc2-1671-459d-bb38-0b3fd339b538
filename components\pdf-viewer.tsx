'use client'
import React, { useState, useEffect } from 'react'

interface PdfViewerProps {
  pdfData?: ArrayBuffer | null
  isLoading?: boolean
  className?: string
}

const PdfViewer: React.FC<PdfViewerProps> = ({
  pdfData = null,
  isLoading = false,
  className = '',
}) => {
  const [pdfUrl, setPdfUrl] = useState<string>('')

  useEffect(() => {
    if (pdfData) {
      // 创建PDF的Blob URL
      const blob = new Blob([pdfData], { type: 'application/pdf' })
      const url = URL.createObjectURL(blob)
      setPdfUrl(url)

      // 清理函数，组件卸载时释放URL
      return () => {
        URL.revokeObjectURL(url)
      }
    } else {
      setPdfUrl('')
    }
  }, [pdfData])

  const downloadPdf = () => {
    if (pdfUrl) {
      const link = document.createElement('a')
      link.href = pdfUrl
      link.download = 'document.pdf'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
  }

  return (
    <div className={`flex flex-col h-full ${className}`}>
      {/* 头部工具栏 */}
      <div className="bg-white border-b border-gray-200 p-4 flex justify-between items-center">
        <h3 className="text-lg font-semibold text-gray-800">PDF Preview</h3>
        {pdfUrl && (
          <button
            onClick={downloadPdf}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors text-sm font-medium"
          >
            Download PDF
          </button>
        )}
      </div>

      {/* 预览区域 */}
      <div className="flex-1 overflow-auto bg-gray-50 p-4">
        {isLoading ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
              <p className="text-gray-600">正在编译PDF...</p>
            </div>
          </div>
        ) : pdfUrl ? (
          <div className="h-full">
            <embed
              src={pdfUrl}
              type="application/pdf"
              className="w-full h-full border-0 rounded-lg shadow-lg"
              title="PDF Preview"
            />
          </div>
        ) : (
          <div className="flex items-center justify-center h-full">
            <div className="text-center text-gray-500">
              <div className="text-6xl mb-4">📄</div>
              <p className="text-lg font-medium mb-2">No PDF to preview</p>
              <p className="text-sm">
                Upload your LaTeX files and click &quot;Preview PDF&quot; to see
                the compiled document
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default PdfViewer
