// 模板转换相关常量
export const CONVERSION_STATUSES = {
  PENDING: 'pending' as const,
  PROCESSING: 'processing' as const,
  COMPLETED: 'completed' as const,
  FAILED: 'failed' as const,
  CANCELLED: 'cancelled' as const,
} as const

export const PROGRESS_STAGES = {
  PENDING: 'pending' as const,
  PROCESSING: 'processing' as const,
  COMPLETED: 'completed' as const,
  FAILED: 'failed' as const,
} as const

// 模板转换相关类型定义
export interface Template {
  id: string
  name: string
  description: string
  category: string
  fileHash?: string
  fileSize?: number
  fileName?: string
  version: string
  author: string
  license: string
  tags: string[]
  isActive?: boolean
  isPublic: boolean
  ownerID?: string
  usageCount: number
  createdAt?: string
  updatedAt?: string
  recommendedCompile?: string // 添加推荐编译方法字段
  // 添加后端返回的字段
  owner?: {
    id: string
    name?: string
    email?: string
  }
}

// 模板列表项（简化版）
export interface TemplateListItem {
  id: string
  name: string
  description: string
  category: string
  usageCount: number
  isPublic: boolean
  author?: string
  recommendedCompile?: string
  fileSize?: number
  version?: string
  createdAt?: string
  owner?: {
    id: string
    name?: string
  }
}

// ZIP文件信息
export interface ZipInfo {
  hasTexFiles: boolean
  totalFiles: number
  totalSize: number
}

// 上传模板响应
export interface UploadTemplateResponse {
  message: string
  template: Template
  zipInfo: ZipInfo
}

// 转换状态枚举
export type ConversionStatus =
  | 'pending'
  | 'processing'
  | 'in_progress' // 添加后端实际返回的状态
  | 'completed'
  | 'failed'
  | 'cancelled'

// 转换进度信息
export interface ConversionProgress {
  percentage: number
  stage: 'pending' | 'processing' | 'completed' | 'failed'
  message: string
  error?: string // 添加错误信息字段
}

// AI思路信息
export interface AIThoughts {
  canViewConversation: boolean
  conversationActive: boolean
  conversationId: string
  hasThoughts: boolean
  lastThought: string
  processingLog: string
  thoughtCount: number
  thoughts: string[]
}

// 转换任务
export interface Conversion {
  id: string
  taskName: string
  status: ConversionStatus
  sourceFileHash?: string
  sourceFileName: string
  sourceFileSize?: number
  resultFileHash?: string
  resultFileName?: string
  resultFileSize?: number
  targetTemplateID: string
  userID?: string
  difyConversationID?: string // 添加Dify对话ID
  errorMessage?: string // 添加错误信息
  createdAt: string
  updatedAt?: string
  completedAt?: string
  targetTemplate?: {
    id: string
    name: string
    fileHash: string
  }
}

// 转换任务列表项
export interface ConversionListItem {
  id: string
  taskName: string
  status: ConversionStatus
  sourceFileName: string
  sourceFileSize?: number
  resultFileSize?: number
  errorMessage?: string
  targetTemplate: {
    id: string
    name: string
  }
  createdAt: string
  updatedAt?: string
  completedAt?: string
}

// 转换状态响应
export interface ConversionStatusResponse {
  conversion: Conversion
  status: ConversionStatus
  progress: ConversionProgress
  aiThoughts?: AIThoughts
}

// 下载响应
export interface DownloadResponse {
  downloadUrl: string
  fileName: string
}

// 创建转换任务请求
export interface CreateConversionRequest {
  source: File
  taskName: string
  targetTemplateID: string
}

// 上传模板请求
export interface UploadTemplateRequest {
  template: File
  name: string
  description: string
  category: string
  version: string
  author: string
  license: string
  tags: string[]
  isPublic: boolean
}
