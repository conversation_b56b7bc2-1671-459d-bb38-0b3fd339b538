'use client'

import React, { useState } from 'react'
import { useForm, FormProvider } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { toast } from 'sonner'
import { FormInput } from '@/components/form/form-input'
import { Button } from '@/components/ui/button'
import { Mail, Lock, User } from 'lucide-react'
import {
  RegisterFormValues,
  registerSchema,
  registerUser,
} from '@/services/auth/register'
import { PROJECT_CODE } from '@/lib/constant'

export default function RegisterPage() {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const router = useRouter()

  const methods = useForm<RegisterFormValues>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      name: '',
      email: '',
      password: '',
      confirmPassword: '',
    },
  })

  const {
    handleSubmit,
    formState: { errors },
  } = methods

  const onSubmit = async (data: RegisterFormValues) => {
    setIsSubmitting(true)
    try {
      await registerUser(data)
      toast.success('Registration successful! Please verify your email.')
      router.push('/verify-email')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="flex items-center justify-center h-full overflow-auto bg-[#ffffff]">
      <div
        className="flex flex-col items-center justify-center bg-white p-8 rounded shadow-md"
        style={{ maxWidth: '33rem', width: '100%', marginTop: '5rem' }}
      >
        <h1
          className="text-4xl font-bold text-center mb-6 pt-2 lg:text-5xl
            bg-gradient-to-r from-[#33b4ff] to-[#ffae00] bg-clip-text text-transparent"
        >
          Welcome to {PROJECT_CODE}!
        </h1>
        <h2 className="text-md mb-6">Sign up for your account!</h2>

        <FormProvider {...methods}>
          <form
            onSubmit={handleSubmit(onSubmit)}
            noValidate
            autoComplete="off"
            className="w-full"
          >
            <FormInput
              label="Username"
              name="name"
              placeholder="Username"
              register={methods.register}
              errors={errors}
              icon={<User className="h-5 w-5" />}
              required
            />

            <FormInput
              label="Email"
              name="email"
              type="email"
              placeholder="Email"
              register={methods.register}
              errors={errors}
              icon={<Mail className="h-5 w-5" />}
              required
            />

            <FormInput
              label="Password"
              name="password"
              type="password"
              placeholder="Password"
              register={methods.register}
              errors={errors}
              icon={<Lock className="h-5 w-5" />}
              required
            />

            <FormInput
              label="Confirm Password"
              name="confirmPassword"
              type="password"
              placeholder="Confirm Password"
              register={methods.register}
              errors={errors}
              icon={<Lock className="h-5 w-5" />}
              required
            />

            <div className="my-4">
              Already have an account?{' '}
              <Link href="/login" className="text-[#33b4ff] hover:underline">
                Login Here
              </Link>
            </div>

            <Button
              type="submit"
              className="w-full bg-[#33b4ff] text-white py-2 rounded hover:bg-blue-600 transition-colors"
              disabled={isSubmitting}
              tag-id="register-button"
            >
              {isSubmitting ? 'Registering...' : 'Register'}
            </Button>
          </form>
        </FormProvider>
      </div>
    </div>
  )
}
