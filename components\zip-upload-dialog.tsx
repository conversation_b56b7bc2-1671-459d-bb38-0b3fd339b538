// components/ZipUploadDialog.tsx
'use client'

import { useState, useCallback } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Checkbox } from '@/components/ui/checkbox'

export default function ZipUploadDialog<T extends boolean = false>({
  open,
  setOpen,
  callback,
  multiple,
}: {
  open: boolean
  setOpen: (open: boolean) => void
  callback: (
    file: T extends true ? File[] : File,
    unwrapFirstFolder: boolean,
  ) => void
  multiple?: T
}) {
  const [isDragging, setIsDragging] = useState<boolean>(false)
  const [error, setError] = useState<string | null>(null)
  const [unwrapFirstFolder, setUnwrapFirstFolder] = useState<boolean>(true)

  const validateFiles = (files: File[]) => {
    for (const file of files) {
      if (!file.name.endsWith('.zip')) {
        setError(
          `Please upload valid .zip files. "${file.name}" is not a zip file.`,
        )
        return false
      }
    }
    return true
  }

  const handleFiles = useCallback(
    async (files: File[]) => {
      if (files.length === 0) {
        setError('No files selected')
        return
      }

      if (!validateFiles(files)) {
        return
      }

      setError(null)
      setOpen(false)

      if (multiple) {
        callback(files as T extends true ? File[] : File, unwrapFirstFolder)
      } else {
        const file = files[0]
        callback(file as T extends true ? File[] : File, unwrapFirstFolder)
      }
    },
    [unwrapFirstFolder, callback, setOpen, multiple],
  )

  const onDrop = useCallback(
    (e: React.DragEvent<HTMLDivElement>) => {
      e.preventDefault()
      setIsDragging(false)
      const files = Array.from(e.dataTransfer.files)
      handleFiles(files)
    },
    [handleFiles],
  )

  const onDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    setIsDragging(true)
  }, [])

  const onDragLeave = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    setIsDragging(false)
  }, [])

  const onFileInputChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const files = Array.from(e.target.files || [])
      handleFiles(files)
      e.target.value = ''
    },
    [handleFiles],
  )

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Upload ZIP File{multiple ? 's' : ''}</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div
            onDrop={onDrop}
            onDragOver={onDragOver}
            onDragLeave={onDragLeave}
            className={`border-2 border-dashed rounded-md p-6 text-center ${
              isDragging ? 'border-primary bg-primary/10' : 'border-gray-300'
            }`}
          >
            <input
              type="file"
              accept=".zip"
              onChange={onFileInputChange}
              className="hidden"
              id="zip-upload"
              multiple={multiple}
            />
            <label htmlFor="zip-upload" className="cursor-pointer">
              <p className="text-sm text-gray-500">
                Drag and drop your ZIP file{multiple ? 's' : ''} here or{' '}
                <span className="text-primary hover:underline">
                  click to select
                </span>
              </p>
            </label>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="unwrap-folder"
              checked={unwrapFirstFolder}
              onCheckedChange={checked => setUnwrapFirstFolder(!!checked)}
            />
            <label htmlFor="unwrap-folder" className="text-sm text-gray-600">
              Unwrap First Folder (if zip contains single folder)
            </label>
          </div>

          {error && <p className="text-sm text-red-500">{error}</p>}
        </div>
      </DialogContent>
    </Dialog>
  )
}
