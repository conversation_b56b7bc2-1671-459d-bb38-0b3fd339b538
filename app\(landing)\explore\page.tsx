'use client'

import React, { useEffect, ReactNode } from 'react'
import styles from './explore.module.css'
import { useRouter } from 'next/navigation'
import Image from 'next/image'
import logo from '@/public/assets/logo-icon.svg'
import { PROJECT_CODE } from '@/lib/constant'

interface ScrollToTopProps {
  children: ReactNode
}

const ScrollToTop = (props: ScrollToTopProps) => {
  useEffect(() => {
    window.scrollTo({ top: 0, left: 0, behavior: 'smooth' })
  }, [])

  return <>{props.children}</>
}

interface JobLogoProps {
  job: string
  company: string
  location: string
}

function JobLogo({ job, company, location }: JobLogoProps) {
  const router = useRouter()

  const handleClick = () => {
    router.push(`/job?job=${encodeURIComponent(job)}`)
  }

  return (
    <div
      onClick={handleClick}
      style={{ cursor: 'pointer' }}
      className={styles.jobCard}
    >
      <Image
        src={logo}
        alt="logo"
        width={160}
        height={160}
        className={styles.jobLogo}
      />
      <div className={styles.jobInfo}>
        <h3 className={styles.jobTitle}>{job}</h3>
        <div className={styles.jobCompany}>{company}</div>
        <div className={styles.jobLocation}>{location}</div>
      </div>
    </div>
  )
}

interface JobItem {
  title: string
  company: string
  location: string
}

const jobs: JobItem[] = [
  {
    title: 'AI engineer intern',
    company: PROJECT_CODE,
    location: 'Remote',
  },
  {
    title: 'Backend engineer intern',
    company: PROJECT_CODE,
    location: 'Remote',
  },
  {
    title: 'Full-stack engineer intern',
    company: PROJECT_CODE,
    location: 'Remote',
  },
  {
    title: 'C++ engineer intern',
    company: PROJECT_CODE,
    location: 'Remote',
  },
  {
    title: 'UI/UX design intern',
    company: PROJECT_CODE,
    location: 'Remote',
  },
]

function Career2() {
  return (
    <ScrollToTop>
      <section className={styles.mainContent}>
        <h1 className={styles.title}>Recently Posted Jobs</h1>
        <div className={styles.jobsList}>
          {jobs.map((job, index) => (
            <div key={index}>
              <JobLogo
                job={job.title}
                company={job.company}
                location={job.location}
              />
            </div>
          ))}
        </div>
      </section>
    </ScrollToTop>
  )
}

export default Career2
