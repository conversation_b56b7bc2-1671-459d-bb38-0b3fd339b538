import FileIndex, {
  FileNode,
  FolderNode,
  isFolderNode,
  isTextNode,
  TextNode,
} from './file-index'
import * as Y from 'yjs'
import { v4 as uuidv4 } from 'uuid'

export interface FileStats {
  name: string
  path: string
  type: 'file' | 'folder'
  uuid?: string
  size?: number
  children?: FileStats[]
}
/**
 * Handles file operations within a project
 * Provides methods for modifying the file structure,
 * file management, including creation, deletion, moving, and listing files.
 */
export default class FileHandler {
  private readonly projectIndex: FileIndex

  constructor(projectIndex: FileIndex) {
    this.projectIndex = projectIndex
  }

  // Path utilities
  public normalizePath(path: string): string {
    return path.split('/').filter(Boolean).join('/')
  }

  public getCurrentFolderPath(path: string): string {
    const segments = this.getPathSegments(path)
    let currentFolder = this.projectIndex.rootFolder
    let currentPath = ''

    for (const segment of segments) {
      const node = currentFolder.get(segment)
      if (!node || !isFolderNode(node)) break

      currentFolder = node as Y.Map<FileNode>
      currentPath = this.joinPath(currentPath, segment)
    }

    return currentPath
  }

  // File system operations
  public async listFiles(
    path: string,
    recursive = false,
  ): Promise<FileStats[]> {
    try {
      const folder = await this.getFolder(path)
      const results = await this.collectFileStats(folder, path, recursive)
      return this.sortFileStats(results)
    } catch (error) {
      console.error(`Error listing files at ${path}:`, error)
      return []
    }
  }

  public async exists(path: string): Promise<boolean> {
    try {
      const { parentPath, name } = this.splitPath(path)
      const folder = await this.getFolder(parentPath)
      return folder.has(name)
    } catch {
      return false
    }
  }

  public async createFolder(path: string): Promise<Y.Map<FileNode>> {
    const segments = this.getPathSegments(path)
    let currentFolder = this.projectIndex.rootFolder

    for (const segment of segments) {
      currentFolder = this.getOrCreateFolderNode(currentFolder, segment)
    }

    return currentFolder
  }

  public async createTextFile(path: string, content = ''): Promise<string> {
    await this.validateNewFile(path)

    const { parentPath, name } = this.splitPath(path)
    const targetFolder = await this.createFolder(parentPath)

    const uuid = uuidv4()
    const textNode: TextNode = { isText: true, uuid }

    targetFolder.set(name, textNode)
    this.createYTextDocument(uuid, content)

    return uuid
  }

  public async updateTextFile(path: string, content: string): Promise<boolean> {
    const ytext = await this.getYTextDocument(path)
    this.replaceTextContent(ytext, content)
    return true
  }

  public async move(oldPath: string, newPath: string): Promise<void> {
    if (oldPath === newPath) return

    await this.validateMove(oldPath, newPath)

    const { parentPath: oldParentPath, name: oldName } = this.splitPath(oldPath)
    const { parentPath: newParentPath, name: newName } = this.splitPath(newPath)

    const sourceFolder = await this.getFolder(oldParentPath)
    const node = this.getRequiredNode(sourceFolder, oldName, oldPath)

    const targetFolder = await this.createFolder(newParentPath)
    this.moveNode(sourceFolder, targetFolder, oldName, newName, node)
  }

  public async delete(path: string): Promise<void> {
    const { parentPath, name } = this.splitPath(path)
    const parentFolder = await this.getFolder(parentPath)

    if (!parentFolder.has(name)) {
      throw new Error(`File or folder not found at ${path}`)
    }

    parentFolder.delete(name)
  }

  public async deleteMultiple(paths: string[]): Promise<string[]> {
    const failedPaths: string[] = []

    await Promise.allSettled(
      paths.map(async path => {
        try {
          await this.delete(path)
        } catch (error) {
          console.error(`Failed to delete ${path}:`, error)
          failedPaths.push(path)
        }
      }),
    )

    return failedPaths
  }

  public async copy(sourcePath: string, targetPath: string): Promise<void> {
    await this.validateCopy(sourcePath, targetPath)

    const sourceNode = await this.getNode(sourcePath)
    if (!sourceNode) {
      throw new Error(`Source not found at ${sourcePath}`)
    }

    if (isTextNode(sourceNode)) {
      await this.copyTextFile(sourcePath, targetPath)
    } else if (isFolderNode(sourceNode)) {
      await this.copyFolder(sourcePath, targetPath, sourceNode)
    }
  }

  // Content operations
  public async getTextContent(path: string): Promise<string | undefined> {
    const node = await this.getNode(path)
    if (!node || !isTextNode(node)) return undefined

    const ytext = this.projectIndex.documentMap.get(node.uuid)
    return ytext?.toString()
  }

  public async getStats(path: string): Promise<FileStats | undefined> {
    try {
      const node = await this.getNode(path)
      if (!node) return undefined

      const { name } = this.splitPath(path)
      return this.createFileStats(node, name, path)
    } catch (error) {
      console.error(`Error getting stats for ${path}:`, error)
      return undefined
    }
  }

  // Private helper methods
  private getPathSegments(path: string): string[] {
    return path.split('/').filter(Boolean)
  }

  private joinPath(basePath: string, segment: string): string {
    return basePath ? `${basePath}/${segment}` : segment
  }

  private splitPath(path: string): { parentPath: string; name: string } {
    const segments = this.getPathSegments(path)
    const name = segments.pop()!
    const parentPath = segments.join('/')
    return { parentPath, name }
  }

  private getOrCreateFolderNode(
    parent: Y.Map<FileNode>,
    name: string,
  ): Y.Map<FileNode> {
    let node = parent.get(name)

    if (!node || !isFolderNode(node)) {
      const folderNode: FolderNode = new Y.Map<FileNode>()
      parent.set(name, folderNode)
      node = folderNode
    }

    return node as Y.Map<FileNode>
  }

  private async collectFileStats(
    folder: Y.Map<FileNode>,
    basePath: string,
    recursive: boolean,
  ): Promise<FileStats[]> {
    const results: FileStats[] = []

    for (const [name, node] of folder) {
      const nodePath = this.joinPath(basePath, name)

      if (isFolderNode(node)) {
        const folderStats = await this.createFolderStats(
          name,
          nodePath,
          recursive,
        )
        results.push(folderStats)
      } else if (isTextNode(node)) {
        const fileStats = this.createTextFileStats(name, nodePath, node)
        results.push(fileStats)
      }
    }

    return results
  }

  private async createFolderStats(
    name: string,
    path: string,
    recursive: boolean,
  ): Promise<FileStats> {
    const folderStats: FileStats = { name, path, type: 'folder' }

    if (recursive) {
      try {
        folderStats.children = await this.listFiles(path, true)
      } catch (err) {
        console.error(`Error listing subfolder ${path}:`, err)
      }
    }

    return folderStats
  }

  private createTextFileStats(
    name: string,
    path: string,
    node: TextNode,
  ): FileStats {
    const content = this.projectIndex.documentMap.get(node.uuid)
    return {
      name,
      path,
      type: 'file',
      uuid: node.uuid,
      size: content?.length || 0,
    }
  }

  private createFileStats(
    node: FileNode,
    name: string,
    path: string,
  ): FileStats {
    if (isTextNode(node)) {
      return this.createTextFileStats(name, path, node)
    } else if (isFolderNode(node)) {
      return { name, path, type: 'folder' }
    }

    throw new Error(`Unknown node type at ${path}`)
  }

  private sortFileStats(results: FileStats[]): FileStats[] {
    return results.sort((a, b) => {
      if (a.type !== b.type) {
        return a.type === 'folder' ? -1 : 1
      }
      return a.name.localeCompare(b.name)
    })
  }

  private createYTextDocument(uuid: string, content: string): void {
    const ytext = new Y.Text()
    if (content) {
      ytext.insert(0, content)
    }
    this.projectIndex.documentMap.set(uuid, ytext)
  }

  private async getYTextDocument(path: string): Promise<Y.Text> {
    const node = await this.getNode(path)
    if (!node || !isTextNode(node)) {
      throw new Error(`Text file not found at ${path}`)
    }

    const ytext = this.projectIndex.documentMap.get(node.uuid)
    if (!ytext) {
      throw new Error(`Document content not found for ${path}`)
    }

    return ytext
  }

  private replaceTextContent(ytext: Y.Text, content: string): void {
    ytext.delete(0, ytext.length)
    ytext.insert(0, content)
  }

  private getRequiredNode(
    folder: Y.Map<FileNode>,
    name: string,
    path: string,
  ): FileNode {
    const node = folder.get(name)
    if (!node) {
      throw new Error(`File or folder not found at ${path}`)
    }
    return node
  }

  private moveNode(
    sourceFolder: Y.Map<FileNode>,
    targetFolder: Y.Map<FileNode>,
    oldName: string,
    newName: string,
    node: FileNode,
  ): void {
    if (isFolderNode(node)) {
      targetFolder.set(newName, node.clone())
    } else {
      targetFolder.set(newName, node)
    }
    sourceFolder.delete(oldName)
  }

  private async copyTextFile(
    sourcePath: string,
    targetPath: string,
  ): Promise<void> {
    const content = (await this.getTextContent(sourcePath)) || ''
    await this.createTextFile(targetPath, content)
  }

  private async copyFolder(
    sourcePath: string,
    targetPath: string,
    sourceNode: Y.Map<FileNode>,
  ): Promise<void> {
    const { parentPath, name } = this.splitPath(targetPath)
    const targetFolder = await this.createFolder(parentPath)
    const newFolder = new Y.Map<FileNode>()
    targetFolder.set(name, newFolder)

    const copyPromises: Promise<void>[] = []

    for (const [childName] of sourceNode) {
      const sourceChildPath = `${sourcePath}/${childName}`
      const targetChildPath = `${targetPath}/${childName}`
      copyPromises.push(this.copy(sourceChildPath, targetChildPath))
    }

    await Promise.all(copyPromises)
  }

  private async validateNewFile(path: string): Promise<void> {
    if (await this.exists(path)) {
      throw new Error(`File already exists at ${path}`)
    }
  }

  private async validateMove(oldPath: string, newPath: string): Promise<void> {
    if (await this.exists(newPath)) {
      throw new Error(`Destination already exists at ${newPath}`)
    }
  }

  private async validateCopy(
    sourcePath: string,
    targetPath: string,
  ): Promise<void> {
    if (await this.exists(targetPath)) {
      throw new Error(`Destination already exists at ${targetPath}`)
    }
  }

  private isTexFile(path: string): boolean {
    return path.toLowerCase().endsWith('.tex')
  }

  private normalizeResultPath(path: string): string {
    return path.startsWith('/') ? path.slice(1) : path
  }

  private async getFolder(path: string): Promise<Y.Map<FileNode>> {
    if (!path) return this.projectIndex.rootFolder

    const segments = this.getPathSegments(path)
    let currentFolder = this.projectIndex.rootFolder

    for (const segment of segments) {
      const node = currentFolder.get(segment)
      if (!node || !isFolderNode(node)) {
        throw new Error(`Folder not found at ${path}`)
      }
      currentFolder = node as Y.Map<FileNode>
    }

    return currentFolder
  }

  private async getNode(path: string): Promise<FileNode | undefined> {
    const segments = this.getPathSegments(path)

    if (segments.length === 0) {
      return this.projectIndex.rootFolder
    }

    const { parentPath, name } = this.splitPath(path)

    try {
      const folder = await this.getFolder(parentPath)
      return folder.get(name)
    } catch {
      return undefined
    }
  }
}
