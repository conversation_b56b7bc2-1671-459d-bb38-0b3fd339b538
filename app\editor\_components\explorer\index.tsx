import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Tooltip } from '@/components/ui/tooltip'
import { NewFile, NewFolder, UploadFile } from '@/components/ar-icons'

import {
  expandToPath,
  setExplorerStore,
  useExplorerStore,
} from '@/stores/explorer-store'
import { useYProjectContext } from '@/lib/y-adapter/use-yproject-context'
import loadingToast from '@/lib/y-adapter/import-files-toast'

import ExplorerTree from './explorer-tree'
import ImportFilesDialog from './import-files'
import FileDrop from './file-drop'
import { FileEntry } from './types'

export default function Explorer() {
  const { yproject } = useYProjectContext()
  const [dialogOpen, setDialogOpen] = useState(false)

  const createNode = (type: 'file' | 'folder') => {
    const selectedPath = useExplorerStore.getState().selectedPath
    if (!selectedPath) return

    const folderPath = yproject.fileHandler.getCurrentFolderPath(selectedPath)
    setExplorerStore(state => {
      state.addingPath = [folderPath, type]
    })
    expandToPath(folderPath)
  }

  const handleImport = async (entries: FileEntry[]) => {
    await loadingToast(
      yproject.fileIndex.importFiles(
        entries.map(entry => ({
          path: entry.relativePath,
          file: entry.file,
        })),
      ),
    )
  }

  return (
    <div className="flex flex-col h-full">
      <div className="flex bg-[#EEEEEE] w-full p-2 gap-1">
        <Tooltip content="New File" asChild>
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 hover:bg-gray-200"
            onClick={() => createNode('file')}
            tag-id="create-new-file"
          >
            <NewFile className="h-6 w-6 text-gray-800" />
          </Button>
        </Tooltip>

        <Tooltip content="New Folder" asChild>
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 hover:bg-gray-200"
            onClick={() => createNode('folder')}
            tag-id="create-new-folder"
          >
            <NewFolder className="h-6 w-6 text-gray-800" />
          </Button>
        </Tooltip>

        <Tooltip content="Upload File" asChild>
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 hover:bg-gray-200"
            onClick={() => setDialogOpen(true)}
            tag-id="upload-file"
          >
            <UploadFile className="h-6 w-6 text-gray-800" />
          </Button>
        </Tooltip>
      </div>

      <FileDrop onDrop={handleImport} className="overflow-y-auto h-0 flex-1">
        <ExplorerTree />
      </FileDrop>

      <ImportFilesDialog
        open={dialogOpen}
        setOpen={setDialogOpen}
        onConfirm={handleImport}
      />
    </div>
  )
}
