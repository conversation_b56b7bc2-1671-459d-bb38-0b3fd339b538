/**
 * useYConfig hook
 * Provides a way to observe and update the YProject configuration in components.
 */
import { YProject } from '@/lib/y-project'
import {
  YProjectConfigInput,
  YProjectConfigOutput,
} from '@/lib/y-project/config-handler'
import { useCallback, useEffect, useState } from 'react'

export function useYConfig(yproject: YProject) {
  const [config, setConfig] = useState<YProjectConfigOutput>(
    yproject.configHandler.getConfig(),
  )

  useEffect(() => {
    const configHandler = yproject.configHandler
    const fileIndex = yproject.fileIndex
    const observeConfig = () => {
      const config = configHandler.getConfig()
      setConfig(config)
    }
    observeConfig()
    configHandler.configMap.observe(observeConfig)
    fileIndex.rootFolder.observeDeep(observeConfig)
    return () => {
      configHandler.configMap.unobserve(observeConfig)
      fileIndex.rootFolder.unobserveDeep(observeConfig)
    }
  }, [yproject])

  const _setConfig = useCallback(
    (newConfig: Partial<YProjectConfigInput>) => {
      const currentConfig = yproject.configHandler.getConfig()
      const config = {
        ...currentConfig,
        ...newConfig,
      }
      yproject.configHandler.setConfig(config)
    },
    [yproject],
  )

  return [config, _setConfig] as const
}
