import { YProject } from '../y-project'
import <PERSON><PERSON><PERSON><PERSON> from 'jszip'
import { OutputFileEntry } from '../y-project/file-index'

export default async function exportZip(
  projectId: string,
  fileName: string,
  version?: string,
) {
  const project = new YProject({ projectId })
  await project.initialSynced
  await project.versionHandler.switchVersion(version)

  const entries: OutputFileEntry[] = await project.fileIndex.exportFiles()
  const zip = new JSZip()
  for (const entry of entries) {
    zip.file(entry.path, entry.buffer as ArrayBuffer)
  }

  const zipBlob = await zip.generateAsync({ type: 'blob' })

  // Trigger download in the browser
  const a = document.createElement('a')
  const url = URL.createObjectURL(zipBlob)
  a.href = url
  a.download = fileName.endsWith('.zip') ? fileName : `${fileName}.zip`
  a.style.display = 'none'
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}
