'use client'
import { useGetMe } from '@/services/auth/get-me'
import { login } from '@/services/auth/login'
import { fetchGetMe } from '@/services/auth/get-me'
import { useEffect, useState } from 'react'

// Default test account for auto-login
const DEFAULT_TEST_ACCOUNT = {
  email: '<EMAIL>',
  password: '<EMAIL>',
}

export default function AutoLogin() {
  const { data: user, isLoading, refetch } = useGetMe()
  const [isAutoLogging, setIsAutoLogging] = useState(false)

  useEffect(() => {
    // Don't auto-login if already loading or already logged in
    if (isLoading || user || isAutoLogging) return

    // Auto-login with test account
    setIsAutoLogging(true)
    login(DEFAULT_TEST_ACCOUNT)
      .then(() => {
        return fetchGetMe()
      })
      .catch(err => {
        console.error('Auto-login failed', err)
      })
      .finally(() => {
        setIsAutoLogging(false)
      })
  }, [user, isLoading, isAutoLogging, refetch])

  return null
}
