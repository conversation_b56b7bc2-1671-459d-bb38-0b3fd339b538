'use client'
import React, { useState, useEffect } from 'react'
import { NavLinksSm, NavLinksLg } from './navigate-links'
import Link from 'next/link'
import Image from 'next/image'
import logo from '@/public/assets/logo-icon.svg'

const NavBar = () => {
  const [top, setTop] = useState(true)
  const [isOpen, setIsOpen] = useState(false)

  const handleClick = () => {
    setIsOpen(!isOpen)
  }

  useEffect(() => {
    // Ensure window is available (client-side only)
    if (typeof window !== 'undefined') {
      setTop(window.scrollY <= 10)

      const scrollHandler = () => {
        setTop(window.scrollY <= 10)
      }

      window.addEventListener('scroll', scrollHandler)
      return () => window.removeEventListener('scroll', scrollHandler)
    }
  }, [])

  return (
    <nav
      className={`fixed top-[32px] left-0 right-0 mx-auto w-fit z-30 transition duration-300 ease-in-out bg-arxOdd px-1 py-1 rounded-[500px] border border-[#eaeaea] shadow-md ${
        !top && 'bg-white shadow-lg'
      }`}
    >
      <div className="flex items-center">
        {/* Logo Section */}
        <div className="flex items-center flex-shrink-0" id="navlogo">
          <Link href="/#home" scroll={false}>
            <Image
              src={logo}
              alt="Logo"
              className="h-[2.2rem] w-[2.2rem]"
              width={35}
              height={35}
            />
          </Link>
        </div>

        {/* Navigation Links (Desktop) */}
        <div className="hidden lg:flex space-x-6">
          <NavLinksLg />
        </div>

        {/* Hamburger Button (Mobile) */}
        <button
          className="p-2 rounded-lg lg:hidden text-blue-900"
          onClick={handleClick}
          aria-label="Toggle navigation menu"
        >
          <svg
            className="h-6 w-6 fill-current"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
          >
            {isOpen ? (
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M18.278 16.864a1 1 0 0 1-1.414 1.414l-4.829-4.828-4.828 4.828a1 1 0 0 1-1.414-1.414l4.828-4.829-4.828-4.828a1 1 0 0 1 1.414-1.414l4.829 4.828 4.828-4.828a1 1 0 1 1 1.414 1.414l-4.828 4.829 4.828 4.828z"
              />
            ) : (
              <path
                fillRule="evenodd"
                d="M4 5h16a1 1 0 0 1 0 2H4a1 1 0 1 1 0-2zm0 6h16a1 1 0 0 1 0 2H4a1 1 0 1 1 0-2zm0 6h16a1 1 0 0 1 0 2H4a1 1 0 1 1 0-2z"
              />
            )}
          </svg>
        </button>

        {/* Mobile Navigation Menu */}
        {isOpen && (
          <div className="absolute left-1/2 top-14 transform -translate-x-1/2 bg-white shadow-xl rounded-md p-4 w-auto lg:hidden">
            <div className="flex flex-col space-y-6">
              <NavLinksSm onClick={() => setIsOpen(false)} />
            </div>
          </div>
        )}
      </div>
    </nav>
  )
}

export default NavBar
