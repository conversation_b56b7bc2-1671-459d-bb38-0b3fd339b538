import React from 'react'
import Link from 'next/link'
import { PROJECT_CODE } from '@/lib/constant'

interface NavItemProps {
  href: string
  children: React.ReactNode
  onClick?: () => void
  className?: string
}

const NavItem: React.FC<NavItemProps> = ({
  href,
  children,
  onClick,
  className = '',
}) => {
  return (
    <Link
      href={href}
      className={`flex justify-center text-[1.1rem] font-[600] text-gray-900 hover:text-blue-900 ${className}`}
      scroll={false}
      onClick={onClick}
    >
      {children}
    </Link>
  )
}

const GetStartedButton: React.FC<{ onClick?: () => void }> = ({ onClick }) => (
  <Link
    href="/login"
    className="flex justify-center items-center text-white bg-blue-500 hover:bg-blue-700 px-6 py-2 rounded-[36px] transition-all duration-300"
    scroll={false}
    onClick={onClick}
  >
    <span className="mr-2">Get</span> <span>Started</span>
  </Link>
)

const navigationItems =
  PROJECT_CODE === 'ArXtect'
    ? [
        { href: '/home', label: PROJECT_CODE, className: 'text-dark' },
        { href: '/projects', label: 'Latex Editor', splitLabel: true },
        { href: '/template', label: 'Template' },
        {
          href: '/template-converter',
          label: 'Template Converter',
          splitLabel: true,
        },
        { href: '/explore', label: 'Career' },
      ]
    : [
        { href: '/home', label: PROJECT_CODE, className: 'text-dark' },
        { href: '/projects', label: 'Latex Editor', splitLabel: true },
        { href: '/template', label: 'Template' },
        {
          href: '/template-converter',
          label: 'Template Converter',
          splitLabel: true,
        },
        { href: '/ai-scientist', label: 'AI-Scientist' },
      ]

const NavLinksLg = () => {
  return (
    <div className="flex justify-between items-center">
      <div className="flex items-center space-x-[46px]">
        {navigationItems.map((item, index) => (
          <NavItem key={index} href={item.href} className={item.className}>
            {item.splitLabel ? (
              <>
                <span className="mr-2">{item.label.split(' ')[0]}</span>
                <span>{item.label.split(' ')[1]}</span>
              </>
            ) : (
              item.label
            )}
          </NavItem>
        ))}
        <GetStartedButton />
      </div>
    </div>
  )
}

interface NavLinksSmProps {
  onClick?: () => void
}

const NavLinksSm: React.FC<NavLinksSmProps> = ({ onClick }) => {
  return (
    <>
      {navigationItems.map((item, index) => (
        <NavItem
          key={index}
          href={item.href}
          className={item.className}
          onClick={onClick}
        >
          {item.splitLabel ? (
            <>
              <span className="mr-2">{item.label.split(' ')[0]}</span>
              <span>{item.label.split(' ')[1]}</span>
            </>
          ) : (
            item.label
          )}
        </NavItem>
      ))}
      <GetStartedButton onClick={onClick} />
    </>
  )
}

export { NavLinksLg, NavLinksSm }
