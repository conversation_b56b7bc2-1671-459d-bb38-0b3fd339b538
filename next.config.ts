import type { NextConfig } from 'next'
import path from 'path'
import { requireEnvVar } from './lib/utils'

const nextConfig: NextConfig = {
  output: 'export',
  async rewrites() {
    return [
      {
        source: '/api/ws', // Match any API route
        destination: process.env.WS_URL
          ? process.env.WS_URL
          : requireEnvVar('API_URL') + '/api/ws', // Use WS_URL from environment variables
      },
      {
        source: '/api/proxy/oss/:path*', // Match any API route for OSS proxy
        destination: process.env.NEXT_PUBLIC_PROXY_OSS_URL
          ? process.env.NEXT_PUBLIC_PROXY_OSS_URL + '/:path*'
          : '/api/proxy/oss/:path*',
      },
      {
        source: '/api/:path*', // Match any API route
        destination: requireEnvVar('API_URL') + '/api/:path*',
      },
    ]
  },
  webpack: (config, { isServer }) => {
    if (!isServer) {
      // Ref: https://github.com/yjs/yjs/issues/438
      // Ensure that all imports of 'yjs' resolve to the same instance
      config.resolve.alias['yjs'] = path.resolve(__dirname, 'node_modules/yjs')
    }
    return config
  },
  images: {
    unoptimized: true,
  },
}

export default nextConfig
