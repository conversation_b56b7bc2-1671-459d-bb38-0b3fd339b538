import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { ColumnDef } from '@tanstack/react-table'
import { ArrowUpDown } from 'lucide-react'
import { Project } from '@/services/projects/get-projects'
import Link from 'next/link'
import { CellActions } from './cell-actions'

const projectTableColumns: ColumnDef<Project>[] = [
  {
    id: 'select',
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && 'indeterminate')
        }
        onCheckedChange={value => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={value => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'projectName',
    header: 'Title',
    cell: ({ row }) => {
      const projectId = row.original.id
      return (
        <Link href={`editor?project-id=${projectId}`}>
          <div className="max-w-[320px] overflow-ellipsis overflow-hidden whitespace-nowrap">
            {row.original.projectName}
          </div>
        </Link>
      )
    },
  },
  {
    accessorFn: row => row.owner.name,
    header: 'Owner',
  },
  {
    accessorKey: 'updatedAt',
    header: ({ column }) => {
      return (
        <div className="flex items-center space-x-2">
          <span>Last updated</span>
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            <ArrowUpDown className="h-4 w-4" />
          </Button>
        </div>
      )
    },
    cell: ({ row }) => {
      const date = new Date(row.original.updatedAt)
      return date.toLocaleString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      })
    },
  },
  {
    accessorKey: 'Actions',
    header: 'Actions',
    cell: ({ row }) => <CellActions row={row} />,
  },
]

export default projectTableColumns
