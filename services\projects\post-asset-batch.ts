import { fileToUint8Array, sha256 } from '@/lib/utils'
import api from '../api'

interface PrecheckResult {
  hash: string
  continue: boolean
  error: string
}

interface UploadResult {
  results: Array<{
    hash: string
    success: boolean
    error: string
  }>
}

interface FileEntry {
  hash: string
  size: number
  buffer: Uint8Array
}

const BATCH_SIZE = 20 * 1024 * 1024 // 20MB

export default async function postAssetBatch(projectId: string, files: Blob[]) {
  const preprocesses = await Promise.all(
    files.map(async file => {
      const buffer = await fileToUint8Array(file)
      const hash = await sha256(buffer)
      const size = buffer.length
      return {
        hash,
        size,
        buffer,
      }
    }),
  )

  const checks: PrecheckResult[] =
    (
      await api.post(
        `projects/${projectId}/assets/precheck-batch`,
        preprocesses.map(pre => ({
          hash: pre.hash,
          size: pre.size,
        })),
      )
    )?.data?.results || []

  const continueSet = new Set(checks.filter(c => c.continue).map(c => c.hash))
  const batches: FileEntry[][] = []

  preprocesses.forEach(pre => {
    if (continueSet.has(pre.hash)) {
      const lastBatch = batches[batches.length - 1]
      if (
        !lastBatch ||
        lastBatch.reduce((sum, f) => sum + f.size, 0) + pre.size > BATCH_SIZE
      ) {
        batches.push([pre])
      } else {
        lastBatch.push(pre)
      }
    }
  })

  const existedResults = checks
    .filter(c => !c.continue)
    .map(c => ({
      hash: c.hash,
      success: !c.error,
      error: c.error,
    }))

  const uploadResults = (
    await Promise.all(
      batches.map(async batch => {
        const formData = new FormData()
        batch.forEach(file => {
          const blob = new Blob([file.buffer], {
            type: 'application/octet-stream',
          })
          formData.append('files', blob, file.hash)
        })

        const response = await api.postForm<UploadResult>(
          `projects/${projectId}/assets/batch`,
          formData,
        )

        return response.data.results
      }),
    )
  ).flat()

  return [...existedResults, ...uploadResults]
}
