'use client'
import React from 'react'
import PdfViewer from '@/components/pdf-viewer'

interface RightPanelProps {
  pdfData?: ArrayBuffer | null
  isLoading?: boolean
}

const RightPanel: React.FC<RightPanelProps> = ({
  pdfData = null,
  isLoading = false,
}) => {
  return (
    <section className="overflow-hidden flex-1 bg-gray-100 max-md:w-full flex flex-col">
      <PdfViewer pdfData={pdfData} isLoading={isLoading} className="h-full" />
    </section>
  )
}

export default RightPanel
