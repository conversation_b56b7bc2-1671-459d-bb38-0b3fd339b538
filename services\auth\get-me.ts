import { useQuery } from '@tanstack/react-query'
import api from '../api'
import queryClient from '@/services/query-client'
import { User } from '../types'

export default async function getMe(): Promise<User> {
  const response = await api.get('users/me')
  return response.data.data.user as User
}

async function getMeWithErrorHandling(): Promise<User | null> {
  try {
    return await getMe()
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
  } catch (error) {
    return null
  }
}

export function useGetMe() {
  return useQuery(
    {
      queryKey: ['me'],
      queryFn: getMeWithErrorHandling,
      retry: 0,
    },
    queryClient,
  )
}

export function fetchGetMe() {
  return queryClient.fetchQuery({
    queryKey: ['me'],
    queryFn: getMeWithErrorHandling,
  })
}
